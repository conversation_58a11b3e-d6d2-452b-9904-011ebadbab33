import * as React from 'react';
import { shallow } from 'enzyme';
import to<PERSON>son from 'enzyme-to-json';

const getCampaignActivityRecipientStatusListMock = jest.fn();
jest.mock('../service', () => ({
  ...jest.requireActual<typeof import('../service')>('../service'),
  getCampaignActivityRecipientStatusList: getCampaignActivityRecipientStatusListMock
}));

import { CampaignActivityRecipientStatusType } from '../model';
import { SortOrder } from '../../../shared/ListingTable/models/ListingTable';

import { mockGetCampaignActivityRecipientStatusList } from '../stub';
import { entities, FILTER_OPERATORS } from '../../../../utils/constants';

import { CampaignActivityRecipientStatusList } from './CampaignActivityRecipientStatusList';
import ListingTable from '../../../shared/ListingTable/ListingTable';
import ListActions from '../../../shared/ListingTable/ListActions/ListActions';

describe('CampaignActivityRecipientStatusList component', () => {
  const props = {
    error: null,
    loading: false,
    history: { push: jest.fn() },
    match: { params: { id: '1' } },
    abortSignal: new AbortController().signal,
    data: mockGetCampaignActivityRecipientStatusList,
    page: { [entities.CAMPAIGN_ACTIVITY_RECIPIENT_STATUS]: 1 },
    filters: [{
      id: 'status',
      field: 'status',
      type: 'string',
      operator: FILTER_OPERATORS.equal.internalName,
      value: CampaignActivityRecipientStatusType.SENT
    }],
    fetchData: jest.fn(),
    updateSelectedPage: jest.fn()
  };

  const getCampaignActivityRecipientStatusListPromise = Promise.resolve({ data: mockGetCampaignActivityRecipientStatusList });
  getCampaignActivityRecipientStatusListMock.mockReturnValue(getCampaignActivityRecipientStatusListPromise);

  let wrapper;
  beforeEach(() => {
    jest.clearAllMocks();
    wrapper = shallow(<CampaignActivityRecipientStatusList {...props} />);
  });

  it('should render component', () => {
    return getCampaignActivityRecipientStatusListPromise.then(() => {
      // @ts-ignore
      expect(toJson(wrapper)).toMatchSnapshot();
    });
  });

  it('should call fetchData on refresh', () => {
    return getCampaignActivityRecipientStatusListPromise.then(() => {
      wrapper.find(ListActions).props().refreshData(2);
      wrapper.update();

      expect(props.updateSelectedPage).toHaveBeenCalledWith(entities.CAMPAIGN_ACTIVITY_RECIPIENT_STATUS, 2);
      expect(props.fetchData).toHaveBeenCalledWith({
        pageSize: 10,
        match: props.match,
        sortByField: 'sentAt',
        history: props.history,
        sortOrder: SortOrder.DESC,
        abortSignal: props.abortSignal,
        filter: CampaignActivityRecipientStatusType.ALL,
        page: { [entities.CAMPAIGN_ACTIVITY_RECIPIENT_STATUS]: 2 }
      });
    });
  });

  it('should call fetchData as per sorting', () => {
    return getCampaignActivityRecipientStatusListPromise.then(() => {
      wrapper.find(ListingTable).props().updateSortOrder('deliveredAt', SortOrder.ASC);
      wrapper.update();

      expect(props.fetchData).toHaveBeenCalledWith({
        pageSize: 10,
        match: props.match,
        history: props.history,
        sortOrder: SortOrder.ASC,
        sortByField: 'deliveredAt',
        abortSignal: props.abortSignal,
        filter: CampaignActivityRecipientStatusType.ALL,
        page: { [entities.CAMPAIGN_ACTIVITY_RECIPIENT_STATUS]: 1 }
      });
    });
  });

  it('should call fetchData with filter payload', () => {
    return getCampaignActivityRecipientStatusListPromise.then(() => {
      wrapper.find(ListingTable).props().updateSelectedFilter(CampaignActivityRecipientStatusType.DELIVERED);
      wrapper.update();

      expect(props.fetchData).toHaveBeenCalledWith({
        pageSize: 10,
        match: props.match,
        sortByField: 'sentAt',
        history: props.history,
        sortOrder: SortOrder.DESC,
        abortSignal: props.abortSignal,
        filter: CampaignActivityRecipientStatusType.DELIVERED,
        page: { [entities.CAMPAIGN_ACTIVITY_RECIPIENT_STATUS]: 1 }
      });
    });
  });

  it('should call fetchData on page size change', () => {
    return getCampaignActivityRecipientStatusListPromise.then(() => {
      wrapper.find(ListingTable).props().setPageSize(30);
      wrapper.update();

      expect(props.fetchData).toHaveBeenCalledWith({
        pageSize: 30,
        match: props.match,
        sortByField: 'sentAt',
        history: props.history,
        sortOrder: SortOrder.DESC,
        abortSignal: props.abortSignal,
        filter: CampaignActivityRecipientStatusType.ALL,
        page: { [entities.CAMPAIGN_ACTIVITY_RECIPIENT_STATUS]: 1 }
      });
    });
  });
});

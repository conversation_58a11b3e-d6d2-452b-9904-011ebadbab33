import * as React from 'react';
import { connect } from 'react-redux';

import { StateInterface } from '../../../../store/store';
import { SortOrder } from '../../../shared/ListingTable/models/ListingTable';
import { CampaignActivityRecipientStatusListContent, CampaignActivityRecipientStatusType } from '../model';

import { entities } from '../../../../utils/constants';
import { ErrorObj } from '../../../../utils/ErrorUtil';
import { isBlank } from '../../../../utils/globalUtil';
import { capitalizeLabel, routeToEntity } from '../../../../utils/entityUtils';

import { getCampaignActivityRecipientStatusList } from '../service';
import { updateSelectedPageAction } from '../../listLayout/actions/ListActions';
import { getCampaignActivityRecipientStatusTableHeaders } from './TableHeaders';

import { TableLoadingSkeleton } from '../../../shared/Table/TableLoadingSkeleton';
import { WithApiStateHandler } from '../../DataManagement/Components/Import/WithApiCall';
import SalesLayout from '../../layout/SalesLayout/SalesLayout';
import ListingTable from '../../../shared/ListingTable/ListingTable';
import ApiStateHandler from '../../../shared/ApiHandler/ApiStateHandler';
import ListActions from '../../../shared/ListingTable/ListActions/ListActions';
import withAbortController from '../../../shared/AbortController/withAbortController';
import TableErrorFallback from '../../../shared/ListingTable/components/TableErrorFallback/TableErrorFallback';

import './_campaign-activity-recipient-status-list.scss';

interface StoreProps {
  page: { [entity: string]: number };
}

interface DispatchProps {
  updateSelectedPage: (entity: string, page: number) => void;
}

interface OwnProps {
  history: any;
  error: ErrorObj;
  loading: boolean;
  abortSignal: AbortSignal;
  match: { params: { id: string }};
  data: CampaignActivityRecipientStatusListContent;
  fetchData: (params) => void;
}

type Props = StoreProps & DispatchProps & OwnProps;

export const CampaignActivityRecipientStatusList:React.FC<Props> = ({
  data,
  page,
  match,
  error,
  history,
  loading,
  fetchData,
  abortSignal,
  updateSelectedPage
}) => {
  const [pageSize, setPageSize] = React.useState<number>(10);
  const [sortByField, setSortByField] = React.useState<string>('sentAt');
  const [sortOrder, setSortOrder] = React.useState<SortOrder>(SortOrder.DESC);
  const [filter, setFilter] = React.useState<CampaignActivityRecipientStatusType>(CampaignActivityRecipientStatusType.ALL);
  const [campaignActivityName, setCampaignActivityName] = React.useState<string>(`${capitalizeLabel(routeToEntity(entities.CAMPAIGNS))} Activity`);

  React.useEffect(() => {
    const query: URLSearchParams = new URLSearchParams(history.location.search);

    if(query.has('name')) {
      setCampaignActivityName(query.get('name'));
    }
  },              []);

  const fetchCampaignActivityRecipientStatus = (pageNumber: number, field?: string, order?: SortOrder, filterValue?: CampaignActivityRecipientStatusType, size?: number) => {
    pageNumber && updateSelectedPage(entities.CAMPAIGN_ACTIVITY_RECIPIENT_STATUS, pageNumber);
    const selectedPage = { [entities.CAMPAIGN_ACTIVITY_RECIPIENT_STATUS]: pageNumber || page[entities.CAMPAIGN_ACTIVITY_RECIPIENT_STATUS] };

    const selectedSortField = !isBlank(field) ? field : sortByField;
    const selectedSortOrder = !isBlank(order) ? order : sortOrder;
    const selectedFilter = !isBlank(filterValue) ? filterValue : filter;
    const selectedPageSize = !isBlank(size) ? size : pageSize;

    fetchData({ match, history, abortSignal, filter: selectedFilter, sortByField: selectedSortField, sortOrder: selectedSortOrder, page: selectedPage, pageSize: selectedPageSize });
  };

  const updateCurrentPage = (pageNumber: number) => fetchCampaignActivityRecipientStatus(pageNumber);

  const updateSortOrder = (field: string, order: SortOrder) => {
    setSortByField(field);
    setSortOrder(order);

    fetchCampaignActivityRecipientStatus(page[entities.CAMPAIGN_ACTIVITY_RECIPIENT_STATUS], field, order);
  };

  return (
    <SalesLayout>
      <div className="main-content-wrapper position-relative campaign-recipient-status__listing">
        <div className="page-header mb-2">
          <div className="page-title-wrapper">
            <div className="page-title">
              <h1 className="h1">Recipient Status for '{campaignActivityName}'</h1>
            </div>
          </div>

          <ListActions
            headers={[]}
            history={history}
            currentUserId={0}
            filterColumns={[]}
            showFilterIcon={false}
            isFilterUpdated={false}
            onClickAdd={() => null}
            profileBasedAbility={null}
            refreshData={updateCurrentPage}
            totalItems={data && data.totalElements}
            sort={{ order: sortOrder, field: sortByField }}
            fetchData={fetchCampaignActivityRecipientStatus}
            entity={entities.CAMPAIGN_ACTIVITY_RECIPIENT_STATUS}
            currentPage={page[entities.CAMPAIGN_ACTIVITY_RECIPIENT_STATUS] || 1}
          />
        </div>

        <div className="page-content flex-column">
          <ApiStateHandler
            error={error}
            history={history}
            loading={loading}
            SkeletonComponent={TableLoadingSkeleton}
            ErrorFallbackComponent={TableErrorFallback}
          >
            <ListingTable
              data={data}
              history={history}
              sortOrder={sortOrder}
              onClickRow={() => null}
              selectedFilter={filter}
              sortByField={sortByField}
              rowClickRequiredPermission={[]}
              updateSortOrder={updateSortOrder}
              updateCurrentPage={updateCurrentPage}
              entity={entities.CAMPAIGN_ACTIVITY_RECIPIENT_STATUS}
              headers={getCampaignActivityRecipientStatusTableHeaders()}
              allTableColumns={getCampaignActivityRecipientStatusTableHeaders()}
              currentPage={page[entities.CAMPAIGN_ACTIVITY_RECIPIENT_STATUS] || 1}
              setPageSize={(size: number) => {
                setPageSize(size);
                fetchCampaignActivityRecipientStatus(1, sortByField, sortOrder, filter, size);
              }}
              updateSelectedFilter={(val) => {
                setFilter(val);
                fetchCampaignActivityRecipientStatus(1, sortByField, sortOrder, val);
              }}
            />
          </ApiStateHandler>
        </div>
      </div>
    </SalesLayout>
  );
};

const mapStateToProps = (state: StateInterface) => ({
  page: state.listLayout.page
});

const service = (params) => {
  const {
    page,
    history,
    pageSize,
    abortSignal,
    match: { params: { id } },
    sortByField = 'sentAt',
    sortOrder = SortOrder.DESC,
    filter = CampaignActivityRecipientStatusType.ALL
  } = params;

  return getCampaignActivityRecipientStatusList(
    id,
    filter,
    sortByField,
    sortOrder,
    page[entities.CAMPAIGN_ACTIVITY_RECIPIENT_STATUS],
    pageSize,
    history,
    abortSignal
  );
};

export default connect(mapStateToProps, {
  updateSelectedPage: updateSelectedPageAction
})(withAbortController(WithApiStateHandler(CampaignActivityRecipientStatusList, service)));

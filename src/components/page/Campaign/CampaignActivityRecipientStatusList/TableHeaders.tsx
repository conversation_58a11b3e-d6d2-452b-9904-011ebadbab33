import * as React from 'react';

import { FieldTypes } from '../../FieldSettings/models/Field';
import { CampaignActivityRecipientStatusType } from '../model';
import { Header } from '../../../shared/ListingTable/models/ListingTable';

import { capitalizeLabel } from '../../../../utils/entityUtils';
import { CampaignActivityRecipientStatusColors } from '../constants';

import EntityLabel from '../../../shared/EntityLabel/EntityLabel';

export const getCampaignActivityRecipientStatusTableHeaders = (): Header[] => [
  {
    id: 'entityId',
    header: 'Entity ID',
    isStandard: true,
    isSortable: false,
    isFilterable: false,
    fieldType: FieldTypes.NUMBER
  },
  {
    id: 'entityType',
    header: 'Entity Type',
    isFilterable: false,
    isSortable: false,
    fieldType: FieldTypes.PICK_LIST,
    isStandard: true,
    picklist: {
      picklistValues: [
        { id: 'CONTACT', name: 'CONTACT', displayName: 'Contact' },
        { id: 'LEAD', name: 'LEAD', displayName: 'Lead' }
      ]
    },
    formattedValue: ({ entityType }) => <EntityLabel str={`{${entityType}}`} entities={[entityType]} />
  },
  {
    id: 'recipientName',
    header: 'Recipient Name',
    isFilterable: false,
    isSortable: false,
    fieldType: FieldTypes.TEXT_FIELD,
    isStandard: true
  },
  {
    id: 'phoneNumber',
    header: 'Phone Number',
    isFilterable: false,
    isSortable: false,
    fieldType: FieldTypes.TEXT_FIELD,
    isStandard: true
  },
  {
    id: 'status',
    header: 'Status',
    isFilterable: true,
    isSortable: false,
    fieldType: FieldTypes.PICK_LIST,
    isStandard: true,
    picklist: {
      picklistValues: [
        { id: CampaignActivityRecipientStatusType.ALL, name: CampaignActivityRecipientStatusType.ALL, displayName: 'All' },
        { id: CampaignActivityRecipientStatusType.SENT, name: CampaignActivityRecipientStatusType.SENT, displayName: 'Sent' },
        { id: CampaignActivityRecipientStatusType.READ, name: CampaignActivityRecipientStatusType.READ, displayName: 'Read' },
        { id: CampaignActivityRecipientStatusType.DELIVERED, name: CampaignActivityRecipientStatusType.DELIVERED, displayName: 'Delivered' },
        { id: CampaignActivityRecipientStatusType.FAILED, name: CampaignActivityRecipientStatusType.FAILED, displayName: 'Failed' },
      ]
    },
    formattedValue: ({ status }) => (
      <span
        className="recipient-status"
        style={{ color: CampaignActivityRecipientStatusColors[status].color, backgroundColor: CampaignActivityRecipientStatusColors[status].backgroundColor }}
      >
        {capitalizeLabel(status)}
      </span>
    )
  },
  {
    id: 'sentAt',
    header: 'Sent At',
    isFilterable: false,
    isSortable: true,
    fieldType: FieldTypes.DATETIME_PICKER,
    isStandard: true
  },
  {
    id: 'deliveredAt',
    header: 'Delivered At',
    isFilterable: false,
    isSortable: true,
    fieldType: FieldTypes.DATETIME_PICKER,
    isStandard: true
  },
  {
    id: 'readAt',
    header: 'Read At',
    isFilterable: false,
    isSortable: true,
    fieldType: FieldTypes.DATETIME_PICKER,
    isStandard: true
  },
  {
    id: 'failedAt',
    header: 'Failed At',
    isFilterable: false,
    isSortable: true,
    fieldType: FieldTypes.DATETIME_PICKER,
    isStandard: true
  },
  {
    id: 'errorMessage',
    header: 'Error Message',
    isFilterable: false,
    isSortable: false,
    fieldType: FieldTypes.TEXT_FIELD,
    isStandard: true
  }
];

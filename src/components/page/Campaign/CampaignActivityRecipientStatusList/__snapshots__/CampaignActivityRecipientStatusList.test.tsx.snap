// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CampaignActivityRecipientStatusList component should render component 1`] = `
<Connect(withRouter(SalesLayout))>
  <div
    className="main-content-wrapper position-relative campaign-recipient-status__listing"
  >
    <div
      className="page-header mb-2"
    >
      <div
        className="page-title-wrapper"
      >
        <div
          className="page-title"
        >
          <h1
            className="h1"
          >
            Recipient Status for '
            Campaign Activity
            '
          </h1>
        </div>
      </div>
      <ListActions
        currentPage={1}
        currentUserId={0}
        entity="campaign-activity-recipient-status"
        fetchData={[Function]}
        filterColumns={Array []}
        headers={Array []}
        history={
          Object {
            "push": [MockFunction],
          }
        }
        isFilterUpdated={false}
        onClickAdd={[Function]}
        profileBasedAbility={null}
        refreshData={[Function]}
        showFilterIcon={false}
        sort={
          Object {
            "field": "sentAt",
            "order": "desc",
          }
        }
        totalItems={2}
      />
    </div>
    <div
      className="page-content flex-column"
    >
      <withRouter(ApiStateHandler)
        ErrorFallbackComponent={[Function]}
        SkeletonComponent={[Function]}
        error={null}
        history={
          Object {
            "push": [MockFunction],
          }
        }
        loading={false}
      >
        <Connect(ListingTable)
          allTableColumns={
            Array [
              Object {
                "fieldType": "NUMBER",
                "header": "Entity ID",
                "id": "entityId",
                "isFilterable": false,
                "isSortable": false,
                "isStandard": true,
              },
              Object {
                "fieldType": "PICK_LIST",
                "formattedValue": [Function],
                "header": "Entity Type",
                "id": "entityType",
                "isFilterable": false,
                "isSortable": false,
                "isStandard": true,
                "picklist": Object {
                  "picklistValues": Array [
                    Object {
                      "displayName": "Contact",
                      "id": "CONTACT",
                      "name": "CONTACT",
                    },
                    Object {
                      "displayName": "Lead",
                      "id": "LEAD",
                      "name": "LEAD",
                    },
                  ],
                },
              },
              Object {
                "fieldType": "TEXT_FIELD",
                "header": "Recipient Name",
                "id": "recipientName",
                "isFilterable": false,
                "isSortable": false,
                "isStandard": true,
              },
              Object {
                "fieldType": "TEXT_FIELD",
                "header": "Phone Number",
                "id": "phoneNumber",
                "isFilterable": false,
                "isSortable": false,
                "isStandard": true,
              },
              Object {
                "fieldType": "PICK_LIST",
                "formattedValue": [Function],
                "header": "Status",
                "id": "status",
                "isFilterable": true,
                "isSortable": false,
                "isStandard": true,
                "picklist": Object {
                  "picklistValues": Array [
                    Object {
                      "displayName": "All",
                      "id": "ALL",
                      "name": "ALL",
                    },
                    Object {
                      "displayName": "Sent",
                      "id": "SENT",
                      "name": "SENT",
                    },
                    Object {
                      "displayName": "Read",
                      "id": "READ",
                      "name": "READ",
                    },
                    Object {
                      "displayName": "Delivered",
                      "id": "DELIVERED",
                      "name": "DELIVERED",
                    },
                    Object {
                      "displayName": "Failed",
                      "id": "FAILED",
                      "name": "FAILED",
                    },
                  ],
                },
              },
              Object {
                "fieldType": "DATETIME_PICKER",
                "header": "Sent At",
                "id": "sentAt",
                "isFilterable": false,
                "isSortable": true,
                "isStandard": true,
              },
              Object {
                "fieldType": "DATETIME_PICKER",
                "header": "Delivered At",
                "id": "deliveredAt",
                "isFilterable": false,
                "isSortable": true,
                "isStandard": true,
              },
              Object {
                "fieldType": "DATETIME_PICKER",
                "header": "Read At",
                "id": "readAt",
                "isFilterable": false,
                "isSortable": true,
                "isStandard": true,
              },
              Object {
                "fieldType": "DATETIME_PICKER",
                "header": "Failed At",
                "id": "failedAt",
                "isFilterable": false,
                "isSortable": true,
                "isStandard": true,
              },
              Object {
                "fieldType": "TEXT_FIELD",
                "header": "Error Message",
                "id": "errorMessage",
                "isFilterable": false,
                "isSortable": false,
                "isStandard": true,
              },
            ]
          }
          currentPage={1}
          data={
            Object {
              "content": Array [
                Object {
                  "deliveredAt": null,
                  "entityId": 1,
                  "entityType": "CONTACT",
                  "errorMessage": null,
                  "failedAt": null,
                  "phoneNumber": "+919763812991",
                  "readAt": null,
                  "recipientName": "New Contact",
                  "sentAt": "2025-07-09T07:36:42.000Z",
                  "status": "SENT",
                },
                Object {
                  "deliveredAt": "2025-07-09T07:40:15.000Z",
                  "entityId": 2,
                  "entityType": "LEAD",
                  "errorMessage": null,
                  "failedAt": null,
                  "phoneNumber": "+919763812991",
                  "readAt": null,
                  "recipientName": "My New Lead",
                  "sentAt": "2025-07-09T07:39:07.000Z",
                  "status": "DELIVERED",
                },
              ],
              "first": true,
              "last": false,
              "number": 0,
              "numberOfElements": 2,
              "size": 10,
              "totalElements": 2,
              "totalPages": 1,
            }
          }
          entity="campaign-activity-recipient-status"
          headers={
            Array [
              Object {
                "fieldType": "NUMBER",
                "header": "Entity ID",
                "id": "entityId",
                "isFilterable": false,
                "isSortable": false,
                "isStandard": true,
              },
              Object {
                "fieldType": "PICK_LIST",
                "formattedValue": [Function],
                "header": "Entity Type",
                "id": "entityType",
                "isFilterable": false,
                "isSortable": false,
                "isStandard": true,
                "picklist": Object {
                  "picklistValues": Array [
                    Object {
                      "displayName": "Contact",
                      "id": "CONTACT",
                      "name": "CONTACT",
                    },
                    Object {
                      "displayName": "Lead",
                      "id": "LEAD",
                      "name": "LEAD",
                    },
                  ],
                },
              },
              Object {
                "fieldType": "TEXT_FIELD",
                "header": "Recipient Name",
                "id": "recipientName",
                "isFilterable": false,
                "isSortable": false,
                "isStandard": true,
              },
              Object {
                "fieldType": "TEXT_FIELD",
                "header": "Phone Number",
                "id": "phoneNumber",
                "isFilterable": false,
                "isSortable": false,
                "isStandard": true,
              },
              Object {
                "fieldType": "PICK_LIST",
                "formattedValue": [Function],
                "header": "Status",
                "id": "status",
                "isFilterable": true,
                "isSortable": false,
                "isStandard": true,
                "picklist": Object {
                  "picklistValues": Array [
                    Object {
                      "displayName": "All",
                      "id": "ALL",
                      "name": "ALL",
                    },
                    Object {
                      "displayName": "Sent",
                      "id": "SENT",
                      "name": "SENT",
                    },
                    Object {
                      "displayName": "Read",
                      "id": "READ",
                      "name": "READ",
                    },
                    Object {
                      "displayName": "Delivered",
                      "id": "DELIVERED",
                      "name": "DELIVERED",
                    },
                    Object {
                      "displayName": "Failed",
                      "id": "FAILED",
                      "name": "FAILED",
                    },
                  ],
                },
              },
              Object {
                "fieldType": "DATETIME_PICKER",
                "header": "Sent At",
                "id": "sentAt",
                "isFilterable": false,
                "isSortable": true,
                "isStandard": true,
              },
              Object {
                "fieldType": "DATETIME_PICKER",
                "header": "Delivered At",
                "id": "deliveredAt",
                "isFilterable": false,
                "isSortable": true,
                "isStandard": true,
              },
              Object {
                "fieldType": "DATETIME_PICKER",
                "header": "Read At",
                "id": "readAt",
                "isFilterable": false,
                "isSortable": true,
                "isStandard": true,
              },
              Object {
                "fieldType": "DATETIME_PICKER",
                "header": "Failed At",
                "id": "failedAt",
                "isFilterable": false,
                "isSortable": true,
                "isStandard": true,
              },
              Object {
                "fieldType": "TEXT_FIELD",
                "header": "Error Message",
                "id": "errorMessage",
                "isFilterable": false,
                "isSortable": false,
                "isStandard": true,
              },
            ]
          }
          history={
            Object {
              "push": [MockFunction],
            }
          }
          onClickRow={[Function]}
          rowClickRequiredPermission={Array []}
          selectedFilter="ALL"
          setPageSize={[Function]}
          sortByField="sentAt"
          sortOrder="desc"
          updateCurrentPage={[Function]}
          updateSelectedFilter={[Function]}
          updateSortOrder={[Function]}
        />
      </withRouter(ApiStateHandler)>
    </div>
  </div>
</Connect(withRouter(SalesLayout))>
`;

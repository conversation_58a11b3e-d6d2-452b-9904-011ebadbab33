import { FieldTypes } from '../FieldSettings/models/Field';
import { EntityFormAction } from '../formLayout/models/Form';
import { CampaignActivityStatus, CampaignAnalyticsType, CampaignStatus, CampaignActivityRecipientStatusType, PHONE_NUMBER_TYPE } from './model';

import { entities } from '../../../utils/constants';

export const CampaignTabs = [
  { displayName: 'Campaigns', route: `/sales/${entities.CAMPAIGNS}/list` },
  { displayName: 'Activities', route: `/sales/${entities.CAMPAIGNS}/activities/list` }
];

export const CampaignStatusColors = {
  [CampaignStatus.DRAFT]: { color: '#006DEE', backgroundColor: '#C6DEFF' },
  [CampaignStatus.IN_PROGRESS]: { color: '#B67C00', backgroundColor: '#FEF6E5' },
  [CampaignStatus.PAUSED]: { color: '#EE0000', backgroundColor: '#FFECEC' },
  [CampaignStatus.COMPLETED]: { color: '#28A645', backgroundColor: '#ECFFF1' }
};

export const CampaignActivityStatusColors = {
  [CampaignActivityStatus.DRAFT]: { color: '#006DEE', backgroundColor: '#C6DEFF' },
  [CampaignActivityStatus.FAILED]: { color: '#EE0000', backgroundColor: '#FFECEC' },
  [CampaignActivityStatus.QUEUED]: { color: '#B67C00', backgroundColor: '#FEF6E5' },
  [CampaignActivityStatus.IN_PROGRESS]: { color: '#B67C00', backgroundColor: '#FEF6E5' },
  [CampaignActivityStatus.PAUSED]: { color: '#EE0000', backgroundColor: '#FFECEC' },
  [CampaignActivityStatus.PROCESSED]: { color: '#28A645', backgroundColor: '#ECFFF1' },
  [CampaignActivityStatus.COMPLETED]: { color: '#28A645', backgroundColor: '#ECFFF1' }
};

export const CampaignCreateEditLayout = {
  id: 1,
  active: true,
  default: true,
  layoutActions: [],
  entity: 'CAMPAIGN',
  systemDefault: true,
  name: 'createCampaign',
  showOnlyImportantField: true,
  layoutHeader: { label: null },
  mode: EntityFormAction.CREATE,
  displayName: 'Create Campaign Layout',
  layoutItems: [
    {
      id: 1,
      row: 1,
      width: 4,
      column: 1,
      type: 'SECTION',
      layoutItems: [
        {
          id: 1,
          row: 1,
          column: 1,
          width: 12,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'name',
            displayName: 'Campaign Name',
            type: FieldTypes.TEXT_FIELD,
            id: 1,
            min: 3,
            max: 255,
            sectionId: 1,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: true,
            standard: true,
            lessThan: null,
            sortable: false,
            readOnly: false,
            internal: false,
            lookupUrl: null,
            filterable: true,
            important: false,
            description: null,
            multiValue: false,
            greaterThan: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false
          }
        },
        {
          id: 2,
          row: 2,
          column: 1,
          width: 12,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'description',
            displayName: 'Description',
            type: FieldTypes.PARAGRAPH_TEXT,
            id: 2,
            min: 0,
            max: 2550,
            sectionId: 1,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: false,
            standard: true,
            lessThan: null,
            sortable: false,
            readOnly: false,
            internal: false,
            lookupUrl: null,
            important: false,
            filterable: false,
            description: null,
            multiValue: false,
            greaterThan: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false
          }
        },
        {
          id: 3,
          row: 3,
          column: 1,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'estimatedBudget',
            displayName: 'Estimated Budget',
            type: FieldTypes.MONEY,
            id: 3,
            sectionId: 1,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: true,
            standard: true,
            lessThan: null,
            sortable: false,
            readOnly: false,
            internal: false,
            lookupUrl: null,
            filterable: true,
            important: false,
            description: null,
            multiValue: false,
            greaterThan: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false
          }
        },
        {
          id: 4,
          row: 3,
          column: 2,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'actualExpense',
            displayName: 'Actual Expense',
            type: FieldTypes.MONEY,
            id: 4,
            sectionId: 1,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: true,
            standard: true,
            lessThan: null,
            sortable: false,
            readOnly: true,
            internal: false,
            lookupUrl: null,
            filterable: true,
            important: false,
            description: null,
            multiValue: false,
            greaterThan: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false
          }
        },
        {
          id: 5,
          row: 4,
          column: 1,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'startDate',
            displayName: 'Start Date',
            type: FieldTypes.DATETIME_PICKER,
            id: 5,
            sectionId: 1,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: true,
            standard: true,
            lessThan: null,
            sortable: true,
            readOnly: false,
            internal: false,
            lookupUrl: null,
            filterable: true,
            important: false,
            description: null,
            multiValue: false,
            greaterThan: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false
          }
        },
        {
          id: 6,
          row: 4,
          column: 2,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'endDate',
            displayName: 'End Date',
            type: FieldTypes.DATETIME_PICKER,
            id: 6,
            sectionId: 1,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            standard: true,
            lessThan: null,
            sortable: true,
            required: false,
            readOnly: false,
            internal: false,
            lookupUrl: null,
            filterable: true,
            important: false,
            description: null,
            multiValue: false,
            greaterThan: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false
          }
        }
      ],
      item: {
        id: 1,
        collapsible: false,
        name: 'generalInformation',
        heading: 'General Information',
        description: 'Add campaign basics like name, budget, and schedule to get started'
      }
    },
    {
      id: 2,
      row: 2,
      width: 4,
      column: 1,
      type: 'SECTION',
      layoutItems: [
        {
          id: 1,
          row: 1,
          column: 1,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'utmCampaign',
            displayName: 'UTM Campaign',
            type: FieldTypes.TEXT_FIELD,
            id: 1,
            min: 0,
            max: 255,
            sectionId: 2,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: true,
            standard: true,
            lessThan: null,
            sortable: false,
            readOnly: false,
            internal: false,
            lookupUrl: null,
            filterable: true,
            important: false,
            description: null,
            multiValue: false,
            greaterThan: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false,
            placeholder: 'e.g. summer_sale, product_launch, leadgen2025'
          }
        },
        {
          id: 2,
          row: 1,
          column: 2,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'utmSource',
            displayName: 'UTM Source',
            type: FieldTypes.TEXT_FIELD,
            id: 2,
            min: 0,
            max: 255,
            sectionId: 2,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: true,
            standard: true,
            lessThan: null,
            sortable: false,
            readOnly: false,
            internal: false,
            lookupUrl: null,
            important: false,
            filterable: true,
            description: null,
            multiValue: false,
            greaterThan: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false,
            placeholder: 'e.g. google, facebook, newsletter, linkedin'
          }
        },
        {
          id: 3,
          row: 2,
          column: 1,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'utmMedium',
            displayName: 'UTM Medium',
            type: FieldTypes.TEXT_FIELD,
            id: 3,
            min: 0,
            max: 255,
            sectionId: 2,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: true,
            standard: true,
            lessThan: null,
            sortable: false,
            readOnly: false,
            internal: false,
            lookupUrl: null,
            important: false,
            filterable: true,
            description: null,
            multiValue: false,
            greaterThan: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false,
            placeholder: 'e.g. cpc, email, social, referral'
          }
        },
        {
          id: 4,
          row: 2,
          column: 2,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'utmContent',
            displayName: 'UTM Content',
            type: FieldTypes.TEXT_FIELD,
            id: 4,
            min: 0,
            max: 255,
            sectionId: 2,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            standard: true,
            lessThan: null,
            required: false,
            sortable: false,
            readOnly: false,
            internal: false,
            lookupUrl: null,
            important: false,
            filterable: true,
            description: null,
            multiValue: false,
            greaterThan: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false,
            placeholder: 'e.g. cta_top, image_ad, button_v1'
          }
        },
        {
          id: 5,
          row: 3,
          column: 1,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'utmTerm',
            displayName: 'UTM Term',
            type: FieldTypes.TEXT_FIELD,
            id: 5,
            min: 0,
            max: 255,
            sectionId: 2,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            standard: true,
            lessThan: null,
            required: false,
            sortable: false,
            readOnly: false,
            internal: false,
            lookupUrl: null,
            important: false,
            filterable: true,
            description: null,
            multiValue: false,
            greaterThan: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false,
            placeholder: 'e.g. crm+software, inventory+tool'
          }
        }
      ],
      item: {
        id: 2,
        collapsible: false,
        name: 'utmInformation',
        heading: 'UTM Information',
        description: 'Enter UTM details to track campaign performance across sources'
      }
    }
  ]
};

export const CampaignDetailsLayout = {
  id: 1,
  active: true,
  default: true,
  layoutActions: [],
  entity: 'CAMPAIGN',
  systemDefault: true,
  name: 'createCampaign',
  showOnlyImportantField: true,
  layoutHeader: { label: null },
  mode: 'DETAILS',
  displayName: 'Details Campaign Layout',
  layoutItems: [
    {
      id: 1,
      row: 1,
      width: 4,
      column: 1,
      type: 'SECTION',
      layoutItems: [
        {
          id: 1,
          row: 1,
          column: 1,
          width: 12,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'name',
            displayName: 'Campaign Name',
            type: FieldTypes.TEXT_FIELD,
            id: 1,
            min: 3,
            max: 255,
            sectionId: 1,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: true,
            standard: true,
            lessThan: null,
            sortable: false,
            readOnly: false,
            internal: false,
            lookupUrl: null,
            filterable: true,
            important: false,
            description: null,
            multiValue: false,
            greaterThan: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false
          }
        },
        {
          id: 2,
          row: 2,
          column: 1,
          width: 12,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'description',
            displayName: 'Description',
            type: FieldTypes.PARAGRAPH_TEXT,
            id: 2,
            min: 0,
            max: 2550,
            sectionId: 1,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: false,
            standard: true,
            lessThan: null,
            sortable: false,
            readOnly: false,
            internal: false,
            lookupUrl: null,
            important: false,
            filterable: false,
            description: null,
            multiValue: false,
            greaterThan: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false
          }
        },
        {
          id: 3,
          row: 3,
          column: 1,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'estimatedBudget',
            displayName: 'Estimated Budget',
            type: FieldTypes.MONEY,
            id: 3,
            sectionId: 1,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: true,
            standard: true,
            lessThan: null,
            sortable: false,
            readOnly: false,
            internal: false,
            lookupUrl: null,
            filterable: true,
            important: false,
            description: null,
            multiValue: false,
            greaterThan: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false
          }
        },
        {
          id: 4,
          row: 3,
          column: 2,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'actualExpense',
            displayName: 'Actual Expense',
            type: FieldTypes.MONEY,
            id: 4,
            sectionId: 1,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: true,
            standard: true,
            lessThan: null,
            sortable: false,
            readOnly: true,
            internal: false,
            lookupUrl: null,
            filterable: true,
            important: false,
            description: null,
            multiValue: false,
            greaterThan: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false
          }
        },
        {
          id: 5,
          row: 4,
          column: 1,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'startDate',
            displayName: 'Start Date',
            type: FieldTypes.DATETIME_PICKER,
            id: 5,
            sectionId: 1,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: true,
            standard: true,
            lessThan: null,
            sortable: true,
            readOnly: false,
            internal: false,
            lookupUrl: null,
            filterable: true,
            important: false,
            description: null,
            multiValue: false,
            greaterThan: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false
          }
        },
        {
          id: 6,
          row: 4,
          column: 2,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'endDate',
            displayName: 'End Date',
            type: FieldTypes.DATETIME_PICKER,
            id: 6,
            sectionId: 1,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            standard: true,
            lessThan: null,
            sortable: true,
            required: false,
            readOnly: false,
            internal: false,
            lookupUrl: null,
            filterable: true,
            important: false,
            description: null,
            multiValue: false,
            greaterThan: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false
          }
        }
      ],
      item: {
        id: 1,
        collapsible: false,
        name: 'generalInformation',
        heading: 'General Information',
        description: 'Add campaign basics like name, budget, and schedule to get started'
      }
    },
    {
      id: 2,
      row: 2,
      width: 4,
      column: 1,
      type: 'SECTION',
      layoutItems: [
        {
          id: 1,
          row: 1,
          column: 1,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'utmCampaign',
            displayName: 'UTM Campaign',
            type: FieldTypes.TEXT_FIELD,
            id: 1,
            min: 0,
            max: 255,
            sectionId: 2,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: true,
            standard: true,
            lessThan: null,
            sortable: false,
            readOnly: false,
            internal: false,
            lookupUrl: null,
            filterable: true,
            important: false,
            description: null,
            multiValue: false,
            greaterThan: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false,
            placeholder: 'e.g. summer_sale, product_launch, leadgen2025'
          }
        },
        {
          id: 2,
          row: 1,
          column: 2,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'utmSource',
            displayName: 'UTM Source',
            type: FieldTypes.TEXT_FIELD,
            id: 2,
            min: 0,
            max: 255,
            sectionId: 2,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: true,
            standard: true,
            lessThan: null,
            sortable: false,
            readOnly: false,
            internal: false,
            lookupUrl: null,
            important: false,
            filterable: true,
            description: null,
            multiValue: false,
            greaterThan: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false,
            placeholder: 'e.g. google, facebook, newsletter, linkedin'
          }
        },
        {
          id: 3,
          row: 2,
          column: 1,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'utmMedium',
            displayName: 'UTM Medium',
            type: FieldTypes.TEXT_FIELD,
            id: 3,
            min: 0,
            max: 255,
            sectionId: 2,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: true,
            standard: true,
            lessThan: null,
            sortable: false,
            readOnly: false,
            internal: false,
            lookupUrl: null,
            important: false,
            filterable: true,
            description: null,
            multiValue: false,
            greaterThan: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false,
            placeholder: 'e.g. cpc, email, social, referral'
          }
        },
        {
          id: 4,
          row: 2,
          column: 2,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'utmContent',
            displayName: 'UTM Content',
            type: FieldTypes.TEXT_FIELD,
            id: 4,
            min: 0,
            max: 255,
            sectionId: 2,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            standard: true,
            lessThan: null,
            required: false,
            sortable: false,
            readOnly: false,
            internal: false,
            lookupUrl: null,
            important: false,
            filterable: true,
            description: null,
            multiValue: false,
            greaterThan: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false,
            placeholder: 'e.g. cta_top, image_ad, button_v1'
          }
        },
        {
          id: 5,
          row: 3,
          column: 1,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'utmTerm',
            displayName: 'UTM Term',
            type: FieldTypes.TEXT_FIELD,
            id: 5,
            min: 0,
            max: 255,
            sectionId: 2,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            standard: true,
            lessThan: null,
            required: false,
            sortable: false,
            readOnly: false,
            internal: false,
            lookupUrl: null,
            important: false,
            filterable: true,
            description: null,
            multiValue: false,
            greaterThan: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false,
            placeholder: 'e.g. crm+software, inventory+tool'
          }
        }
      ],
      item: {
        id: 2,
        collapsible: false,
        name: 'utmInformation',
        heading: 'UTM Information',
        description: 'Enter UTM details to track campaign performance across sources'
      }
    },
    {
      id: 3,
      row: 3,
      width: 4,
      column: 1,
      type: 'SECTION',
      layoutItems: [
        {
          id: 1,
          row: 1,
          column: 1,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'createdAt',
            displayName: 'Created At',
            type: FieldTypes.DATETIME_PICKER,
            id: 1,
            sectionId: 3,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            lessThan: null,
            internal: true,
            standard: false,
            required: false,
            sortable: false,
            readOnly: false,
            lookupUrl: null,
            filterable: true,
            important: false,
            description: null,
            multiValue: false,
            greaterThan: null,
            placeholder: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false
          }
        },
        {
          id: 2,
          row: 1,
          column: 2,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'createdBy',
            displayName: 'Created By',
            type: FieldTypes.LOOK_UP,
            id: 2,
            sectionId: 3,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: false,
            standard: false,
            lessThan: null,
            internal: true,
            sortable: false,
            readOnly: false,
            lookupUrl: null,
            filterable: true,
            important: false,
            description: null,
            multiValue: false,
            greaterThan: null,
            placeholder: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false
          }
        },
        {
          id: 3,
          row: 2,
          column: 1,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'updatedAt',
            displayName: 'Updated At',
            type: FieldTypes.DATETIME_PICKER,
            id: 3,
            sectionId: 3,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: false,
            standard: false,
            lessThan: null,
            internal: true,
            sortable: false,
            readOnly: false,
            lookupUrl: null,
            filterable: true,
            important: false,
            description: null,
            multiValue: false,
            greaterThan: null,
            placeholder: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false
          }
        },
        {
          id: 4,
          row: 2,
          column: 2,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'updatedBy',
            displayName: 'Updated By',
            type: FieldTypes.LOOK_UP,
            id: 4,
            sectionId: 3,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: false,
            standard: false,
            lessThan: null,
            internal: true,
            sortable: false,
            readOnly: false,
            lookupUrl: null,
            filterable: true,
            important: false,
            description: null,
            multiValue: false,
            greaterThan: null,
            placeholder: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false
          }
        },
        {
          id: 5,
          row: 3,
          column: 1,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'startedAt',
            displayName: 'Started At',
            type: FieldTypes.DATETIME_PICKER,
            id: 5,
            sectionId: 3,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: false,
            standard: false,
            lessThan: null,
            internal: true,
            sortable: false,
            readOnly: false,
            lookupUrl: null,
            filterable: true,
            important: false,
            description: null,
            multiValue: false,
            greaterThan: null,
            placeholder: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false
          }
        },
        {
          id: 6,
          row: 3,
          column: 2,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'startedBy',
            displayName: 'Started By',
            type: FieldTypes.LOOK_UP,
            id: 6,
            sectionId: 3,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: false,
            standard: false,
            lessThan: null,
            internal: true,
            sortable: false,
            readOnly: false,
            lookupUrl: null,
            filterable: true,
            important: false,
            description: null,
            multiValue: false,
            greaterThan: null,
            placeholder: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false
          }
        },
        {
          id: 7,
          row: 4,
          column: 1,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'lastPausedAt',
            displayName: 'Last Paused At',
            type: FieldTypes.DATETIME_PICKER,
            id: 7,
            sectionId: 3,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: false,
            standard: false,
            lessThan: null,
            internal: true,
            sortable: false,
            readOnly: false,
            lookupUrl: null,
            filterable: true,
            important: false,
            description: null,
            multiValue: false,
            greaterThan: null,
            placeholder: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false
          }
        },
        {
          id: 8,
          row: 4,
          column: 2,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'lastPausedBy',
            displayName: 'Last Paused By',
            type: FieldTypes.LOOK_UP,
            id: 8,
            sectionId: 3,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: false,
            standard: false,
            lessThan: null,
            internal: true,
            sortable: false,
            readOnly: false,
            lookupUrl: null,
            filterable: true,
            important: false,
            description: null,
            multiValue: false,
            greaterThan: null,
            placeholder: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false
          }
        },
        {
          id: 9,
          row: 5,
          column: 1,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'lastResumedAt',
            displayName: 'Last Resumed At',
            type: FieldTypes.DATETIME_PICKER,
            id: 9,
            sectionId: 3,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: false,
            standard: false,
            lessThan: null,
            internal: true,
            sortable: false,
            readOnly: false,
            lookupUrl: null,
            filterable: true,
            important: false,
            description: null,
            multiValue: false,
            greaterThan: null,
            placeholder: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false
          }
        },
        {
          id: 10,
          row: 5,
          column: 2,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'lastResumedBy',
            displayName: 'Last Resumed By',
            type: FieldTypes.LOOK_UP,
            id: 10,
            sectionId: 3,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: false,
            standard: false,
            lessThan: null,
            internal: true,
            sortable: false,
            readOnly: false,
            lookupUrl: null,
            filterable: true,
            important: false,
            description: null,
            multiValue: false,
            greaterThan: null,
            placeholder: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false
          }
        },
        {
          id: 11,
          row: 6,
          column: 1,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'endedAt',
            displayName: 'Ended At',
            type: FieldTypes.DATETIME_PICKER,
            id: 11,
            sectionId: 3,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: false,
            standard: false,
            lessThan: null,
            internal: true,
            sortable: false,
            readOnly: false,
            lookupUrl: null,
            filterable: true,
            important: false,
            description: null,
            multiValue: false,
            greaterThan: null,
            placeholder: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false
          }
        },
        {
          id: 12,
          row: 6,
          column: 2,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'endedBy',
            displayName: 'Ended By',
            type: FieldTypes.LOOK_UP,
            id: 12,
            sectionId: 3,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: false,
            standard: false,
            lessThan: null,
            internal: true,
            sortable: false,
            readOnly: false,
            lookupUrl: null,
            filterable: true,
            important: false,
            description: null,
            multiValue: false,
            greaterThan: null,
            placeholder: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false
          }
        },
        {
          id: 13,
          row: 7,
          column: 1,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'id',
            displayName: 'ID',
            type: FieldTypes.NUMBER,
            id: 13,
            sectionId: 3,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            lessThan: null,
            internal: true,
            standard: false,
            required: false,
            sortable: false,
            readOnly: false,
            lookupUrl: null,
            filterable: true,
            important: false,
            description: null,
            multiValue: false,
            greaterThan: null,
            placeholder: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false
          }
        },
        {
          id: 14,
          row: 8,
          column: 2,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'status',
            displayName: 'Status',
            type: FieldTypes.PICK_LIST,
            id: 13,
            sectionId: 3,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            lessThan: null,
            internal: true,
            standard: false,
            required: false,
            sortable: false,
            readOnly: false,
            lookupUrl: null,
            filterable: true,
            important: false,
            description: null,
            multiValue: false,
            greaterThan: null,
            placeholder: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false
          }
        }
      ],
      item: {
        id: 3,
        name: 'internals',
        description: null,
        collapsible: false,
        heading: 'Internals'
      }
    }
  ]
};

export const CampaignActivityCreateEditLayout = {
  id: 1,
  active: true,
  default: true,
  layoutActions: [],
  systemDefault: true,
  entity: 'CAMPAIGN_ACTIVITY',
  name: 'createCampaignActivity',
  showOnlyImportantField: true,
  layoutHeader: { label: null },
  mode: EntityFormAction.CREATE,
  displayName: 'Create Campaign Activity Layout',
  layoutItems: [
    {
      id: 1,
      row: 1,
      width: 4,
      column: 1,
      type: 'SECTION',
      layoutItems: [
        {
          id: 1,
          row: 1,
          column: 1,
          width: 12,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'name',
            displayName: 'Activity Name',
            type: FieldTypes.TEXT_FIELD,
            id: 1,
            min: 3,
            max: 255,
            sectionId: 1,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: true,
            standard: true,
            lessThan: null,
            sortable: false,
            readOnly: false,
            internal: false,
            lookupUrl: null,
            filterable: true,
            important: false,
            description: null,
            multiValue: false,
            greaterThan: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false
          }
        },
        {
          id: 2,
          row: 2,
          column: 1,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'campaign',
            displayName: 'Campaign Name',
            type: FieldTypes.LOOK_UP,
            id: 2,
            sectionId: 1,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: true,
            standard: true,
            lessThan: null,
            sortable: false,
            readOnly: false,
            internal: false,
            pickLists: null,
            important: false,
            filterable: true,
            description: null,
            multiValue: false,
            greaterThan: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: true,
            lookupUrl: `/${entities.CAMPAIGNS}/lookup?excludeStatus=${CampaignStatus.COMPLETED}&q=name:`
          }
        },
        {
          id: 3,
          row: 2,
          column: 2,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'type',
            displayName: 'Activity Type',
            type: FieldTypes.PICK_LIST,
            id: 3,
            sectionId: 1,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: true,
            standard: true,
            lessThan: null,
            sortable: false,
            readOnly: false,
            internal: false,
            lookupUrl: null,
            important: false,
            filterable: true,
            description: null,
            multiValue: false,
            greaterThan: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false,
            pickLists: [
              {
                id: 'WHATSAPP',
                name: 'WHATSAPP',
                displayName: 'WhatsApp',
                disabled: false,
                systemDefault: true
              }
            ]
          }
        },
        {
          id: 4,
          row: 3,
          column: 1,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'estimatedBudget',
            displayName: 'Estimated Budget',
            type: FieldTypes.MONEY,
            id: 4,
            sectionId: 1,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: true,
            standard: true,
            lessThan: null,
            sortable: false,
            readOnly: false,
            internal: false,
            lookupUrl: null,
            filterable: true,
            important: false,
            description: null,
            multiValue: false,
            greaterThan: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false
          }
        },
        {
          id: 5,
          row: 3,
          column: 2,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'actualExpense',
            displayName: 'Actual Expense',
            type: FieldTypes.MONEY,
            id: 5,
            sectionId: 1,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: true,
            standard: true,
            lessThan: null,
            sortable: false,
            readOnly: true,
            internal: false,
            lookupUrl: null,
            filterable: true,
            important: false,
            description: null,
            multiValue: false,
            greaterThan: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false
          }
        },
        {
          id: 6,
          row: 3,
          column: 1,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'startDate',
            displayName: 'Start Date',
            type: FieldTypes.DATETIME_PICKER,
            id: 6,
            sectionId: 1,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: true,
            standard: true,
            lessThan: null,
            sortable: true,
            readOnly: false,
            internal: false,
            lookupUrl: null,
            filterable: true,
            important: false,
            description: null,
            multiValue: false,
            greaterThan: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false
          }
        },
        {
          id: 6,
          row: 3,
          column: 2,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'endDate',
            displayName: 'End Date',
            type: FieldTypes.DATETIME_PICKER,
            id: 7,
            sectionId: 1,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            standard: true,
            lessThan: null,
            sortable: true,
            required: false,
            readOnly: false,
            internal: false,
            lookupUrl: null,
            filterable: true,
            important: false,
            description: null,
            multiValue: false,
            greaterThan: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false
          }
        }
      ],
      item: {
        id: 1,
        collapsible: false,
        name: 'activityDetails',
        heading: 'Activity Details',
        description: 'Add a name, select type, and define budget for your campaign activity'
      }
    },
    {
      id: 2,
      row: 2,
      width: 4,
      column: 1,
      type: 'SECTION',
      layoutItems: [
        {
          id: 1,
          row: 1,
          column: 2,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'utmCampaign',
            displayName: 'UTM Campaign',
            type: FieldTypes.TEXT_FIELD,
            id: 1,
            min: 0,
            max: 255,
            sectionId: 2,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: true,
            standard: true,
            lessThan: null,
            sortable: false,
            readOnly: false,
            internal: false,
            lookupUrl: null,
            filterable: true,
            important: false,
            description: null,
            multiValue: false,
            greaterThan: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false,
            placeholder: 'e.g. summer_sale, product_launch, leadgen2025'
          }
        },
        {
          id: 2,
          row: 1,
          column: 2,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'utmSource',
            displayName: 'UTM Source',
            type: FieldTypes.TEXT_FIELD,
            id: 2,
            min: 0,
            max: 255,
            sectionId: 2,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: true,
            standard: true,
            lessThan: null,
            sortable: false,
            readOnly: false,
            internal: false,
            lookupUrl: null,
            important: false,
            filterable: true,
            description: null,
            multiValue: false,
            greaterThan: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false,
            placeholder: 'e.g. google, facebook, newsletter, linkedin'
          }
        },
        {
          id: 3,
          row: 2,
          column: 1,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'utmMedium',
            displayName: 'UTM Medium',
            type: FieldTypes.TEXT_FIELD,
            id: 3,
            min: 0,
            max: 255,
            sectionId: 2,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: true,
            standard: true,
            lessThan: null,
            sortable: false,
            readOnly: false,
            internal: false,
            lookupUrl: null,
            important: false,
            filterable: true,
            description: null,
            multiValue: false,
            greaterThan: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false,
            placeholder: 'e.g. cpc, email, social, referral'
          }
        },
        {
          id: 4,
          row: 2,
          column: 2,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'utmContent',
            displayName: 'UTM Content',
            type: FieldTypes.TEXT_FIELD,
            id: 4,
            min: 0,
            max: 255,
            sectionId: 2,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            standard: true,
            lessThan: null,
            required: false,
            sortable: false,
            readOnly: false,
            internal: false,
            lookupUrl: null,
            important: false,
            filterable: true,
            description: null,
            multiValue: false,
            greaterThan: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false,
            placeholder: 'e.g. cta_top, image_ad, button_v1'
          }
        },
        {
          id: 5,
          row: 3,
          column: 1,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'utmTerm',
            displayName: 'UTM Term',
            type: FieldTypes.TEXT_FIELD,
            id: 5,
            min: 0,
            max: 255,
            sectionId: 2,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            standard: true,
            lessThan: null,
            required: false,
            sortable: false,
            readOnly: false,
            internal: false,
            lookupUrl: null,
            important: false,
            filterable: true,
            description: null,
            multiValue: false,
            greaterThan: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false,
            placeholder: 'e.g. crm+software, inventory+tool'
          }
        }
      ],
      item: {
        id: 2,
        collapsible: false,
        name: 'utmInformation',
        heading: 'UTM Information',
        description: 'Enter UTM details to track campaign activity performance across sources'
      }
    },
    {
      id: 3,
      row: 3,
      width: 4,
      column: 1,
      type: 'SECTION',
      layoutItems: [
        {
          id: 1,
          row: 1,
          column: 1,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'entity',
            displayName: 'Entity',
            type: FieldTypes.PICK_LIST,
            id: 1,
            sectionId: 3,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: true,
            standard: true,
            lessThan: null,
            sortable: false,
            readOnly: false,
            internal: false,
            lookupUrl: null,
            filterable: true,
            important: false,
            description: null,
            multiValue: false,
            greaterThan: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false,
            pickLists: [
              {
                id: 'LEAD',
                name: 'LEAD',
                displayName: 'Lead',
                disabled: false,
                systemDefault: true
              },
              {
                id: 'CONTACT',
                name: 'CONTACT',
                displayName: 'Contact',
                disabled: false,
                systemDefault: true
              }
            ]
          }
        },
        {
          id: 2,
          row: 1,
          column: 2,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'selectedSmartlist',
            displayName: 'Smartlist',
            type: FieldTypes.PICK_LIST,
            id: 2,
            sectionId: 3,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: false,
            standard: true,
            lessThan: null,
            sortable: false,
            readOnly: false,
            internal: false,
            lookupUrl: null,
            important: false,
            filterable: true,
            description: null,
            multiValue: false,
            greaterThan: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false
          }
        },
        {
          id: 3,
          row: 2,
          column: 1,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'sentTo',
            displayName: 'Sent To',
            type: FieldTypes.PICK_LIST,
            id: 3,
            sectionId: 3,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: true,
            standard: true,
            lessThan: null,
            sortable: false,
            readOnly: false,
            internal: false,
            lookupUrl: null,
            important: false,
            filterable: true,
            description: null,
            multiValue: false,
            greaterThan: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false,
            pickLists: [
              {
                id: PHONE_NUMBER_TYPE.PRIMARY_PHONE_NUMBER,
                name: PHONE_NUMBER_TYPE.PRIMARY_PHONE_NUMBER,
                displayName: 'Primary Phone number',
                disabled: false,
                systemDefault: true
              },
              {
                id: PHONE_NUMBER_TYPE.ALL_PHONE_NUMBERS,
                name: PHONE_NUMBER_TYPE.ALL_PHONE_NUMBERS,
                displayName: 'All Available Phone numbers',
                disabled: false,
                systemDefault: true
              }
            ]
          }
        },
        {
          id: 4,
          row: 3,
          column: 1,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'filters',
            displayName: 'Filters',
            type: FieldTypes.PICK_LIST,
            id: 4,
            sectionId: 3,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            standard: true,
            lessThan: null,
            required: false,
            sortable: false,
            readOnly: false,
            internal: false,
            lookupUrl: null,
            important: false,
            filterable: true,
            description: null,
            multiValue: false,
            greaterThan: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false
          }
        }
      ],
      item: {
        id: 3,
        collapsible: false,
        name: 'recipients',
        heading: 'Recipients',
        description: 'Choose who will receive this activity leads, contacts, or custom lists'
      }
    },
    {
      id: 4,
      row: 4,
      width: 4,
      column: 1,
      type: 'SECTION',
      layoutItems: [
        {
          id: 1,
          row: 1,
          column: 1,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'connectedAccount',
            displayName: 'Whatsapp Account',
            type: FieldTypes.LOOK_UP,
            id: 1,
            sectionId: 4,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: true,
            standard: true,
            lessThan: null,
            sortable: false,
            readOnly: false,
            internal: false,
            filterable: true,
            important: false,
            description: null,
            multiValue: false,
            greaterThan: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: true,
            lookupUrl: '/messages/connected-accounts/lookup?q='
          }
        },
        {
          id: 2,
          row: 1,
          column: 2,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'whatsappTemplate',
            displayName: 'Template Name',
            type: FieldTypes.LOOK_UP,
            id: 2,
            sectionId: 4,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: true,
            standard: true,
            lessThan: null,
            sortable: false,
            readOnly: false,
            internal: false,
            lookupUrl: null,
            important: false,
            filterable: true,
            description: null,
            multiValue: false,
            greaterThan: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: true
          }
        }
      ],
      item: {
        id: 4,
        collapsible: false,
        name: 'selectTemplate',
        heading: 'Select Template',
        description: 'Pick a saved template to speed up content creation'
      }
    }
  ]
};

export const CampaignActivityDetailsLayout = {
  id: 1,
  active: true,
  default: true,
  layoutActions: [],
  systemDefault: true,
  entity: 'CAMPAIGN_ACTIVITY',
  name: 'detailsCampaignActivity',
  showOnlyImportantField: true,
  layoutHeader: { label: null },
  mode: 'DETAILS',
  displayName: 'Details Campaign Activity Layout',
  layoutItems: [
    {
      id: 1,
      row: 1,
      width: 4,
      column: 1,
      type: 'SECTION',
      layoutItems: [
        {
          id: 1,
          row: 1,
          column: 1,
          width: 12,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'name',
            displayName: 'Activity Name',
            type: FieldTypes.TEXT_FIELD,
            id: 1,
            min: 3,
            max: 255,
            sectionId: 1,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: true,
            standard: true,
            lessThan: null,
            sortable: false,
            readOnly: false,
            internal: false,
            lookupUrl: null,
            filterable: true,
            important: false,
            description: null,
            multiValue: false,
            greaterThan: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false
          }
        },
        {
          id: 2,
          row: 2,
          column: 1,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'campaign',
            displayName: 'Campaign Name',
            type: FieldTypes.LOOK_UP,
            id: 2,
            sectionId: 1,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: true,
            standard: true,
            lessThan: null,
            sortable: false,
            readOnly: false,
            internal: false,
            pickLists: null,
            important: false,
            filterable: true,
            description: null,
            multiValue: false,
            greaterThan: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: true,
            lookupUrl: `/${entities.CAMPAIGNS}/lookup?excludeStatus=${CampaignStatus.COMPLETED}&q=name:`
          }
        },
        {
          id: 3,
          row: 2,
          column: 2,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'type',
            displayName: 'Activity Type',
            type: FieldTypes.PICK_LIST,
            id: 3,
            sectionId: 1,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: true,
            standard: true,
            lessThan: null,
            sortable: false,
            readOnly: false,
            internal: false,
            lookupUrl: null,
            important: false,
            filterable: true,
            description: null,
            multiValue: false,
            greaterThan: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false,
            pickLists: [
              {
                id: 'WHATSAPP',
                name: 'WHATSAPP',
                displayName: 'WhatsApp',
                disabled: false,
                systemDefault: true
              }
            ]
          }
        },
        {
          id: 4,
          row: 3,
          column: 1,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'estimatedBudget',
            displayName: 'Estimated Budget',
            type: FieldTypes.MONEY,
            id: 4,
            sectionId: 1,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: true,
            standard: true,
            lessThan: null,
            sortable: false,
            readOnly: false,
            internal: false,
            lookupUrl: null,
            filterable: true,
            important: false,
            description: null,
            multiValue: false,
            greaterThan: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false
          }
        },
        {
          id: 5,
          row: 3,
          column: 2,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'actualExpense',
            displayName: 'Actual Expense',
            type: FieldTypes.MONEY,
            id: 5,
            sectionId: 1,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: true,
            standard: true,
            lessThan: null,
            sortable: false,
            readOnly: true,
            internal: false,
            lookupUrl: null,
            filterable: true,
            important: false,
            description: null,
            multiValue: false,
            greaterThan: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false
          }
        },
        {
          id: 6,
          row: 3,
          column: 1,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'startDate',
            displayName: 'Start Date',
            type: FieldTypes.DATETIME_PICKER,
            id: 6,
            sectionId: 1,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: true,
            standard: true,
            lessThan: null,
            sortable: true,
            readOnly: false,
            internal: false,
            lookupUrl: null,
            filterable: true,
            important: false,
            description: null,
            multiValue: false,
            greaterThan: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false
          }
        },
        {
          id: 6,
          row: 3,
          column: 2,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'endDate',
            displayName: 'End Date',
            type: FieldTypes.DATETIME_PICKER,
            id: 7,
            sectionId: 1,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            standard: true,
            lessThan: null,
            sortable: true,
            required: false,
            readOnly: false,
            internal: false,
            lookupUrl: null,
            filterable: true,
            important: false,
            description: null,
            multiValue: false,
            greaterThan: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false
          }
        }
      ],
      item: {
        id: 1,
        collapsible: false,
        name: 'activityDetails',
        heading: 'Activity Details',
        description: 'Add a name, select type, and define budget for your campaign activity'
      }
    },
    {
      id: 2,
      row: 2,
      width: 4,
      column: 1,
      type: 'SECTION',
      layoutItems: [
        {
          id: 1,
          row: 1,
          column: 2,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'utmCampaign',
            displayName: 'UTM Campaign',
            type: FieldTypes.TEXT_FIELD,
            id: 1,
            min: 0,
            max: 255,
            sectionId: 2,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: true,
            standard: true,
            lessThan: null,
            sortable: false,
            readOnly: false,
            internal: false,
            lookupUrl: null,
            filterable: true,
            important: false,
            description: null,
            multiValue: false,
            greaterThan: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false,
            placeholder: 'e.g. summer_sale, product_launch, leadgen2025'
          }
        },
        {
          id: 2,
          row: 1,
          column: 2,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'utmSource',
            displayName: 'UTM Source',
            type: FieldTypes.TEXT_FIELD,
            id: 2,
            min: 0,
            max: 255,
            sectionId: 2,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: true,
            standard: true,
            lessThan: null,
            sortable: false,
            readOnly: false,
            internal: false,
            lookupUrl: null,
            important: false,
            filterable: true,
            description: null,
            multiValue: false,
            greaterThan: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false,
            placeholder: 'e.g. google, facebook, newsletter, linkedin'
          }
        },
        {
          id: 3,
          row: 2,
          column: 1,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'utmMedium',
            displayName: 'UTM Medium',
            type: FieldTypes.TEXT_FIELD,
            id: 3,
            min: 0,
            max: 255,
            sectionId: 2,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: true,
            standard: true,
            lessThan: null,
            sortable: false,
            readOnly: false,
            internal: false,
            lookupUrl: null,
            important: false,
            filterable: true,
            description: null,
            multiValue: false,
            greaterThan: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false,
            placeholder: 'e.g. cpc, email, social, referral'
          }
        },
        {
          id: 4,
          row: 2,
          column: 2,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'utmContent',
            displayName: 'UTM Content',
            type: FieldTypes.TEXT_FIELD,
            id: 4,
            min: 0,
            max: 255,
            sectionId: 2,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            standard: true,
            lessThan: null,
            required: false,
            sortable: false,
            readOnly: false,
            internal: false,
            lookupUrl: null,
            important: false,
            filterable: true,
            description: null,
            multiValue: false,
            greaterThan: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false,
            placeholder: 'e.g. cta_top, image_ad, button_v1'
          }
        },
        {
          id: 5,
          row: 3,
          column: 1,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'utmTerm',
            displayName: 'UTM Term',
            type: FieldTypes.TEXT_FIELD,
            id: 5,
            min: 0,
            max: 255,
            sectionId: 2,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            standard: true,
            lessThan: null,
            required: false,
            sortable: false,
            readOnly: false,
            internal: false,
            lookupUrl: null,
            important: false,
            filterable: true,
            description: null,
            multiValue: false,
            greaterThan: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false,
            placeholder: 'e.g. crm+software, inventory+tool'
          }
        }
      ],
      item: {
        id: 2,
        collapsible: false,
        name: 'utmInformation',
        heading: 'UTM Information',
        description: 'Enter UTM details to track campaign activity performance across sources'
      }
    },
    {
      id: 3,
      row: 3,
      width: 4,
      column: 1,
      type: 'SECTION',
      layoutItems: [
        {
          id: 1,
          row: 1,
          column: 1,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'entity',
            displayName: 'Entity',
            type: FieldTypes.PICK_LIST,
            id: 1,
            sectionId: 3,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: true,
            standard: true,
            lessThan: null,
            sortable: false,
            readOnly: false,
            internal: false,
            lookupUrl: null,
            filterable: true,
            important: false,
            description: null,
            multiValue: false,
            greaterThan: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false,
            pickLists: [
              {
                id: 'LEAD',
                name: 'LEAD',
                displayName: 'Lead',
                disabled: false,
                systemDefault: true
              },
              {
                id: 'CONTACT',
                name: 'CONTACT',
                displayName: 'Contact',
                disabled: false,
                systemDefault: true
              }
            ]
          }
        },
        {
          id: 2,
          row: 1,
          column: 2,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'selectedSmartlist',
            displayName: 'Smartlist',
            type: FieldTypes.PICK_LIST,
            id: 2,
            sectionId: 3,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: false,
            standard: true,
            lessThan: null,
            sortable: false,
            readOnly: false,
            internal: false,
            lookupUrl: null,
            important: false,
            filterable: true,
            description: null,
            multiValue: false,
            greaterThan: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false
          }
        },
        {
          id: 3,
          row: 2,
          column: 1,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'sentTo',
            displayName: 'Sent To',
            type: FieldTypes.PICK_LIST,
            id: 3,
            sectionId: 3,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: true,
            standard: true,
            lessThan: null,
            sortable: false,
            readOnly: false,
            internal: false,
            lookupUrl: null,
            important: false,
            filterable: true,
            description: null,
            multiValue: false,
            greaterThan: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false,
            pickLists: [
              {
                id: PHONE_NUMBER_TYPE.PRIMARY_PHONE_NUMBER,
                name: PHONE_NUMBER_TYPE.PRIMARY_PHONE_NUMBER,
                displayName: 'Primary Phone number',
                disabled: false,
                systemDefault: true
              },
              {
                id: PHONE_NUMBER_TYPE.ALL_PHONE_NUMBERS,
                name: PHONE_NUMBER_TYPE.ALL_PHONE_NUMBERS,
                displayName: 'All Available Phone numbers',
                disabled: false,
                systemDefault: true
              }
            ]
          }
        },
        {
          id: 4,
          row: 3,
          column: 1,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'filters',
            displayName: 'Filters',
            type: FieldTypes.PICK_LIST,
            id: 4,
            sectionId: 3,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            standard: true,
            lessThan: null,
            required: false,
            sortable: false,
            readOnly: false,
            internal: false,
            lookupUrl: null,
            important: false,
            filterable: true,
            description: null,
            multiValue: false,
            greaterThan: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false
          }
        }
      ],
      item: {
        id: 3,
        collapsible: false,
        name: 'recipients',
        heading: 'Recipients',
        description: 'Choose who will receive this activity leads, contacts, or custom lists'
      }
    },
    {
      id: 4,
      row: 4,
      width: 4,
      column: 1,
      type: 'SECTION',
      layoutItems: [
        {
          id: 1,
          row: 1,
          column: 1,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'connectedAccount',
            displayName: 'Whatsapp Account',
            type: FieldTypes.LOOK_UP,
            id: 1,
            sectionId: 4,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: true,
            standard: true,
            lessThan: null,
            sortable: false,
            readOnly: false,
            internal: false,
            filterable: true,
            important: false,
            description: null,
            multiValue: false,
            greaterThan: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: true,
            lookupUrl: '/messages/connected-accounts/lookup?q='
          }
        },
        {
          id: 2,
          row: 1,
          column: 2,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'whatsappTemplate',
            displayName: 'Template Name',
            type: FieldTypes.LOOK_UP,
            id: 2,
            sectionId: 4,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: true,
            standard: true,
            lessThan: null,
            sortable: false,
            readOnly: false,
            internal: false,
            lookupUrl: null,
            important: false,
            filterable: true,
            description: null,
            multiValue: false,
            greaterThan: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: true
          }
        }
      ],
      item: {
        id: 4,
        collapsible: false,
        name: 'selectTemplate',
        heading: 'Select Template',
        description: 'Pick a saved template to speed up content creation'
      }
    },
    {
      id: 5,
      row: 5,
      width: 4,
      column: 1,
      type: 'SECTION',
      layoutItems: [
        {
          id: 1,
          row: 1,
          column: 1,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'createdAt',
            displayName: 'Created At',
            type: FieldTypes.DATETIME_PICKER,
            id: 1,
            sectionId: 3,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            lessThan: null,
            internal: true,
            standard: false,
            required: false,
            sortable: false,
            readOnly: false,
            lookupUrl: null,
            filterable: true,
            important: false,
            description: null,
            multiValue: false,
            greaterThan: null,
            placeholder: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false
          }
        },
        {
          id: 2,
          row: 1,
          column: 2,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'createdBy',
            displayName: 'Created By',
            type: FieldTypes.LOOK_UP,
            id: 2,
            sectionId: 3,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: false,
            standard: false,
            lessThan: null,
            internal: true,
            sortable: false,
            readOnly: false,
            lookupUrl: null,
            filterable: true,
            important: false,
            description: null,
            multiValue: false,
            greaterThan: null,
            placeholder: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false
          }
        },
        {
          id: 3,
          row: 2,
          column: 1,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'updatedAt',
            displayName: 'Updated At',
            type: FieldTypes.DATETIME_PICKER,
            id: 3,
            sectionId: 3,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: false,
            standard: false,
            lessThan: null,
            internal: true,
            sortable: false,
            readOnly: false,
            lookupUrl: null,
            filterable: true,
            important: false,
            description: null,
            multiValue: false,
            greaterThan: null,
            placeholder: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false
          }
        },
        {
          id: 4,
          row: 2,
          column: 2,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'updatedBy',
            displayName: 'Updated By',
            type: FieldTypes.LOOK_UP,
            id: 4,
            sectionId: 3,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: false,
            standard: false,
            lessThan: null,
            internal: true,
            sortable: false,
            readOnly: false,
            lookupUrl: null,
            filterable: true,
            important: false,
            description: null,
            multiValue: false,
            greaterThan: null,
            placeholder: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false
          }
        },
        {
          id: 5,
          row: 3,
          column: 1,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'startedAt',
            displayName: 'Started At',
            type: FieldTypes.DATETIME_PICKER,
            id: 5,
            sectionId: 3,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: false,
            standard: false,
            lessThan: null,
            internal: true,
            sortable: false,
            readOnly: false,
            lookupUrl: null,
            filterable: true,
            important: false,
            description: null,
            multiValue: false,
            greaterThan: null,
            placeholder: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false
          }
        },
        {
          id: 6,
          row: 3,
          column: 2,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'startedBy',
            displayName: 'Started By',
            type: FieldTypes.LOOK_UP,
            id: 6,
            sectionId: 3,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: false,
            standard: false,
            lessThan: null,
            internal: true,
            sortable: false,
            readOnly: false,
            lookupUrl: null,
            filterable: true,
            important: false,
            description: null,
            multiValue: false,
            greaterThan: null,
            placeholder: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false
          }
        },
        {
          id: 7,
          row: 4,
          column: 1,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'lastPausedAt',
            displayName: 'Last Paused At',
            type: FieldTypes.DATETIME_PICKER,
            id: 7,
            sectionId: 3,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: false,
            standard: false,
            lessThan: null,
            internal: true,
            sortable: false,
            readOnly: false,
            lookupUrl: null,
            filterable: true,
            important: false,
            description: null,
            multiValue: false,
            greaterThan: null,
            placeholder: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false
          }
        },
        {
          id: 8,
          row: 4,
          column: 2,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'lastPausedBy',
            displayName: 'Last Paused By',
            type: FieldTypes.LOOK_UP,
            id: 8,
            sectionId: 3,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: false,
            standard: false,
            lessThan: null,
            internal: true,
            sortable: false,
            readOnly: false,
            lookupUrl: null,
            filterable: true,
            important: false,
            description: null,
            multiValue: false,
            greaterThan: null,
            placeholder: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false
          }
        },
        {
          id: 9,
          row: 5,
          column: 1,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'lastResumedAt',
            displayName: 'Last Resumed At',
            type: FieldTypes.DATETIME_PICKER,
            id: 9,
            sectionId: 3,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: false,
            standard: false,
            lessThan: null,
            internal: true,
            sortable: false,
            readOnly: false,
            lookupUrl: null,
            filterable: true,
            important: false,
            description: null,
            multiValue: false,
            greaterThan: null,
            placeholder: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false
          }
        },
        {
          id: 10,
          row: 5,
          column: 2,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'lastResumedBy',
            displayName: 'Last Resumed By',
            type: FieldTypes.LOOK_UP,
            id: 10,
            sectionId: 3,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: false,
            standard: false,
            lessThan: null,
            internal: true,
            sortable: false,
            readOnly: false,
            lookupUrl: null,
            filterable: true,
            important: false,
            description: null,
            multiValue: false,
            greaterThan: null,
            placeholder: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false
          }
        },
        {
          id: 11,
          row: 6,
          column: 1,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'endedAt',
            displayName: 'Ended At',
            type: FieldTypes.DATETIME_PICKER,
            id: 11,
            sectionId: 3,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: false,
            standard: false,
            lessThan: null,
            internal: true,
            sortable: false,
            readOnly: false,
            lookupUrl: null,
            filterable: true,
            important: false,
            description: null,
            multiValue: false,
            greaterThan: null,
            placeholder: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false
          }
        },
        {
          id: 12,
          row: 6,
          column: 2,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'endedBy',
            displayName: 'Ended By',
            type: FieldTypes.LOOK_UP,
            id: 12,
            sectionId: 3,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            required: false,
            standard: false,
            lessThan: null,
            internal: true,
            sortable: false,
            readOnly: false,
            lookupUrl: null,
            filterable: true,
            important: false,
            description: null,
            multiValue: false,
            greaterThan: null,
            placeholder: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false
          }
        },
        {
          id: 13,
          row: 7,
          column: 1,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'id',
            displayName: 'ID',
            type: FieldTypes.NUMBER,
            id: 13,
            sectionId: 3,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            lessThan: null,
            internal: true,
            standard: false,
            required: false,
            sortable: false,
            readOnly: false,
            lookupUrl: null,
            filterable: true,
            important: false,
            description: null,
            multiValue: false,
            greaterThan: null,
            placeholder: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false
          }
        },
        {
          id: 14,
          row: 8,
          column: 2,
          width: 6,
          type: 'FIELD',
          layoutItems: [],
          item: {
            internalName: 'status',
            displayName: 'Status',
            type: FieldTypes.PICK_LIST,
            id: 13,
            sectionId: 3,
            regex: null,
            entity: null,
            length: null,
            active: true,
            masked: false,
            unique: false,
            lessThan: null,
            internal: true,
            standard: false,
            required: false,
            sortable: false,
            readOnly: false,
            lookupUrl: null,
            filterable: true,
            important: false,
            description: null,
            multiValue: false,
            greaterThan: null,
            placeholder: null,
            primaryField: null,
            colorConfiguration: [],
            showDefaultOptions: false
          }
        }
      ],
      item: {
        id: 5,
        name: 'internals',
        heading: 'Internals',
        description: null,
        collapsible: false
      }
    }
  ]
};

export const CampaignAnalyticsDimension = {
  [entities.CAMPAIGNS]: {
    [CampaignAnalyticsType.OVERALL_ENGAGEMENT]: ['DAY'],
    [CampaignAnalyticsType.ENGAGEMENT_PER_ACTIVITY]: ['DAY', 'ACTIVITIES']
  },
  [entities.CAMPAIGN_ACTIVITIES]: {
    [CampaignAnalyticsType.RESPONSE_COUNT]: ['ACTIVITY_STATUS'],
    [CampaignAnalyticsType.OVERALL_ENGAGEMENT]: ['DAY']
  }
};

export const LINE_CHART_COLORS = [
  '#FF7557',
  '#7856FF',
  '#B2596E',
  '#3BA974'
];

export const CAMPAIGN_FUNNEL_REPORT_DIMENSION_VALUES = ['Sent', 'Delivered', 'Read'];

export const CampaignActivityRecipientStatusColors = {
  [CampaignActivityRecipientStatusType.SENT]: { color: '#15B8A6', backgroundColor: '#15B8A61A' },
  [CampaignActivityRecipientStatusType.READ]: { color: '#3C82F6', backgroundColor: '#3C82F61A' },
  [CampaignActivityRecipientStatusType.DELIVERED]: { color: '#6466F1', backgroundColor: '#6466F11A' },
  [CampaignActivityRecipientStatusType.FAILED]: { color: '#DA5597', backgroundColor: '#DA55971A' }
};

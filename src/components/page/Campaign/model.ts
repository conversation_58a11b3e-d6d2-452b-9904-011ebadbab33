import { IdName } from '../../shared/KanbanBoard/model';
import { CurrencyValue } from '../../shared/Input/Money/Money';
import { PermissionAction } from '../../../utils/models/PermissionModel';
import { ListLayoutFilterRule } from '../../shared/ListLayoutFilters/model';
import { ConnectedAccount } from '../Communication/WhatsAppSettings/WhatsAppSettingsReducer';

export enum CampaignStatus {
  DRAFT = 'DRAFT',
  IN_PROGRESS = 'IN_PROGRESS',
  PAUSED = 'PAUSED',
  COMPLETED = 'COMPLETED'
}

export interface Campaign {
  id: number;
  name: string;
  utmTerm: string;
  utmSource: string;
  utmMedium: string;
  utmContent: string;
  utmCampaign: string;
  description: string;
  status: CampaignStatus;
  totalEngagement: number;
  estimatedBudget: CurrencyValue;
  actualExpense: CurrencyValue;
  activities: CampaignActivity[];
  startDate: string;
  startedAt: string;
  startedBy: IdName;
  endDate: string;
  endedAt: string;
  endedBy: IdName;
  createdAt: string;
  createdBy: IdName;
  updatedAt: string;
  updatedBy: IdName;
  recordActions: PermissionAction;
}

export interface CampaignListContent {
  size: number;
  last: boolean;
  first: boolean;
  totalPages: number;
  content: Campaign[];
  totalElements: number;
  numberOfElements: number;
}

export enum CampaignActivityStatus {
  DRAFT = 'DRAFT',
  FAILED = 'FAILED',
  QUEUED = 'QUEUED',
  IN_PROGRESS = 'IN_PROGRESS',
  PAUSED = 'PAUSED',
  COMPLETED = 'COMPLETED',
  PROCESSED = 'PROCESSED'
}

export enum CampaignActivityType {
  WHATSAPP = 'WHATSAPP'
}

export enum PHONE_NUMBER_TYPE {
  ALL_PHONE_NUMBERS = 'ALL_PHONE_NUMBERS',
  PRIMARY_PHONE_NUMBER = 'PRIMARY_PHONE_NUMBER'
}

export interface CampaignActivity {
  id: number;
  name: string;
  bulkJobId: number;
  status: CampaignActivityStatus;
  entity: string;
  utmTerm: string;
  utmSource: string;
  utmMedium: string;
  utmContent: string;
  utmCampaign: string;
  campaign: IdName;
  totalEngagement: number;
  estimatedBudget: CurrencyValue;
  actualExpense: CurrencyValue;
  createdAt: string;
  createdBy: IdName;
  updatedAt: string;
  updatedBy: IdName;
  startDate: string;
  startedAt: string;
  startedBy: IdName;
  endDate: string;
  endedAt: string;
  endedBy: IdName;
  lastPausedAt: string;
  lastPausedBy: IdName;
  filters: {
    jsonRule: {
      valid: boolean;
      condition: string;
      rules: ListLayoutFilterRule[];
    }
  };
  payload: {
    type: CampaignActivityType;
    sentTo: PHONE_NUMBER_TYPE;
    whatsappTemplate: IdName;
    connectedAccount: IdName;
  };
  recordActions: PermissionAction;
}

export interface CampaignActivityFormValues {
  id: number;
  name: string;
  campaign: IdName;
  status: CampaignActivityStatus;
  utmTerm: string;
  utmSource: string;
  utmMedium: string;
  utmContent: string;
  utmCampaign: string;
  estimatedBudget: CurrencyValue;
  actualExpense: CurrencyValue;
  createdAt: string;
  createdBy: IdName;
  updatedAt: string;
  updatedBy: IdName;
  startDate: string;
  startedAt: string;
  startedBy: IdName;
  endDate: string;
  endedAt: string;
  endedBy: IdName;
  lastPausedAt: string;
  lastPausedBy: IdName;
  filters: ListLayoutFilterRule[];
  whatsappTemplate: IdName;
  connectedAccount: ConnectedAccount;
  entity:  {
    id: string;
    name: string;
    displayName: string;
    disabled: boolean;
    systemDefault: boolean;
  };
  type: {
    id: CampaignActivityType;
    name: string;
    disabled: boolean;
    systemDefault: boolean;
  };
  sentTo: {
    id: PHONE_NUMBER_TYPE;
    name: string;
    disabled: boolean;
    systemDefault: boolean;
  };
}

export enum CampaignActionType {
  START = 'START',
  RESUME = 'RESUME',
  PAUSE = 'PAUSE',
  COMPLETE = 'COMPLETE'
}

export interface CampaignActivityListContent {
  size: number;
  last: boolean;
  first: boolean;
  totalPages: number;
  totalElements: number;
  numberOfElements: number;
  content: CampaignActivity[];
}

export enum CampaignAnalyticsType {
  RESPONSE_COUNT = 'Response Count',
  BUDGET_VS_ACTUAL_EXPENSE = 'Budget vs Actual Expense',
  OVERALL_ENGAGEMENT = 'Overall Engagement',
  ENGAGEMENT_PER_ACTIVITY = 'Engagement per activity'
}

export interface CampaignDimension {
  id: number;
  name: string;
  value: number;
}

export interface CampaignAnalyticsData {
  id: number;
  name: string;
  value: number;
  color?: string;
  clipPathValue?: number;
  dimension: CampaignDimension[];
}

export enum CampaignActivityRecipientStatusType {
  ALL = 'ALL',
  SENT = 'SENT',
  READ = 'READ',
  DELIVERED = 'DELIVERED',
  FAILED = 'FAILED'
}

export interface CampaignActivityRecipientStatus {
  entityId: number;
  entityType: string;
  phoneNumber: string;
  recipientName: string;
  status: CampaignActivityRecipientStatusType;
  sentAt: string;
  readAt: string;
  failedAt: string;
  deliveredAt: string;
  errorMessage: string;
}

export interface CampaignActivityRecipientStatusListContent {
  size: number;
  last: boolean;
  first: boolean;
  totalPages: number;
  totalElements: number;
  numberOfElements: number;
  content: CampaignActivityRecipientStatus[];
}

import axios from 'axios';
import MockAdapter from 'axios-mock-adapter';

jest.mock('../../../config/apiConfig', () => ({
  generateBaseUrl: jest.fn().mockReturnValue('http://localhost:8088/v1')
}));

import { SortOrder } from '../../shared/ListingTable/models/ListingTable';
import { CampaignActionType, CampaignActivityRecipientStatusType, CampaignActivityType, CampaignAnalyticsType } from './model';

import { storeData } from '../../../store/mockStore';
import { CampaignAnalyticsDimension } from './constants';
import { entities, FILTER_OPERATORS } from '../../../utils/constants';
import { mockGetCampaignActivity, mockGetCampaignActivityList, mockGetCampaignActivityRecipientStatusList, mockGetCampaignActivtiyAnalyticsData, mockGetCampaignAnalyticsData, mockGetCampaignList } from './stub';

import {
  runCampaign,
  createCampaign,
  deleteCampaign,
  updateCampaign,
  getCampaignList,
  getCampaignDetails,
  runCampaignActivity,
  getCampaignAnalytics,
  createCampaignActivity,
  deleteCampaignActivity,
  updateCampaignActivity,
  getCampaignActivityList,
  getCampaignOverviewDetails,
  getCampaignActivityDetails,
  getCampaignActivityAnalytics,
  getCampaignActivityRecipientStatusList
} from './service';

describe('Campaign service', () => {
  const mock = new MockAdapter(axios);

  const history = { push: jest.fn() };
  const abortSignal = new AbortController().signal;
  const timezone = storeData.loginForm.userPreferences.timezone;

  beforeEach(() => mock.reset());

  it('should call campaign list api', (done) => {
    const payload = { jsonRule: null };
    mock.onPost('http://localhost:8088/v1/campaigns/search?sort=updatedAt,desc&page=0&size=10', { ...payload }).reply(200, mockGetCampaignList);

    getCampaignList(1, 10, history, abortSignal)
      .then((response) => {
        expect(response.status).toEqual(200);
        expect(response.data).toEqual(mockGetCampaignList);
        done();
      })
      .catch(err => done.fail(err));
  });

  it('should create campaign', (done) => {
    mock.onPost('http://localhost:8088/v1/campaigns', { ...mockGetCampaignList.content[0] }).reply(200, { id: 1 });

    createCampaign(mockGetCampaignList.content[0], history)
      .then((response) => {
        expect(response.status).toEqual(200);
        expect(response.data).toEqual({ id: 1 });
        done();
      })
      .catch(err => done.fail(err));
  });

  it('should get campaign details', (done) => {
    mock.onGet('http://localhost:8088/v1/campaigns/1').reply(200, mockGetCampaignList.content[0]);

    getCampaignDetails(1, history, abortSignal)
      .then((response) => {
        expect(response.data).toEqual(mockGetCampaignList.content[0]);
        done();
      })
      .catch(err => done.fail(err));
  });

  it('should get campaign overview details', (done) => {
    mock.onGet('http://localhost:8088/v1/campaigns/1/overview').reply(200, mockGetCampaignList.content[0]);

    getCampaignOverviewDetails(1, history, abortSignal)
      .then((response) => {
        expect(response.data).toEqual(mockGetCampaignList.content[0]);
        done();
      })
      .catch(err => done.fail(err));
  });

  it('should update campaign', (done) => {
    mock.onPut('http://localhost:8088/v1/campaigns/1', { ...mockGetCampaignList.content[0] }).reply(200, mockGetCampaignList.content[0]);

    updateCampaign(mockGetCampaignList.content[0], history)
      .then((response) => {
        expect(response.data).toEqual(mockGetCampaignList.content[0]);
        done();
      })
      .catch(err => done.fail(err));
  });

  it('should delete campaign', (done) => {
    mock.onDelete('http://localhost:8088/v1/campaigns/1').reply(200);

    deleteCampaign(1, history)
      .then((response) => {
        expect(response.status).toEqual(200);
        done();
      })
      .catch(err => done.fail(err));
  });

  it('should create campaign activity', (done) => {
    mock.onPost('http://localhost:8088/v1/campaigns/123/activities', { ...mockGetCampaignActivity }).reply(200, { id: 1 });

    createCampaignActivity(mockGetCampaignActivity, history)
      .then((response) => {
        expect(response.status).toEqual(200);
        expect(response.data).toEqual({ id: 1 });
        done();
      })
      .catch(err => done.fail(err));
  });

  it('should get campaign details', (done) => {
    mock.onGet('http://localhost:8088/v1/campaigns/1/activities/1').reply(200, mockGetCampaignActivity);

    getCampaignActivityDetails(1, 1, history)
      .then((response) => {
        expect(response.data).toEqual(mockGetCampaignActivity);
        done();
      })
      .catch(err => done.fail(err));
  });

  it('should update campaign activity', (done) => {
    mock.onPut('http://localhost:8088/v1/campaigns/123/activities/1', { ...mockGetCampaignActivity }).reply(200, mockGetCampaignActivity);

    updateCampaignActivity(mockGetCampaignActivity, history)
      .then((response) => {
        expect(response.data).toEqual(mockGetCampaignActivity);
        done();
      })
      .catch(err => done.fail(err));
  });

  it('should delete campaign activity', (done) => {
    mock.onDelete('http://localhost:8088/v1/campaigns/1/activities/1').reply(200);

    deleteCampaignActivity(1, 1, history)
      .then((response) => {
        expect(response.status).toEqual(200);
        done();
      })
      .catch(err => done.fail(err));
  });

  it('should run campaign', (done) => {
    mock.onPost('http://localhost:8088/v1/campaigns/1/start').reply(200);

    runCampaign(1, CampaignActionType.START, history)
      .then((response) => {
        expect(response.status).toEqual(200);
        done();
      })
      .catch(err => done.fail(err));
  });

  it('should run campaign activity', (done) => {
    mock.onPost('http://localhost:8088/v1/campaigns/1/activities/1/start').reply(200);

    runCampaignActivity(1, 1, CampaignActionType.START, history)
      .then((response) => {
        expect(response.status).toEqual(200);
        done();
      })
      .catch(err => done.fail(err));
  });

  it('should call campaign activity list api', (done) => {
    const payload = {
      jsonRule: {
        valid: true,
        condition: 'AND',
        rules: [{
          id: 'type',
          field: 'type',
          type: 'string',
          value: CampaignActivityType.WHATSAPP,
          operator: FILTER_OPERATORS.equal.internalName
        }]
      }
    };

    mock.onPost('http://localhost:8088/v1/campaigns/activities/search?sort=updatedAt,desc&page=0&size=10', { ...payload }).reply(200, mockGetCampaignActivityList);

    getCampaignActivityList(1, 10, history, abortSignal)
      .then((response) => {
        expect(response.status).toEqual(200);
        expect(response.data).toEqual(mockGetCampaignActivityList);
        done();
      })
      .catch(err => done.fail(err));
  });

  it('should get campaign analytics', (done) => {
    const dateRange = { startDate: '2025-05-01T18:30:00.000Z', endDate: '2025-05-15T18:29:59.999Z' };
    const dimension = CampaignAnalyticsDimension[entities.CAMPAIGNS][CampaignAnalyticsType.OVERALL_ENGAGEMENT];

    const payload = {
      timeZone: timezone,
      groupBy: dimension,
      metric: { type: 'COUNT' },
      dateRange: [dateRange.startDate, dateRange.endDate]
    };

    mock.onPost('http://localhost:8088/v1/campaigns/1/analytics', { ...payload }).reply(200, mockGetCampaignAnalyticsData);

    getCampaignAnalytics(1, dimension, timezone, dateRange, history, abortSignal)
      .then((response) => {
        expect(response.status).toEqual(200);
        expect(response.data).toEqual(mockGetCampaignAnalyticsData);
        done();
      })
      .catch(err => done.fail(err));
  });

  it('should get campaign activity analytics', (done) => {
    const dateRange = { startDate: '2025-05-01T18:30:00.000Z', endDate: '2025-05-15T18:29:59.999Z' };
    const dimension = CampaignAnalyticsDimension[entities.CAMPAIGN_ACTIVITIES][CampaignAnalyticsType.RESPONSE_COUNT];

    const payload = {
      timeZone: timezone,
      groupBy: dimension,
      metric: { type: 'COUNT' },
      dateRange: [dateRange.startDate, dateRange.endDate]
    };

    mock.onPost('http://localhost:8088/v1/campaigns/1/activities/1/analytics', { ...payload }).reply(200, mockGetCampaignActivtiyAnalyticsData);

    getCampaignActivityAnalytics(1, 1, dimension, timezone, dateRange, history, abortSignal)
      .then((response) => {
        expect(response.status).toEqual(200);
        expect(response.data).toEqual(mockGetCampaignActivtiyAnalyticsData);
        done();
      })
      .catch(err => done.fail(err));
  });

  it('should call campaign activity recipient status list api', (done) => {
    const payload = {
      jsonRule: {
        valid: true,
        condition: 'AND',
        rules: [{
          id: 'status',
          field: 'status',
          type: 'string',
          value: CampaignActivityRecipientStatusType.SENT,
          operator: FILTER_OPERATORS.equal.internalName
        }]
      }
    };

    mock.onPost('http://localhost:8088/v1/campaigns/activities/1/recipient-status?sort=sentAt,desc&page=0&size=10', { ...payload }).reply(200, mockGetCampaignActivityRecipientStatusList);

    getCampaignActivityRecipientStatusList(1, CampaignActivityRecipientStatusType.SENT, 'sentAt', SortOrder.DESC, 1, 10, history, abortSignal)
      .then((response) => {
        expect(response.status).toEqual(200);
        expect(response.data).toEqual(mockGetCampaignActivityRecipientStatusList);
        done();
      })
      .catch(err => done.fail(err));
  });
});

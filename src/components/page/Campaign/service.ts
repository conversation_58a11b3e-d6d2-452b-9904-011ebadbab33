import axios from 'axios';

import { DateRange } from '../../shared/DateRangePickerComponent/model';
import { SortOrder } from '../../shared/ListingTable/models/ListingTable';
import { Campaign, CampaignActivity, CampaignActionType, CampaignActivityType, CampaignActivityRecipientStatusType } from './model';

import * as api from '../../../services/api';
import { entities, FILTER_OPERATORS } from '../../../utils/constants';
import { defaultApiConfig, generateBaseUrl } from '../../../config/apiConfig';

export const getCampaignList = (pageNumber: number = 1, pageSize: number = 10, history, abortSignal: AbortSignal) => {
  const headers = api.setHeaders();
  const baseUrl = generateBaseUrl(defaultApiConfig, entities.CAMPAIGNS);

  const url = `${baseUrl}/campaigns/search?sort=updatedAt,desc&page=${pageNumber-1}&size=${pageSize}`;

  // @ts-ignore
  return axios({
    url,
    headers,
    history,
    method: 'post',
    shouldRetry: true,
    signal: abortSignal,
    data: { jsonRule: null }
  });
};

export const createCampaign = (formValues: Campaign, history) => {
  const headers = api.setHeaders();
  const baseUrl = generateBaseUrl(defaultApiConfig, entities.CAMPAIGNS);

  const url = `${baseUrl}/campaigns`;

   // @ts-ignore
  return axios({
    url,
    headers,
    history,
    method: 'post',
    data: formValues,
    shouldRetry: true
  });
};

export const getCampaignDetails = (campaignId: number, history, abortSignal: AbortSignal) => {
  const headers = api.setHeaders();
  const baseUrl = generateBaseUrl(defaultApiConfig, entities.CAMPAIGNS);

  const url = `${baseUrl}/campaigns/${campaignId}`;

   // @ts-ignore
  return axios({
    url,
    headers,
    history,
    method: 'get',
    shouldRetry: true,
    signal: abortSignal
  });
};

export const getCampaignOverviewDetails = (campaignId: number, history, abortSignal: AbortSignal) => {
  const headers = api.setHeaders();
  const baseUrl = generateBaseUrl(defaultApiConfig, entities.CAMPAIGNS);

  const url = `${baseUrl}/campaigns/${campaignId}/overview`;

   // @ts-ignore
  return axios({
    url,
    headers,
    history,
    method: 'get',
    shouldRetry: true,
    signal: abortSignal
  });
};

export const updateCampaign = (formValues: Campaign, history) => {
  const headers = api.setHeaders();
  const baseUrl = generateBaseUrl(defaultApiConfig, entities.CAMPAIGNS);

  const url = `${baseUrl}/campaigns/${formValues.id}`;

   // @ts-ignore
  return axios({
    url,
    headers,
    history,
    method: 'put',
    data: formValues,
    shouldRetry: true
  });
};

export const deleteCampaign = (campaignId: number, history) => {
  const headers = api.setHeaders();
  const baseUrl = generateBaseUrl(defaultApiConfig, entities.CAMPAIGNS);

  const url = `${baseUrl}/campaigns/${campaignId}`;

   // @ts-ignore
  return axios({
    url,
    headers,
    history,
    method: 'delete',
    shouldRetry: true
  });
};

export const createCampaignActivity = (formValues: CampaignActivity, history) => {
  const headers = api.setHeaders();
  const baseUrl = generateBaseUrl(defaultApiConfig, entities.CAMPAIGNS);
  const campaignId = formValues.campaign.id;

  const url = `${baseUrl}/campaigns/${campaignId}/activities`;

   // @ts-ignore
  return axios({
    url,
    headers,
    history,
    method: 'post',
    data: formValues,
    shouldRetry: true
  });
};

export const getCampaignActivityDetails = (activityId: number, campaignId: number, history) => {
  const headers = api.setHeaders();
  const baseUrl = generateBaseUrl(defaultApiConfig, entities.CAMPAIGNS);

  const url = `${baseUrl}/campaigns/${campaignId}/activities/${activityId}`;

   // @ts-ignore
  return axios({
    url,
    headers,
    history,
    method: 'get',
    shouldRetry: true
  });
};

export const updateCampaignActivity = (formValues: CampaignActivity, history) => {
  const headers = api.setHeaders();
  const baseUrl = generateBaseUrl(defaultApiConfig, entities.CAMPAIGNS);
  const campaignId = formValues.campaign.id;

  const url = `${baseUrl}/campaigns/${campaignId}/activities/${formValues.id}`;

   // @ts-ignore
  return axios({
    url,
    headers,
    history,
    method: 'put',
    data: formValues,
    shouldRetry: true
  });
};

export const deleteCampaignActivity = (activityId: number, campaignId: number, history) => {
  const headers = api.setHeaders();
  const baseUrl = generateBaseUrl(defaultApiConfig, entities.CAMPAIGNS);

  const url = `${baseUrl}/campaigns/${campaignId}/activities/${activityId}`;

   // @ts-ignore
  return axios({
    url,
    headers,
    history,
    method: 'delete',
    shouldRetry: true
  });
};

export const runCampaign = (campaignId: number, action: CampaignActionType, history) => {
  const headers = api.setHeaders();
  const baseUrl = generateBaseUrl(defaultApiConfig, entities.CAMPAIGNS);

  const url = `${baseUrl}/campaigns/${campaignId}/${action.toLowerCase()}`;

  // @ts-ignore
  return axios({
    url,
    headers,
    history,
    method: 'post',
    shouldRetry: true
  });
};

export const runCampaignActivity = (activityId: number, campaignId: number, action: CampaignActionType, history) => {
  const headers = api.setHeaders();
  const baseUrl = generateBaseUrl(defaultApiConfig, entities.CAMPAIGNS);

  const url = `${baseUrl}/campaigns/${campaignId}/activities/${activityId}/${action.toLowerCase()}`;

  // @ts-ignore
  return axios({
    url,
    headers,
    history,
    method: 'post',
    shouldRetry: true
  });
};

export const getCampaignActivityList = (pageNumber: number = 1, pageSize: number = 10, history, abortSignal: AbortSignal) => {
  const headers = api.setHeaders();
  const baseUrl = generateBaseUrl(defaultApiConfig, entities.CAMPAIGNS);

  const url = `${baseUrl}/campaigns/activities/search?sort=updatedAt,desc&page=${pageNumber-1}&size=${pageSize}`;

  const payload = {
    jsonRule: {
      valid: true,
      condition: 'AND',
      rules: [{
        id: 'type',
        field: 'type',
        type: 'string',
        value: CampaignActivityType.WHATSAPP,
        operator: FILTER_OPERATORS.equal.internalName
      }]
    }
  };

  // @ts-ignore
  return axios({
    url,
    headers,
    history,
    data: payload,
    method: 'post',
    shouldRetry: true,
    signal: abortSignal
  });
};

export const getCampaignAnalytics = (id: number, dimension: string[], timezone: string, dateRange: DateRange, history, abortSignal: AbortSignal) => {
  const headers = api.setHeaders();
  const baseUrl = generateBaseUrl(defaultApiConfig, entities.CAMPAIGNS);

  const url = `${baseUrl}/campaigns/${id}/analytics`;

  const payload = {
    timeZone: timezone,
    groupBy: dimension,
    metric: { type: 'COUNT' },
    dateRange: [dateRange.startDate, dateRange.endDate]
  };

  // @ts-ignore
  return axios({
    url,
    headers,
    history,
    data: payload,
    method: 'post',
    shouldRetry: true,
    signal: abortSignal
  });
};

export const getCampaignActivityAnalytics = (id: number, campaignId: number, dimension: string[], timezone: string, dateRange: DateRange, history, abortSignal: AbortSignal) => {
  const headers = api.setHeaders();
  const baseUrl = generateBaseUrl(defaultApiConfig, entities.CAMPAIGNS);

  const url = `${baseUrl}/campaigns/${campaignId}/activities/${id}/analytics`;

  const payload = {
    timeZone: timezone,
    groupBy: dimension,
    metric: { type: 'COUNT' },
    dateRange: [dateRange.startDate, dateRange.endDate]
  };

  // @ts-ignore
  return axios({
    url,
    headers,
    history,
    data: payload,
    method: 'post',
    shouldRetry: true,
    signal: abortSignal
  });
};

export const getCampaignActivityRecipientStatusList = (
  id: number,
  filter: CampaignActivityRecipientStatusType,
  sortByField: string,
  sortOrder: SortOrder,
  pageNumber: number = 1,
  pageSize: number = 10,
  history,
  abortSignal: AbortSignal
) => {
  const headers = api.setHeaders();
  const baseUrl = generateBaseUrl(defaultApiConfig, entities.CAMPAIGNS);

  const url = `${baseUrl}/campaigns/activities/${id}/recipient-status?sort=${sortByField},${sortOrder}&page=${pageNumber-1}&size=${pageSize}`;

  let payload = { jsonRule: null };
  if(filter !== CampaignActivityRecipientStatusType.ALL) {
    payload = {
      jsonRule: {
        valid: true,
        condition: 'AND',
        rules: [{
          id: 'status',
          field: 'status',
          type: 'string',
          value: filter,
          operator: FILTER_OPERATORS.equal.internalName
        }]
      }
    };
  }

  // @ts-ignore
  return axios({
    url,
    headers,
    history,
    data: payload,
    method: 'post',
    shouldRetry: true,
    signal: abortSignal
  });
};

import { CampaignActivityRecipientStatusType, CampaignActivityStatus, CampaignActivityType, CampaignStatus, PHONE_NUMBER_TYPE } from './model';

import { FILTER_OPERATORS } from '../../../utils/constants';

export const mockGetCampaignActivity = {
  id: 1,
  name: 'Kylas Activity',
  status: CampaignActivityStatus.DRAFT,
  entity: 'LEAD',
  bulkJobId: null,
  utmTerm: 'term',
  utmSource: 'google',
  utmMedium: 'medium',
  utmContent: 'content',
  totalEngagement: 0,
  utmCampaign: 'utm campaign',
  campaign: { id:123, name: 'campaign name' },
  estimatedBudget: { currencyId: 400, value: 100000 },
  actualExpense: { currencyId: 400, value: 110000 },
  createdAt: '2021-09-10T04:04:23.835Z',
  createdBy: { id: 3788, name: '<PERSON>' },
  updatedAt: '2021-09-10T04:04:23.835Z',
  updatedBy: { id: 3788, name: '<PERSON>' },
  startDate: '2021-09-10T04:04:23.835Z',
  startedAt: '2021-09-10T04:04:23.835Z',
  startedBy: { id: 3788, name: 'Andrew Strauss' },
  endDate: '2021-09-11T04:04:23.835Z',
  endedAt: '2021-09-10T04:04:23.835Z',
  endedBy: { id: 3788, name: 'Andrew Strauss' },
  lastPausedAt: '2021-09-10T04:04:23.835Z',
  lastPausedBy: { id: 3788, name: 'Andrew Strauss' },
  filters: {
    jsonRule: {
      valid: true,
      condition: 'AND',
      rules: [
        {
          id: 'id',
          field: 'id',
          type: 'long',
          operator: FILTER_OPERATORS.in.internalName,
          value: '553092,553052'
        }
      ]
    }
  },
  payload: {
    type: CampaignActivityType.WHATSAPP,
    sentTo: PHONE_NUMBER_TYPE.PRIMARY_PHONE_NUMBER,
    whatsappTemplate: { id: 47, name: 'Welcome to Kylas' },
    connectedAccount: { id: 1, name: 'Whatsapp Business Account' }
  },
  recordActions: {
    read: true,
    write: true,
    update: true,
    delete: true,
    readAll: true,
    updateAll: true
  }
};

export const mockGetCampaignList = {
  size: 30,
  number: 0,
  last: false,
  first: true,
  totalPages: 1,
  totalElements: 1,
  numberOfElements: 1,
  content: [
    {
      id: 1,
      name: 'Kylas Campaign',
      status: CampaignStatus.DRAFT,
      utmTerm: 'term',
      utmSource: 'google',
      utmMedium: 'medium',
      utmContent: 'content',
      totalEngagement: 0,
      utmCampaign: 'utm campaign',
      activities: [{ ...mockGetCampaignActivity }],
      description: 'Kylas Campaign for Diwali festival',
      estimatedBudget: { currencyId: 400, value: 100000 },
      actualExpense: { currencyId: 400, value: 110000 },
      createdAt: '2021-09-10T04:04:23.835Z',
      createdBy: { id: 3788, name: 'Andrew Strauss' },
      updatedAt: '2021-09-10T04:04:23.835Z',
      updatedBy: { id: 3788, name: 'Andrew Strauss' },
      startDate: '2021-09-10T04:04:23.835Z',
      startedAt: '2021-09-10T04:04:23.835Z',
      startedBy: { id: 3788, name: 'Andrew Strauss' },
      endDate: '2021-09-11T04:04:23.835Z',
      endedAt: '2021-09-10T04:04:23.835Z',
      endedBy: { id: 3788, name: 'Andrew Strauss' },
      recordActions: {
        read: true,
        write: true,
        update: true,
        delete: true,
        readAll: true,
        updateAll: true
      }
    }
  ]
};

export const mockCampaignActivityFormValues = {
  name: 'Kylas Campaign Activity',
  status: CampaignActivityStatus.DRAFT,
  startDate: '2025-05-05T09:00:00.000Z',
  endDate: '2025-05-05T11:00:00.000Z',
  utmSource: 'google',
  utmMedium: 'email',
  utmTerm: 'crm+software',
  utmContent: 'image_ad',
  utmCampaign: 'summer sales',
  campaign: { id: 1, name: 'Kylas Campaign' },
  actualExpense: { currencyId: 400, value: 100 },
  estimatedBudget: { currencyId: 400, value: 100 },
  entity: {
    id: 'LEAD',
    name: 'LEAD',
    displayName: 'Leadw',
    disabled: false,
    systemDefault: true
  },
  selectedSmartlist: { id: 1, name: 'Created Today', displayName: 'Created Today' },
  type: {
    id: CampaignActivityType.WHATSAPP,
    name: 'WhatsApp',
    disabled: false,
    systemDefault: true
  },
  filters: [
    {
      id: 'createdAt',
      field: 'createdAt',
      operator: FILTER_OPERATORS.today.internalName,
      type: 'date',
      not: null,
      input: null,
      value: null,
      data: null,
      rules: null,
      group: false,
      property: null,
      condition: null,
      primaryField: null,
      timeZone: 'Asia/Calcutta'
    }
  ],
  whatsappTemplate: { id: 1, name: 'Welcome to Kylas' },
  sentTo: {
    id: PHONE_NUMBER_TYPE.ALL_PHONE_NUMBERS,
    name: 'All Available Phone numbers',
    disabled: false,
    systemDefault: true
  },
  connectedAccount: {
    id: 1,
    name: 'whatsapp account',
    businessName: 'whatsapp account',
    value: 1,
    label: 'whatsapp account'
  }
};

export const mockGetCampaignActivityList = {
  size: 10,
  number: 0,
  last: false,
  first: true,
  totalPages: 1,
  totalElements: 1,
  numberOfElements: 1,
  content: [{ ...mockGetCampaignActivity }]
};

export const mockGetCampaignAnalyticsData = [
  {
    id: null,
    name: '2025-06-01T18:30:00.000Z',
    dimension: [],
    value: 10000
  },
  {
    id: null,
    name: '2025-06-02T18:30:00.000Z',
    dimension: [],
    value: 90000
  },
  {
    id: null,
    name: '2025-06-03T18:30:00.000Z',
    dimension: [],
    value: 40000
  },
  {
    id: null,
    name: '2025-06-04T18:30:00.000Z',
    dimension: [],
    value: 149000
  },
  {
    id: null,
    name: '2025-06-05T18:30:00.000Z',
    dimension: [],
    value: 30200
  }
];

export const mockGetCampaignActivtiyAnalyticsData = [
  {
    id: null,
    name: 'Sent',
    dimension: [],
    value: 1000
  },
  {
    id: null,
    name: 'Delivered',
    dimension: [],
    value: 800
  },
  {
    id: null,
    name: 'Read',
    dimension: [],
    value: 500
  },
  {
    id: null,
    name: 'Failed',
    dimension: [],
    value: 200
  }
];

export const mockGetCampaigAnalyticsDataWithMultiDimensional = [
  {
    id: null,
    value: null,
    name: '2025-06-01T18:30:00.000Z',
    dimension: [
      {
        id: 1,
        name: 'Kylas Summer Campaign Activity',
        value: 100
      },
      {
        id: 2,
        name: 'Kylas Diwali Campaign Activity',
        value: 120
      }
    ]
  },
  {
    id: null,
    value: null,
    name: '2025-06-02T18:30:00.000Z',
    dimension: [
      {
        id: 1,
        name: 'Kylas Summer Campaign Activity',
        value: 120
      },
      {
        id: 2,
        name: 'Kylas Diwali Campaign Activity',
        value: 90
      }
    ]
  },
  {
    id: null,
    value: null,
    name: '2025-06-03T18:30:00.000Z',
    dimension: [
      {
        id: 1,
        name: 'Kylas Summer Campaign Activity',
        value: 160
      },
      {
        id: 2,
        name: 'Kylas Diwali Campaign Activity',
        value: 110
      }
    ]
  }
];

export const mockGetCampaignActivityRecipientStatusList = {
  size: 10,
  number: 0,
  last: false,
  first: true,
  totalPages: 1,
  totalElements: 2,
  numberOfElements: 2,
  content: [
    {
      entityId: 1,
      entityType: 'CONTACT',
      recipientName: 'New Contact',
      phoneNumber: '+919763812991',
      status: CampaignActivityRecipientStatusType.SENT,
      sentAt: '2025-07-09T07:36:42.000Z',
      readAt: null,
      failedAt: null,
      deliveredAt: null,
      errorMessage: null
    },
    {
      entityId: 2,
      entityType: 'LEAD',
      recipientName: 'My New Lead',
      phoneNumber: '+919763812991',
      status: CampaignActivityRecipientStatusType.DELIVERED,
      sentAt: '2025-07-09T07:39:07.000Z',
      deliveredAt: '2025-07-09T07:40:15.000Z',
      readAt: null,
      failedAt: null,
      errorMessage: null
    }
  ]
};

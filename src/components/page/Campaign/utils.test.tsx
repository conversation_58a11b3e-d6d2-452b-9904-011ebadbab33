import * as React from 'react';

import { entities, ReferrerFlow } from '../../../utils/constants';
import { CampaignActionType, CampaignActivityStatus, CampaignAnalyticsData, CampaignStatus } from './model';

import {
  isCampaignDisabled,
  shouldShowRecipientStatus,
  isCampaignActivityDisabled,
  formatResponseForFunnelReport,
  getInitialFormValuesForCampaign,
  formatResponseForLineChartReport,
  getInitialFormValuesForCampaignActivity,
  getRedirectionUrlForCampaignRelatedForms,
  getRequiredFormValuesToShowOnDetailsPage,
  getAvailableActionsForCampaignAsPerStatus,
  getConfirmModalPropsAsPerCampaignActionType,
  getAvailableActionsForCampaignActivityAsPerStatus
} from './utils';
import { routeToEntitySingular } from '../../../utils/entityUtils';
import { CAMPAIGN_FUNNEL_REPORT_DIMENSION_VALUES } from './constants';
import { mockGetCampaigAnalyticsDataWithMultiDimensional, mockGetCampaignActivtiyAnalyticsData, mockGetCampaignAnalyticsData } from './stub';

describe('Campaign utils', () => {
  it('should get intial form values for campaign', () => {
    expect(getInitialFormValuesForCampaign(400)).toEqual({ status: CampaignStatus.DRAFT, actualExpense: { currencyId: 400, value: 0 } });
  });

  it('should get intial form values for campaign activity', () => {
    expect(getInitialFormValuesForCampaignActivity(400)).toEqual({
      status: CampaignStatus.DRAFT,
      estimatedBudget: { currencyId: 400, value: null },
      actualExpense: { currencyId: 400, value: null }
    });
  });

  it('get require actions to run campaign as per status', () => {
    expect(getAvailableActionsForCampaignAsPerStatus(CampaignStatus.DRAFT)).toEqual([CampaignActionType.START]);
    expect(getAvailableActionsForCampaignAsPerStatus(CampaignStatus.IN_PROGRESS)).toEqual([CampaignActionType.PAUSE, CampaignActionType.COMPLETE]);
  });

  it('get require actions to run campaign activity as per status', () => {
    expect(getAvailableActionsForCampaignActivityAsPerStatus(CampaignActivityStatus.DRAFT)).toEqual([CampaignActionType.START]);
    expect(getAvailableActionsForCampaignActivityAsPerStatus(CampaignActivityStatus.PAUSED)).toEqual([CampaignActionType.RESUME, CampaignActionType.COMPLETE]);
  });

  it('should get confirm modal props as per campaignActionType', () => {
    expect(getConfirmModalPropsAsPerCampaignActionType(entities.CAMPAIGNS, 'Kylas Campaign', CampaignActionType.PAUSE)).toEqual({
      title: 'Pause Campaign',
      message:
        <div>
          <div>Are you sure, you want to pause this {routeToEntitySingular(entities.CAMPAIGNS)}. Please note, that you can resume the journey anytime you like.</div>
          <div className="mt-2">Do you want to proceed?</div>
        </div>,
      confirmBtn: { label: 'Pause', className: 'btn-primary' }
    });

    expect(getConfirmModalPropsAsPerCampaignActionType(entities.CAMPAIGN_ACTIVITIES, 'Kylas Campaign Activity', CampaignActionType.COMPLETE)).toEqual({
      title: 'Complete Activity',
      message:
        <div>
          <div>You are about to complete the <b className="text-break">Kylas Campaign Activity</b> activity. Are you sure you want to <b>Complete</b> this activity?</div>
        </div>,
      confirmBtn: { label: 'Complete Activity', className: 'btn-primary' }
    });
  });

  it('should get required formValues to show on details page', () => {
    expect(getRequiredFormValuesToShowOnDetailsPage(entities.CAMPAIGNS)).toEqual({
      'Campaign Details': [
        'id',
        'status',
        'startDate',
        'endDate',
        'createdAt',
        'createdBy',
        'updatedAt',
        'updatedBy',
        'startedAt',
        'startedBy',
        'lastPausedAt',
        'lastPausedBy',
        'lastResumedAt',
        'lastResumedBy',
        'endedAt',
        'endedBy'
      ],
      'UTM Details': [
        'utmCampaign',
        'utmSource',
        'utmMedium',
        'utmContent',
        'utmTerm'
      ]
    });
  });

  it('should get redirection url for campaign related forms', () => {
    expect(getRedirectionUrlForCampaignRelatedForms(entities.CAMPAIGNS, 1, ReferrerFlow.CAMPAIGN_FORM)).toEqual('/sales/campaigns/view/1');
    expect(getRedirectionUrlForCampaignRelatedForms(entities.CAMPAIGNS, 1, ReferrerFlow.CAMPAIGN_DETAILS)).toEqual('/sales/campaigns/details/1');
    expect(getRedirectionUrlForCampaignRelatedForms(entities.CAMPAIGN_ACTIVITIES, 1, ReferrerFlow.CAMPAIGN_DETAILS)).toEqual('/sales/campaigns/details/1');
  });

  it('should check campaign is disabled', () => {
    expect(isCampaignDisabled(CampaignStatus.DRAFT)).toEqual(false);
    expect(isCampaignDisabled(CampaignStatus.IN_PROGRESS)).toEqual(true);
  });

  it('should check campaign activity is disabled', () => {
    expect(isCampaignActivityDisabled(CampaignActivityStatus.DRAFT)).toEqual(false);
    expect(isCampaignActivityDisabled(CampaignActivityStatus.FAILED)).toEqual(false);
    expect(isCampaignActivityDisabled(CampaignActivityStatus.IN_PROGRESS)).toEqual(true);
  });

  it('should show recipient status', () => {
    expect(shouldShowRecipientStatus(CampaignActivityStatus.DRAFT)).toEqual(false);
    expect(shouldShowRecipientStatus(CampaignActivityStatus.IN_PROGRESS)).toEqual(true);
  });

  it('should format response for funnel report', () => {
    expect(formatResponseForFunnelReport(mockGetCampaignActivtiyAnalyticsData, CAMPAIGN_FUNNEL_REPORT_DIMENSION_VALUES, null)).toEqual({
      data: [
        {
          color: 'rgb(40, 126, 254)',
          dimension: [],
          id: null,
          name: 'Sent',
          value: 1000,
          clipPathValue: 1000
        },
        {
          color: 'rgb(113, 172, 254)',
          dimension: [],
          id: null,
          name: 'Delivered',
          value: 800,
          clipPathValue: 800
        },
        {
          color: 'rgb(185, 217, 254)',
          dimension: [],
          id: null,
          name: 'Read',
          value: 500,
          clipPathValue: 500
        }
      ],
      maxValue: 1000
    });

    expect(formatResponseForFunnelReport(mockGetCampaignActivtiyAnalyticsData, CAMPAIGN_FUNNEL_REPORT_DIMENSION_VALUES, [100, 75, 50])).toEqual({
      data: [
        {
          color: 'rgb(40, 126, 254)',
          dimension: [],
          id: null,
          name: 'Sent',
          value: 1000,
          clipPathValue: 100
        },
        {
          color: 'rgb(113, 172, 254)',
          dimension: [],
          id: null,
          name: 'Delivered',
          value: 800,
          clipPathValue: 75
        },
        {
          color: 'rgb(185, 217, 254)',
          dimension: [],
          id: null,
          name: 'Read',
          value: 500,
          clipPathValue: 50
        }
      ],
      maxValue: 100
    });
  });

  it('should format response for line chart report', () => {
    expect(formatResponseForLineChartReport(mockGetCampaignAnalyticsData, false, null, '#006DEE')).toEqual({
      data: [
        {
          color: '#006DEE',
          dimension: [],
          id: null,
          name: '2025-06-01T18:30:00.000Z',
          value: 10000
        },
        {
          color: '#006DEE',
          dimension: [],
          id: null,
          name: '2025-06-02T18:30:00.000Z',
          value: 90000
        },
        {
          color: '#006DEE',
          dimension: [],
          id: null,
          name: '2025-06-03T18:30:00.000Z',
          value: 40000
        },
        {
          color: '#006DEE',
          dimension: [],
          id: null,
          name: '2025-06-04T18:30:00.000Z',
          value: 149000
        },
        {
          color: '#006DEE',
          dimension: [],
          id: null,
          name: '2025-06-05T18:30:00.000Z',
          value: 30200
        }
      ],
      dimensions: new Map()
    });

    expect(formatResponseForLineChartReport(mockGetCampaigAnalyticsDataWithMultiDimensional, true, null, '#006DEE')).toEqual({
      data: [
        {
          'Kylas Diwali Campaign Activity_2': 120,
          'Kylas Summer Campaign Activity_1': 100,
          dimension: [
            {
              id: 1,
              name: 'Kylas Summer Campaign Activity',
              value: 100
            },
            {
              id: 2,
              name: 'Kylas Diwali Campaign Activity',
              value: 120
            }
          ],
          id: null,
          name: '2025-06-01T18:30:00.000Z',
          value: null
        },
        {
          'Kylas Diwali Campaign Activity_2': 90,
          'Kylas Summer Campaign Activity_1': 120,
          dimension: [
            {
              id: 1,
              name: 'Kylas Summer Campaign Activity',
              value: 120
            },
            {
              id: 2,
              name: 'Kylas Diwali Campaign Activity',
              value: 90
            }
          ],
          id: null,
          name: '2025-06-02T18:30:00.000Z',
          value: null
        },
        {
          'Kylas Diwali Campaign Activity_2': 110,
          'Kylas Summer Campaign Activity_1': 160,
          dimension: [
            {
              id: 1,
              name: 'Kylas Summer Campaign Activity',
              value: 160
            },
            {
              id: 2,
              name: 'Kylas Diwali Campaign Activity',
              value: 110
            }
          ],
          id: null,
          name: '2025-06-03T18:30:00.000Z',
          value: null
        }
      ] as unknown as CampaignAnalyticsData[],
      dimensions: new Map([
        [
          'Kylas Summer Campaign Activity_1',
          {
            color: '#FF7557',
            name: 'Kylas Summer Campaign Activity'
          }
        ],
        [
          'Kylas Diwali Campaign Activity_2',
          {
            color: '#7856FF',
            name: 'Kylas Diwali Campaign Activity'
          }
        ]
      ])
    });

    // when dimension is not present in response data
    expect(formatResponseForLineChartReport(mockGetCampaigAnalyticsDataWithMultiDimensional.map(v => ({ ...v, dimension: [] })), true, [{  id: 1, name: 'Kylas Summer Campaign Activity' }, { id: 2, name: 'Kylas Diwali Campaign Activity' }], '#006DEE')).toEqual({
      data: [
        {
          'Kylas Diwali Campaign Activity_2': 0,
          'Kylas Summer Campaign Activity_1': 0,
          dimension: [],
          id: null,
          name: '2025-06-01T18:30:00.000Z',
          value: null
        },
        {
          'Kylas Diwali Campaign Activity_2': 0,
          'Kylas Summer Campaign Activity_1': 0,
          dimension: [],
          id: null,
          name: '2025-06-02T18:30:00.000Z',
          value: null
        },
        {
          'Kylas Diwali Campaign Activity_2': 0,
          'Kylas Summer Campaign Activity_1': 0,
          dimension: [],
          id: null,
          name: '2025-06-03T18:30:00.000Z',
          value: null
        }
      ] as unknown as CampaignAnalyticsData[],
      dimensions: new Map([
        [
          'Kylas Summer Campaign Activity_1',
          {
            color: '#FF7557',
            name: 'Kylas Summer Campaign Activity'
          }
        ],
        [
          'Kylas Diwali Campaign Activity_2',
          {
            color: '#7856FF',
            name: 'Kylas Diwali Campaign Activity'
          }
        ]
      ])
    });
  });
});

import * as React from 'react';

import { IdName } from '../../shared/KanbanBoard/model';
import { CampaignActivityStatus, CampaignStatus, CampaignActionType, CampaignAnalyticsData, CampaignDimension } from './model';

import { LINE_CHART_COLORS } from './constants';
import { isBlank } from '../../../utils/globalUtil';
import { entities, ReferrerFlow } from '../../../utils/constants';
import { capitalizeLabel, routeToEntity, routeToEntitySingular } from '../../../utils/entityUtils';

export const getInitialFormValuesForCampaign = (tenantCurrencyId: number) => {
  return {
    status: CampaignStatus.DRAFT,
    actualExpense: { currencyId: tenantCurrencyId, value: 0 }
  };
};

export const getInitialFormValuesForCampaignActivity = (defaultCurrencyId: number) => {
  return {
    status: CampaignStatus.DRAFT,
    estimatedBudget: { currencyId: defaultCurrencyId, value: null },
    actualExpense: { currencyId: defaultCurrencyId, value: null }
  };
};

export const getAvailableActionsForCampaignAsPerStatus = (status: CampaignStatus) => {
  const options = [];

  switch(status) {
    case CampaignStatus.DRAFT:
      options.push(CampaignActionType.START);
      break;

    case CampaignStatus.IN_PROGRESS:
      options.push(CampaignActionType.PAUSE, CampaignActionType.COMPLETE);
      break;

    case CampaignStatus.PAUSED:
      options.push(CampaignActionType.RESUME, CampaignActionType.COMPLETE);
      break;
  }

  return options;
};

export const getAvailableActionsForCampaignActivityAsPerStatus = (status: CampaignActivityStatus) => {
  const options = [];

  switch(status) {
    case CampaignActivityStatus.DRAFT:
    case CampaignActivityStatus.FAILED:
      options.push(CampaignActionType.START);
      break;

    case CampaignActivityStatus.QUEUED:
    case CampaignActivityStatus.IN_PROGRESS:
      options.push(CampaignActionType.PAUSE, CampaignActionType.COMPLETE);
      break;

    case CampaignActivityStatus.PAUSED:
      options.push(CampaignActionType.RESUME, CampaignActionType.COMPLETE);
      break;

    case CampaignActivityStatus.PROCESSED:
      options.push(CampaignActionType.COMPLETE);
      break;
  }

  return options;
};

export const getCampaignActionName = (action: CampaignActionType) => {
  switch(action) {
    case CampaignActionType.START:
      return 'started';
    case CampaignActionType.PAUSE:
      return 'paused';
    case CampaignActionType.RESUME:
      return 'resumed';
    case CampaignActionType.COMPLETE:
      return 'completed';
  }
};

export const getConfirmModalPropsAsPerCampaignActionType = (entity: string, name: string, actionType: string | CampaignActionType) => {
  if(entity === entities.CAMPAIGNS) {
    switch(actionType) {
      case 'delete_campaign':
        return {
          title: `Delete ${capitalizeLabel(routeToEntitySingular(entities.CAMPAIGNS))}`,
          message: <span>This action cannot be undone. Are you sure, you want to delete <b className="text-break">{name}</b>?</span>,
          confirmBtn: { label: 'Delete', className: 'btn-danger' }
        };

      case CampaignActionType.START:
        return {
          title: `Start ${capitalizeLabel(routeToEntitySingular(entities.CAMPAIGNS))}`,
          message:
            <div>
              <div>You are about to launch this {routeToEntitySingular(entities.CAMPAIGNS)}, which might include multiple activities. Once launched, the {routeToEntitySingular(entities.CAMPAIGNS)} will start executing its activities.</div>
              <div className="mt-2">Do you want to proceed?</div>
            </div>,
          confirmBtn: { label: 'Yes, Start', className: 'btn-primary' }
        };

      case CampaignActionType.PAUSE:
        return {
          title: `Pause ${capitalizeLabel(routeToEntitySingular(entities.CAMPAIGNS))}`,
          message:
            <div>
              <div>Are you sure, you want to pause this {routeToEntitySingular(entities.CAMPAIGNS)}. Please note, that you can resume the journey anytime you like.</div>
              <div className="mt-2">Do you want to proceed?</div>
            </div>,
          confirmBtn: { label: 'Pause', className: 'btn-primary' }
        };

      case CampaignActionType.RESUME:
        return {
          title: `Resume ${capitalizeLabel(routeToEntitySingular(entities.CAMPAIGNS))}`,
          message:
            <div>
              <div>You are about to resume this {routeToEntitySingular(entities.CAMPAIGNS)}. All eligible activities will continue from where they were paused.</div>
              <div className="mt-2">Do you want to proceed?</div>
            </div>,
          confirmBtn: { label: 'Resume', className: 'btn-primary' }
        };

      case CampaignActionType.COMPLETE:
        return {
          title: `Complete ${capitalizeLabel(routeToEntitySingular(entities.CAMPAIGNS))}`,
          message:
            <div>
              <div>You are about to complete <b className="text-break">{name}</b> {routeToEntitySingular(entities.CAMPAIGNS)}. All ongoing activities under this {routeToEntitySingular(entities.CAMPAIGNS)} will also be marked as complete.</div>
              <div className="mt-2">Do you want to proceed?</div>
            </div>,
          confirmBtn: { label: 'Complete Campaign', className: 'btn-primary' }
        };
    }
  }

  if(entity === entities.CAMPAIGN_ACTIVITIES) {
    switch(actionType) {
      case 'delete_campaign_activity':
        return {
          title: 'Delete Activity',
          message: <span>This action cannot be undone. Are you sure, you want to delete <b className="text-break">{name}</b>?</span>,
          confirmBtn: { label: 'Delete', className: 'btn-danger' }
        };

      case CampaignActionType.START:
        return {
          title: 'Start Activity',
          message:
            <div>
              <div>Starting this activity under this {routeToEntity(entities.CAMPAIGNS)} will start the {routeToEntity(entities.CAMPAIGNS)} (if not started). All other activities under this {routeToEntity(entities.CAMPAIGNS)} will remain in their original state.</div>
              <div className="mt-2">Are you sure you want to <b>Start</b> the activity?</div>
            </div>,
          confirmBtn: { label: 'Yes, Start', className: 'btn-primary' }
        };

      case CampaignActionType.PAUSE:
        return {
          title: 'Pause Activity',
          message:
            <div>
              <div>Are you sure, you want to pause this activity. Please note, that you can resume the activity anytime you like.</div>
              <div className="mt-2">Do you want to proceed?</div>
            </div>,
          confirmBtn: { label: 'Pause', className: 'btn-primary' }
        };

      case CampaignActionType.RESUME:
        return {
          title: 'Resume Activity',
          message:
            <div>
              <div>You are about to resume this activity. The {routeToEntity(entities.CAMPAIGNS)} will also resume (if paused) and other activities will remain in there original state.</div>
              <div className="mt-2">Do you want to proceed?</div>
            </div>,
          confirmBtn: { label: 'Resume', className: 'btn-primary' }
        };

      case CampaignActionType.COMPLETE:
        return {
          title: 'Complete Activity',
          message:
            <div>
              <div>You are about to complete the <b className="text-break">{name}</b> activity. Are you sure you want to <b>Complete</b> this activity?</div>
            </div>,
          confirmBtn: { label: 'Complete Activity', className: 'btn-primary' }
        };
    }
  }
};

export const getRequiredFormValuesToShowOnDetailsPage = (entity: string) => {
  if(entity === entities.CAMPAIGNS) {
    return {
      'Campaign Details': [
        'id',
        'status',
        'startDate',
        'endDate',
        'createdAt',
        'createdBy',
        'updatedAt',
        'updatedBy',
        'startedAt',
        'startedBy',
        'lastPausedAt',
        'lastPausedBy',
        'lastResumedAt',
        'lastResumedBy',
        'endedAt',
        'endedBy'
      ],
      'UTM Details': [
        'utmCampaign',
        'utmSource',
        'utmMedium',
        'utmContent',
        'utmTerm'
      ]
    };
  }

  return {
    'Activity Details': [
      'id',
      'name',
      'type',
      'status',
      'startDate',
      'endDate',
      'entity',
      'sentTo',
      'connectedAccount',
      'whatsappTemplate',
      'createdAt',
      'createdBy',
      'updatedAt',
      'updatedBy',
      'startedAt',
      'startedBy',
      'lastPausedAt',
      'lastPausedBy',
      'lastResumedAt',
      'lastResumedBy',
      'endedAt',
      'endedBy'
    ],
    'UTM Details': [
      'utmCampaign',
      'utmSource',
      'utmMedium',
      'utmContent',
      'utmTerm'
    ]
  };
};

export const getRedirectionUrlForCampaignRelatedForms = (entity: string, campaignId: number, referrer: ReferrerFlow) => {
  switch(referrer) {
    case ReferrerFlow.CAMPAIGN_DETAILS:
      return `/sales/${entities.CAMPAIGNS}/details/${campaignId}`;
    case ReferrerFlow.CAMPAIGN_FORM:
      return (entity === entities.CAMPAIGNS) ? `/sales/${entities.CAMPAIGNS}/view/${campaignId}` : `/sales/${entities.CAMPAIGNS}/details/${campaignId}`;
    case ReferrerFlow.CAMPAIGN_ACTIVITY_LIST:
      return `/sales/${entities.CAMPAIGNS}/activities/list`;
    default:
      return `/sales/${entities.CAMPAIGNS}/list`;
  }
};

export const isCampaignDisabled = (status: CampaignStatus) => (status !== CampaignStatus.DRAFT);

export const isCampaignActivityDisabled = (status: CampaignActivityStatus) => ![CampaignActivityStatus.DRAFT, CampaignActivityStatus.FAILED].includes(status);

export const shouldShowRecipientStatus = (status: CampaignActivityStatus) => ![CampaignActivityStatus.DRAFT, CampaignActivityStatus.QUEUED, CampaignActivityStatus.FAILED].includes(status);

const generateColorGradient = (count: number) => {
  const start = { r: 185, g: 217, b: 254 };
  const end = { r: 40, g: 126, b: 254 };

  return Array.from({ length: count }, (_, i) => {
    const factor = i / Math.max(count - 1, 1);

    const r = Math.round(start.r + factor * (end.r - start.r));
    const g = Math.round(start.g + factor * (end.g - start.g));
    const b = Math.round(start.b + factor * (end.b - start.b));

    return `rgb(${r}, ${g}, ${b})`;
  });
};

export const formatResponseForFunnelReport = (campaignAnalyticsData: CampaignAnalyticsData[], dimensionValues: string[], defaultValuesForClipPath: number[]) => {
  if(isBlank(campaignAnalyticsData)) return { maxValue: 0, data: campaignAnalyticsData };

  const dataAsPerDimensions = dimensionValues.reduce((list, val) => {
    const particularDimensionData = campaignAnalyticsData.find(f => f.name === val);

    if(!isBlank(particularDimensionData)) {
      list.push(particularDimensionData);
    }

    return list;
  },                                                 []);

  const colorGradient = generateColorGradient(dataAsPerDimensions.length).reverse();

  const colorMap = new Map<string, string>(dataAsPerDimensions.map((v: CampaignAnalyticsData, i: number) => [`${v.name}_${v.value}`, colorGradient[i]]));

  return {
    maxValue: !isBlank(defaultValuesForClipPath) ? defaultValuesForClipPath[0] : Math.max(...dataAsPerDimensions.map(v => v.value)),
    data: dataAsPerDimensions.map((v: CampaignAnalyticsData, i: number) => ({
      ...v,
      color: colorMap.get(`${v.name}_${v.value}`) || 'rgb(185, 217, 254)',
      clipPathValue: !isBlank(defaultValuesForClipPath) ? defaultValuesForClipPath[i] : v.value
    }))
  };
};

export const formatResponseForLineChartReport = (campaignAnalyticsData: CampaignAnalyticsData[], isMultiLineChart: boolean, multiDimensionToRender: IdName[], strokeColor: string) => {
  const dimensions = new Map<string, { name: string, color: string }>(null);

  if(!isMultiLineChart) return { dimensions, data: campaignAnalyticsData.map(v => ({ ...v, color: strokeColor })) };

  const resultantData = campaignAnalyticsData.map((v: CampaignAnalyticsData) => {
    const result = { ...v };
    const dimensionIds = new Set<number>();

    // Add existing dimensions
    v.dimension.forEach((val: CampaignDimension) => {
      const key = `${val.name}_${val.id}`;

      result[key] = val.value;
      dimensionIds.add(val.id);

      if(!dimensions.has(key)) {
        dimensions.set(key, { name: val.name, color: LINE_CHART_COLORS[dimensions.size] });
      }
    });

     // Add missing dimensions with value 0
    multiDimensionToRender?.forEach((val: IdName) => {
      if(!dimensionIds.has(val.id)) {
        const key = `${val.name}_${val.id}`;

        result[key] = 0;

        if(!dimensions.has(key)) {
          dimensions.set(key, { name: val.name, color: LINE_CHART_COLORS[dimensions.size] });
        }
      }
    });

    return result;
  });

  return { dimensions, data: resultantData };
};

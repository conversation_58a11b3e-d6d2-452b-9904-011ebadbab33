import { getWhatsappChatbotsListingTableHeaders } from './TableHeaders';
import { FieldTypes } from '../../../FieldSettings/models/Field';

describe('getWhatsappChatbotsListingTableHeaders', () => {
  const timezone = 'Asia/Calcutta';
  const dateFormat = 'DD-MM-YYYY';
  let headers;

  beforeEach(() => {
    headers = getWhatsappChatbotsListingTableHeaders(timezone, dateFormat);
  });

  describe('Header Configuration', () => {
    it('should return correct number of headers', () => {
      expect(headers).toHaveLength(9);
    });

    it('should have all required header properties', () => {
      headers.forEach(header => {
        expect(header).toHaveProperty('id');
        expect(header).toHaveProperty('header');
        expect(header).toHaveProperty('fieldType');
        expect(header).toHaveProperty('isFilterable');
        expect(header).toHaveProperty('isSortable');
        expect(header).toHaveProperty('isStandard');
        expect(header).toHaveProperty('isDefault');
      });
    });

    it('should have correct header IDs in order', () => {
      const expectedIds = [
        'id', 'name', 'type', 'status', 'description', 
        'connectedAccount', 'trigger', 'created_at', 'updated_at'
      ];
      
      headers.forEach((header, index) => {
        expect(header.id).toBe(expectedIds[index]);
      });
    });

    it('should have correct header labels', () => {
      const expectedLabels = [
        'ID', 'Chatbot Name', 'Type', 'Status', 'Description',
        'Connected Account', 'Trigger', 'Created At', 'Updated At'
      ];
      
      headers.forEach((header, index) => {
        expect(header.header).toBe(expectedLabels[index]);
      });
    });
  });

  describe('Field Types', () => {
    it('should have correct field types', () => {
      expect(headers[0].fieldType).toBe(FieldTypes.TEXT_FIELD); // id
      expect(headers[1].fieldType).toBe(FieldTypes.TEXT_FIELD); // name
      expect(headers[2].fieldType).toBe(FieldTypes.PICK_LIST); // type
      expect(headers[3].fieldType).toBe(FieldTypes.PICK_LIST); // status
      expect(headers[4].fieldType).toBe(FieldTypes.TEXT_FIELD); // description
      expect(headers[5].fieldType).toBe(FieldTypes.LOOK_UP); // connectedAccount
      expect(headers[6].fieldType).toBe(FieldTypes.PICK_LIST); // trigger
      expect(headers[7].fieldType).toBe(FieldTypes.DATETIME_PICKER); // created_at
      expect(headers[8].fieldType).toBe(FieldTypes.DATETIME_PICKER); // updated_at
    });
  });

  describe('Filterable and Sortable Properties', () => {
    it('should have correct filterable properties', () => {
      const filterableHeaders = headers.filter(h => h.isFilterable);
      const nonFilterableHeaders = headers.filter(h => !h.isFilterable);
      
      expect(filterableHeaders).toHaveLength(8); // All except id
      expect(nonFilterableHeaders).toHaveLength(1); // Only id
      expect(nonFilterableHeaders[0].id).toBe('id');
    });

    it('should have correct sortable properties', () => {
      const sortableHeaders = headers.filter(h => h.isSortable);
      const nonSortableHeaders = headers.filter(h => !h.isSortable);
      
      expect(sortableHeaders).toHaveLength(3); // name, created_at, updated_at
      expect(nonSortableHeaders).toHaveLength(6);
      
      const sortableIds = sortableHeaders.map(h => h.id);
      expect(sortableIds).toContain('name');
      expect(sortableIds).toContain('created_at');
      expect(sortableIds).toContain('updated_at');
    });
  });

  describe('Default Visibility', () => {
    it('should have correct default visibility settings', () => {
      const defaultHeaders = headers.filter(h => h.isDefault);
      const hiddenHeaders = headers.filter(h => !h.isDefault);
      
      expect(defaultHeaders).toHaveLength(8); // All except updated_at
      expect(hiddenHeaders).toHaveLength(1); // Only updated_at
      expect(hiddenHeaders[0].id).toBe('updated_at');
    });
  });

  describe('Picklist Configurations', () => {
    it('should have correct type picklist values', () => {
      const typeHeader = headers.find(h => h.id === 'type');
      expect(typeHeader.picklist).toBeDefined();
      expect(typeHeader.picklist.picklistValues).toHaveLength(2);
      
      const typeValues = typeHeader.picklist.picklistValues.map(v => v.id);
      expect(typeValues).toContain('AI');
      expect(typeValues).toContain('RULE_BASED');
    });

    it('should have correct status picklist values', () => {
      const statusHeader = headers.find(h => h.id === 'status');
      expect(statusHeader.picklist).toBeDefined();
      expect(statusHeader.picklist.picklistValues).toHaveLength(3);
      
      const statusValues = statusHeader.picklist.picklistValues.map(v => v.id);
      expect(statusValues).toContain('ACTIVE');
      expect(statusValues).toContain('INACTIVE');
      expect(statusValues).toContain('DRAFT');
    });

    it('should have correct trigger picklist values', () => {
      const triggerHeader = headers.find(h => h.id === 'trigger');
      expect(triggerHeader.picklist).toBeDefined();
      expect(triggerHeader.picklist.picklistValues).toHaveLength(3);
      
      const triggerValues = triggerHeader.picklist.picklistValues.map(v => v.id);
      expect(triggerValues).toContain('NEW_ENTITY');
      expect(triggerValues).toContain('KEYWORD');
      expect(triggerValues).toContain('MANUAL');
    });
  });

  describe('Lookup Configuration', () => {
    it('should have correct lookup configuration for connected account', () => {
      const accountHeader = headers.find(h => h.id === 'connectedAccount');
      expect(accountHeader.lookup).toBeDefined();
      expect(accountHeader.lookup.entity).toBe('messages');
      expect(accountHeader.lookup.lookupUrl).toBe('/messages/connected-accounts/lookup?q=');
      expect(accountHeader.showDefaultOptions).toBe(true);
    });
  });

  describe('Formatted Values', () => {
    it('should format type values correctly', () => {
      const typeHeader = headers.find(h => h.id === 'type');
      const formattedValue = typeHeader.formattedValue({ type: 'AI' });
      expect(formattedValue).toBe('Ai');
    });

    it('should format status values with badges', () => {
      const statusHeader = headers.find(h => h.id === 'status');
      
      const activeStatus = statusHeader.formattedValue({ status: 'ACTIVE' });
      expect(activeStatus.props.className).toContain('badge-success');
      
      const inactiveStatus = statusHeader.formattedValue({ status: 'INACTIVE' });
      expect(inactiveStatus.props.className).toContain('badge-secondary');
      
      const draftStatus = statusHeader.formattedValue({ status: 'DRAFT' });
      expect(draftStatus.props.className).toContain('badge-warning');
    });

    it('should format description with truncation', () => {
      const descriptionHeader = headers.find(h => h.id === 'description');
      
      const shortDescription = descriptionHeader.formattedValue({ description: 'Short desc' });
      expect(shortDescription).toBe('Short desc');
      
      const longDescription = descriptionHeader.formattedValue({ 
        description: 'This is a very long description that should be truncated after 50 characters to maintain readability'
      });
      expect(longDescription).toBe('This is a very long description that should be tru...');
      
      const noDescription = descriptionHeader.formattedValue({ description: null });
      expect(noDescription).toBe('-');
    });

    it('should format connected account display name', () => {
      const accountHeader = headers.find(h => h.id === 'connectedAccount');
      
      const withAccount = accountHeader.formattedValue({ 
        connectedAccount: { displayName: 'Sales Team' }
      });
      expect(withAccount).toBe('Sales Team');
      
      const withoutAccount = accountHeader.formattedValue({ connectedAccount: null });
      expect(withoutAccount).toBe('-');
    });

    it('should format trigger values correctly', () => {
      const triggerHeader = headers.find(h => h.id === 'trigger');
      
      const newEntityTrigger = triggerHeader.formattedValue({ trigger: 'NEW_ENTITY' });
      expect(newEntityTrigger).toBe('New Entity');
      
      const keywordTrigger = triggerHeader.formattedValue({ trigger: 'KEYWORD' });
      expect(keywordTrigger).toBe('Keyword');
    });
  });

  describe('Date Formatting', () => {
    it('should format created_at dates correctly', () => {
      const createdAtHeader = headers.find(h => h.id === 'created_at');
      
      // Mock formatDate function behavior
      const mockDate = '2024-01-15T10:30:00Z';
      const formattedValue = createdAtHeader.formattedValue({ created_at: mockDate });
      
      // The actual formatting depends on the formatDate utility
      expect(typeof formattedValue).toBe('string');
    });

    it('should format updated_at dates correctly', () => {
      const updatedAtHeader = headers.find(h => h.id === 'updated_at');
      
      const mockDate = '2024-01-16T11:30:00Z';
      const formattedValue = updatedAtHeader.formattedValue({ updated_at: mockDate });
      
      expect(typeof formattedValue).toBe('string');
    });
  });
});

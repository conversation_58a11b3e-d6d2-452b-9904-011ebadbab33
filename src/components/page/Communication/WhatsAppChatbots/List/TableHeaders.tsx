import * as React from 'react';

import { FieldTypes } from '../../../FieldSettings/models/Field';
import { capitalizeLabel } from '../../../../../utils/entityUtils';
import { formatDate } from '../../../../../utils/dateUtils';

export const getWhatsappChatbotsListingTableHeaders = (timezone: string, dateFormat: string) => [
  {
    id: 'id',
    header: 'ID',
    fieldType: FieldTypes.TEXT_FIELD,
    isFilterable: false,
    isSortable: false,
    isStandard: true,
    isDefault: true
  },
  {
    id: 'name',
    header: 'Chatbot Name',
    fieldType: FieldTypes.TEXT_FIELD,
    isFilterable: true,
    isSortable: true,
    isStandard: true,
    isDefault: true
  },
  {
    id: 'type',
    header: 'Type',
    fieldType: FieldTypes.PICK_LIST,
    isFilterable: true,
    isSortable: false,
    isStandard: true,
    isDefault: true,
    picklist: {
      id: 1,
      name: 'type',
      picklistValues: [
        {
          id: 'AI',
          name: 'AI',
          displayName: 'AI',
          systemDefault: true
        },
        {
          id: 'RULE_BASED',
          name: 'RULE_BASED',
          displayName: 'Rule Based',
          systemDefault: true
        }
      ]
    },
    formattedValue: ({ type }) => capitalizeLabel(type)
  },
  {
    id: 'status',
    header: 'Status',
    fieldType: FieldTypes.PICK_LIST,
    isFilterable: true,
    isSortable: false,
    isStandard: true,
    isDefault: true,
    picklist: {
      id: 1,
      name: 'status',
      picklistValues: [
        {
          id: 'ACTIVE',
          name: 'ACTIVE',
          displayName: 'Active',
          systemDefault: true
        },
        {
          id: 'INACTIVE',
          name: 'INACTIVE',
          displayName: 'Inactive',
          systemDefault: true
        },
        {
          id: 'DRAFT',
          name: 'DRAFT',
          displayName: 'Draft',
          systemDefault: true
        }
      ]
    },
    formattedValue: ({ status }) => (
      <span className={`badge ${status === 'ACTIVE' ? 'badge-success' : status === 'INACTIVE' ? 'badge-secondary' : 'badge-warning'}`}>
        {capitalizeLabel(status)}
      </span>
    )
  },
  {
    id: 'description',
    header: 'Description',
    fieldType: FieldTypes.TEXT_FIELD,
    isFilterable: true,
    isSortable: false,
    isStandard: true,
    isDefault: true,
    formattedValue: ({ description }) => description ? (description.length > 50 ? `${description.substring(0, 50)}...` : description) : '-'
  },
  {
    id: 'connectedAccount',
    header: 'Connected Account',
    fieldType: FieldTypes.LOOK_UP,
    isFilterable: true,
    isSortable: false,
    isStandard: true,
    isDefault: true,
    showDefaultOptions: true,
    lookup: {
      entity: 'messages',
      lookupUrl: '/messages/connected-accounts/lookup?q='
    },
    formattedValue: ({ connectedAccount }) => connectedAccount?.displayName || '-'
  },
  {
    id: 'trigger',
    header: 'Trigger',
    fieldType: FieldTypes.PICK_LIST,
    isFilterable: true,
    isSortable: false,
    isStandard: true,
    isDefault: true,
    picklist: {
      id: 1,
      name: 'trigger',
      picklistValues: [
        {
          id: 'NEW_ENTITY',
          name: 'NEW_ENTITY',
          displayName: 'New Entity',
          systemDefault: true
        },
        {
          id: 'KEYWORD',
          name: 'KEYWORD',
          displayName: 'Keyword',
          systemDefault: true
        },
        {
          id: 'MANUAL',
          name: 'MANUAL',
          displayName: 'Manual',
          systemDefault: true
        }
      ]
    },
    formattedValue: ({ trigger }) => capitalizeLabel(trigger?.replace('_', ' '))
  },
  {
    id: 'created_at',
    header: 'Created At',
    fieldType: FieldTypes.DATETIME_PICKER,
    isFilterable: true,
    isSortable: true,
    isStandard: true,
    isDefault: true,
    formattedValue: ({ created_at }) => formatDate(created_at, dateFormat, timezone)
  },
  {
    id: 'updated_at',
    header: 'Updated At',
    fieldType: FieldTypes.DATETIME_PICKER,
    isFilterable: true,
    isSortable: true,
    isStandard: true,
    isDefault: false,
    formattedValue: ({ updated_at }) => formatDate(updated_at, dateFormat, timezone)
  }
];

import * as React from 'react';
import { shallow } from 'enzyme';
import to<PERSON><PERSON> from 'enzyme-to-json';

import { WhatsAppChatbotsList } from './WhatsAppChatbotsList';
import { SortOrder } from '../../../../shared/ListingTable/models/ListingTable';

describe('WhatsAppChatbotsList', () => {
  const defaultProps = {
    history: { push: jest.fn() },
    error: null,
    loading: false,
    data: {
      content: [
        {
          id: 'chatbot-1',
          name: 'Customer Support Bot',
          type: 'AI',
          description: 'Handles customer inquiries',
          status: 'ACTIVE',
          welcomeMessage: 'Hello! How can I help you?',
          thankYouMessage: 'Thank you for contacting us!',
          connectedAccount: {
            displayName: 'Sales Team',
            entityType: 'LEAD',
            accountId: 12345
          },
          trigger: 'NEW_ENTITY',
          created_at: '2024-01-15T10:30:00Z',
          updated_at: '2024-01-15T10:30:00Z',
          recordActions: {
            read: true,
            update: true,
            delete: false
          }
        }
      ],
      totalElements: 1,
      totalPages: 1,
      page: {
        no: 1,
        size: 10
      }
    },
    page: { 'whatsapp-chatbots': 1 },
    timezone: 'Asia/Calcutta',
    dateFormat: 'DD-MM-YYYY',
    fetchData: jest.fn(),
    filters: [],
    currentUserId: 1,
    profilePermissions: [
      {
        id: 1,
        name: 'whatsappChatbots',
        action: {
          read: true,
          write: true,
          update: true,
          delete: false
        }
      }
    ],
    updateSelectedPage: jest.fn(),
    setSelectedRecordAction: jest.fn()
  };

  let wrapper;

  beforeEach(() => {
    wrapper = shallow(<WhatsAppChatbotsList {...defaultProps} />);
  });

  it('should render the component', () => {
    expect(toJson(wrapper)).toMatchSnapshot();
  });

  it('should fetch chatbots on mount when data is not available', () => {
    const propsWithoutData = {
      ...defaultProps,
      data: null
    };

    shallow(<WhatsAppChatbotsList {...propsWithoutData} />);

    expect(propsWithoutData.fetchData).toHaveBeenCalledWith({
      filters: [],
      history: propsWithoutData.history,
      page: { 'whatsapp-chatbots': 1 },
      searchText: '',
      sortByField: 'updated_at',
      sortOrder: SortOrder.DESC
    });
  });

  it('should update sort order and fetch data', () => {
    wrapper.find('Connect(ListingTable)').props().updateSortOrder('created_at', 'asc');

    expect(defaultProps.fetchData).toHaveBeenCalledWith({
      filters: [],
      history: defaultProps.history,
      page: { 'whatsapp-chatbots': 1 },
      searchText: '',
      sortByField: 'created_at',
      sortOrder: 'asc'
    });
  });

  it('should handle text search', () => {
    const searchText = 'Customer Support';
    wrapper.find('Connect(ListActions)').props().onTextSearch(searchText);

    expect(defaultProps.fetchData).toHaveBeenCalledWith({
      filters: [],
      history: defaultProps.history,
      page: { 'whatsapp-chatbots': 1 },
      searchText: 'Customer Support',
      sortByField: 'updated_at',
      sortOrder: SortOrder.DESC
    });
  });

  it('should update current page', () => {
    wrapper.find('Connect(ListingTable)').props().updateCurrentPage(2);

    expect(defaultProps.updateSelectedPage).toHaveBeenCalledWith('whatsapp-chatbots', 2);
    expect(defaultProps.fetchData).toHaveBeenCalledWith({
      filters: [],
      history: defaultProps.history,
      page: { 'whatsapp-chatbots': 2 },
      searchText: '',
      sortByField: 'updated_at',
      sortOrder: SortOrder.DESC
    });
  });
});

import * as React from 'react';
import { shallow, mount } from 'enzyme';
import to<PERSON><PERSON> from 'enzyme-to-json';

import { WhatsAppChatbotsList } from './WhatsAppChatbotsList';

describe('WhatsAppChatbotsList', () => {
  const mockChatbots = [
    {
      id: 'chatbot-1',
      name: 'Customer Support Bot',
      type: 'AI',
      description: 'Handles customer inquiries and support requests',
      status: 'ACTIVE',
      welcomeMessage: 'Hello! How can I help you?',
      thankYouMessage: 'Thank you for contacting us!',
      connectedAccount: {
        displayName: 'Sales Team',
        entityType: 'LEAD',
        accountId: 12345
      },
      trigger: 'NEW_ENTITY',
      created_at: '2024-01-15T10:30:00Z',
      updated_at: '2024-01-15T10:30:00Z',
      createdBy: {
        id: 1,
        name: '<PERSON>'
      },
      updatedBy: {
        id: 2,
        name: '<PERSON>'
      }
    },
    {
      id: 'chatbot-2',
      name: 'Lead Qualification Bot',
      type: 'RULE_BASED',
      description: 'Qualifies incoming leads automatically',
      status: 'INACTIVE',
      welcomeMessage: 'Welcome! Let me help qualify your inquiry.',
      thankYouMessage: 'Thanks for your information!',
      connectedAccount: {
        displayName: 'Marketing Team',
        entityType: 'LEAD',
        accountId: 12346
      },
      trigger: 'KEYWORD',
      created_at: '2024-01-10T08:15:00Z',
      updated_at: '2024-01-14T16:45:00Z',
      createdBy: {
        id: 3,
        name: 'Mike Johnson'
      },
      updatedBy: {
        id: 1,
        name: 'John Doe'
      }
    },
    {
      id: 'chatbot-3',
      name: 'Order Status Bot',
      type: 'AI',
      description: 'Provides order status updates to customers',
      status: 'DRAFT',
      welcomeMessage: 'Hi! I can help you check your order status.',
      thankYouMessage: 'Have a great day!',
      connectedAccount: {
        displayName: 'Support Team',
        entityType: 'CONTACT',
        accountId: 12347
      },
      trigger: 'MANUAL',
      created_at: '2024-01-12T14:20:00Z',
      updated_at: '2024-01-16T11:30:00Z',
      createdBy: {
        id: 2,
        name: 'Jane Smith'
      },
      updatedBy: {
        id: 3,
        name: 'Mike Johnson'
      }
    }
  ];

  const defaultProps = {
    history: { push: jest.fn() },
    error: null,
    loading: false,
    data: mockChatbots,
    timezone: 'Asia/Calcutta',
    dateFormat: 'DD-MM-YYYY',
    fetchData: jest.fn(),
    profilePermissions: [
      {
        id: 1,
        name: 'whatsappChatbots',
        action: {
          read: true,
          write: true,
          update: true,
          delete: false
        }
      }
    ]
  };

  let wrapper;

  beforeEach(() => {
    jest.clearAllMocks();
    wrapper = shallow(<WhatsAppChatbotsList {...defaultProps} />);
  });

  describe('Component Rendering', () => {
    it('should render the component', () => {
      expect(toJson(wrapper)).toMatchSnapshot();
    });

    it('should render SetupLayout wrapper', () => {
      expect(wrapper.find('SetupLayout')).toHaveLength(1);
    });

    it('should render page header with title', () => {
      expect(wrapper.find('.page-title-wrapper h1').text()).toContain('WhatsApp Chatbots');
    });

    it('should render Create Chatbot button', () => {
      const createButton = wrapper.find('.btn-primary');
      expect(createButton.text()).toBe('Create Chatbot');
    });
  });

  describe('Data Fetching', () => {
    it('should fetch chatbots on mount when data is not available', () => {
      const propsWithoutData = {
        ...defaultProps,
        data: null
      };

      shallow(<WhatsAppChatbotsList {...propsWithoutData} />);
      expect(propsWithoutData.fetchData).toHaveBeenCalled();
    });

    it('should not fetch data if data is already available', () => {
      expect(defaultProps.fetchData).not.toHaveBeenCalled();
    });
  });

  describe('Loading State', () => {
    it('should show loading skeleton when loading is true', () => {
      const loadingProps = {
        ...defaultProps,
        loading: true
      };

      const loadingWrapper = shallow(<WhatsAppChatbotsList {...loadingProps} />);
      expect(loadingWrapper.find('.chatbots-loading-section')).toHaveLength(1);
      expect(loadingWrapper.find('Skeleton')).toHaveLength(3);
    });
  });

  describe('Empty State', () => {
    it('should show empty state when no chatbots are available', () => {
      const emptyProps = {
        ...defaultProps,
        data: []
      };

      const emptyWrapper = shallow(<WhatsAppChatbotsList {...emptyProps} />);
      expect(emptyWrapper.find('.no-chatbots-message')).toHaveLength(1);
      expect(emptyWrapper.find('.empty-state h3').text()).toBe('No Chatbots Found');
    });

    it('should show chatbot icon in empty state', () => {
      const emptyProps = {
        ...defaultProps,
        data: []
      };

      const emptyWrapper = shallow(<WhatsAppChatbotsList {...emptyProps} />);
      const icon = emptyWrapper.find('.empty-state i');
      expect(icon.hasClass('fa-message-bot')).toBe(true);
      expect(icon.prop('style')).toEqual({ color: '#07835e', fontSize: '3rem' });
    });
  });

  describe('Chatbot Cards Rendering', () => {
    it('should render correct number of chatbot cards', () => {
      expect(wrapper.find('.chatbot-card')).toHaveLength(mockChatbots.length);
    });

    it('should render chatbot names correctly', () => {
      const chatbotNames = wrapper.find('.chatbot-display-name');
      expect(chatbotNames.at(0).text()).toBe('Customer Support Bot');
      expect(chatbotNames.at(1).text()).toBe('Lead Qualification Bot');
      expect(chatbotNames.at(2).text()).toBe('Order Status Bot');
    });

    it('should render chatbot header icon', () => {
      const headerIcons = wrapper.find('.header .icon img');
      expect(headerIcons).toHaveLength(mockChatbots.length);
      headerIcons.forEach(icon => {
        expect(icon.prop('alt')).toBe('Not Found');
      });
    });

    it('should render MultiActionModal for each chatbot', () => {
      expect(wrapper.find('MultiActionModal')).toHaveLength(mockChatbots.length);
    });
  });

  describe('Chatbot Info Grid', () => {
    it('should render info grid for each chatbot', () => {
      expect(wrapper.find('.info-grid')).toHaveLength(mockChatbots.length);
    });

    it('should render TYPE column with correct labels and values', () => {
      const typeLabels = wrapper.find('.info-grid .info-item').filterWhere(item =>
        item.find('.info-label').text() === 'TYPE'
      );
      expect(typeLabels).toHaveLength(mockChatbots.length);

      const typeTexts = typeLabels.find('.type-text');
      expect(typeTexts.at(0).text()).toBe('Ai');
      expect(typeTexts.at(1).text()).toBe('Rule Based');
      expect(typeTexts.at(2).text()).toBe('Ai');
    });

    it('should render AI stars icon for AI chatbots', () => {
      const aiTypeItems = wrapper.find('.info-grid .info-item').filterWhere(item =>
        item.find('.info-label').text() === 'TYPE' && item.find('.ai-icon').length > 0
      );
      expect(aiTypeItems).toHaveLength(2); // Two AI chatbots in mock data

      aiTypeItems.forEach(item => {
        const aiIcon = item.find('.ai-icon');
        expect(aiIcon.prop('alt')).toBe('AI');
        expect(aiIcon.hasClass('type-icon')).toBe(true);
      });
    });

    it('should render circle icon for non-AI chatbots', () => {
      const nonAiTypeItems = wrapper.find('.info-grid .info-item').filterWhere(item =>
        item.find('.info-label').text() === 'TYPE' && item.find('.type-indicator').length > 0
      );
      expect(nonAiTypeItems).toHaveLength(1); // One RULE_BASED chatbot in mock data
    });

    it('should render STATUS column with correct values and styling', () => {
      const statusLabels = wrapper.find('.info-grid .info-item').filterWhere(item =>
        item.find('.info-label').text() === 'STATUS'
      );
      expect(statusLabels).toHaveLength(mockChatbots.length);

      const statusTexts = statusLabels.find('.status-text');
      expect(statusTexts.at(0).text()).toBe('Active');
      expect(statusTexts.at(1).text()).toBe('Inactive');
      expect(statusTexts.at(2).text()).toBe('Draft');

      // Check status indicator classes
      const statusIndicators = statusLabels.find('.status-indicator');
      expect(statusIndicators.at(0).hasClass('active')).toBe(true);
      expect(statusIndicators.at(1).hasClass('inactive')).toBe(true);
      expect(statusIndicators.at(2).hasClass('draft')).toBe(true);
    });

    it('should render DESCRIPTION column with correct values', () => {
      const descriptionLabels = wrapper.find('.info-grid .info-item').filterWhere(item =>
        item.find('.info-label').text() === 'DESCRIPTION'
      );
      expect(descriptionLabels).toHaveLength(mockChatbots.length);

      const descriptionTexts = descriptionLabels.find('.description-text');
      expect(descriptionTexts.at(0).text()).toBe('Handles customer inquiries and support requests');
      expect(descriptionTexts.at(1).text()).toBe('Qualifies incoming leads automatically');
      expect(descriptionTexts.at(2).text()).toBe('Provides order status updates to customers');
    });

    it('should render CONNECTED ACCOUNT column with WhatsApp business icon', () => {
      const accountLabels = wrapper.find('.info-grid .info-item').filterWhere(item =>
        item.find('.info-label').text() === 'CONNECTED ACCOUNT'
      );
      expect(accountLabels).toHaveLength(mockChatbots.length);

      const accountIcons = accountLabels.find('.whatsapp-business-icon');
      expect(accountIcons).toHaveLength(mockChatbots.length);
      accountIcons.forEach(icon => {
        expect(icon.prop('alt')).toBe('WhatsApp Business');
        expect(icon.hasClass('account-icon')).toBe(true);
      });

      const accountTexts = accountLabels.find('.account-text');
      expect(accountTexts.at(0).text()).toBe('Sales Team');
      expect(accountTexts.at(1).text()).toBe('Marketing Team');
      expect(accountTexts.at(2).text()).toBe('Support Team');
    });
  });

  describe('Action Buttons', () => {
    it('should render Manage button for ACTIVE chatbots', () => {
      const activeButtons = wrapper.find('.manage-chatbot');
      expect(activeButtons).toHaveLength(1);
      expect(activeButtons.text()).toBe('Manage');
      expect(activeButtons.hasClass('btn-outline-primary')).toBe(true);
    });

    it('should render Activate button for INACTIVE chatbots', () => {
      const inactiveButtons = wrapper.find('.activate-chatbot');
      expect(inactiveButtons).toHaveLength(1);
      expect(inactiveButtons.text()).toBe('Activate');
      expect(inactiveButtons.hasClass('btn-outline-primary')).toBe(true);
    });

    it('should render Setup button for DRAFT chatbots', () => {
      const draftButtons = wrapper.find('.setup-chatbot');
      expect(draftButtons).toHaveLength(1);
      expect(draftButtons.text()).toBe('Setup');
      expect(draftButtons.hasClass('btn-outline-primary')).toBe(true);
    });

    it('should render line separator between action button and dropdown', () => {
      expect(wrapper.find('.line-separator')).toHaveLength(mockChatbots.length);
    });
  });

  describe('Timestamps', () => {
    it('should render timestamps section for each chatbot', () => {
      expect(wrapper.find('.timestamps')).toHaveLength(mockChatbots.length);
    });

    it('should render created and updated timestamps with user information', () => {
      const timestampItems = wrapper.find('.timestamp-item');
      expect(timestampItems).toHaveLength(mockChatbots.length * 4); // 4 timestamp items per chatbot

      // Check labels
      const timestampLabels = wrapper.find('.timestamp-label');
      const createdAtLabels = timestampLabels.filterWhere(label => label.text() === 'Created At:');
      const createdByLabels = timestampLabels.filterWhere(label => label.text() === 'Created By:');
      const updatedAtLabels = timestampLabels.filterWhere(label => label.text() === 'Updated At:');
      const updatedByLabels = timestampLabels.filterWhere(label => label.text() === 'Updated By:');

      expect(createdAtLabels).toHaveLength(mockChatbots.length);
      expect(createdByLabels).toHaveLength(mockChatbots.length);
      expect(updatedAtLabels).toHaveLength(mockChatbots.length);
      expect(updatedByLabels).toHaveLength(mockChatbots.length);
    });

    it('should display correct user names in Created By and Updated By fields', () => {
      const timestampValues = wrapper.find('.timestamp-value');

      // Find Created By and Updated By values for first chatbot (indices 1 and 3 in first group of 4)
      const firstChatbotCreatedBy = timestampValues.at(1);
      const firstChatbotUpdatedBy = timestampValues.at(3);

      expect(firstChatbotCreatedBy.text()).toBe('John Doe');
      expect(firstChatbotUpdatedBy.text()).toBe('Jane Smith');

      // Find Created By and Updated By values for second chatbot (indices 5 and 7 in second group of 4)
      const secondChatbotCreatedBy = timestampValues.at(5);
      const secondChatbotUpdatedBy = timestampValues.at(7);

      expect(secondChatbotCreatedBy.text()).toBe('Mike Johnson');
      expect(secondChatbotUpdatedBy.text()).toBe('John Doe');
    });
  });

  describe('MultiActionModal Options', () => {
    it('should provide View Details option for each chatbot', () => {
      const multiActionModals = wrapper.find('MultiActionModal');

      multiActionModals.forEach((modal) => {
        const options = modal.prop('options');
        expect(options).toHaveLength(1);
        expect(options[0].label).toBe('View Details');
        expect(typeof options[0].action).toBe('function');
      });
    });

    it('should log chatbot ID when View Details is clicked', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      const firstModal = wrapper.find('MultiActionModal').at(0);
      const options = firstModal.prop('options');

      // Execute the View Details action
      options[0].action();

      expect(consoleSpy).toHaveBeenCalledWith('View chatbot details:', 'chatbot-1');

      consoleSpy.mockRestore();
    });
  });

  describe('Data Structure Handling', () => {
    it('should handle data as array', () => {
      const arrayDataProps = {
        ...defaultProps,
        data: mockChatbots
      };

      const arrayWrapper = shallow(<WhatsAppChatbotsList {...arrayDataProps} />);
      expect(arrayWrapper.find('.chatbot-card')).toHaveLength(mockChatbots.length);
    });

    it('should handle data with content property', () => {
      const contentDataProps = {
        ...defaultProps,
        data: { content: mockChatbots }
      };

      const contentWrapper = shallow(<WhatsAppChatbotsList {...contentDataProps} />);
      expect(contentWrapper.find('.chatbot-card')).toHaveLength(mockChatbots.length);
    });

    it('should handle null/undefined data gracefully', () => {
      const nullDataProps = {
        ...defaultProps,
        data: null
      };

      const nullWrapper = shallow(<WhatsAppChatbotsList {...nullDataProps} />);
      // Should show mock data when no API data is available
      expect(nullWrapper.find('.chatbot-card')).toHaveLength(3); // Mock data has 3 items
    });
  });

  describe('Error Handling', () => {
    it('should handle missing connected account gracefully', () => {
      const chatbotWithoutAccount = {
        ...mockChatbots[0],
        connectedAccount: null
      };

      const propsWithoutAccount = {
        ...defaultProps,
        data: [chatbotWithoutAccount]
      };

      const wrapperWithoutAccount = shallow(<WhatsAppChatbotsList {...propsWithoutAccount} />);
      const accountText = wrapperWithoutAccount.find('.account-text');
      expect(accountText.text()).toBe('Not connected');
    });

    it('should handle missing description gracefully', () => {
      const chatbotWithoutDescription = {
        ...mockChatbots[0],
        description: null
      };

      const propsWithoutDescription = {
        ...defaultProps,
        data: [chatbotWithoutDescription]
      };

      const wrapperWithoutDescription = shallow(<WhatsAppChatbotsList {...propsWithoutDescription} />);
      const descriptionText = wrapperWithoutDescription.find('.description-text');
      expect(descriptionText.text()).toBe('No description provided');
    });
  });
});

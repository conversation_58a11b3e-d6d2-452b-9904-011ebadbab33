import * as React from 'react';
import { connect } from 'react-redux';

import { isBlank } from '../../../../../utils/globalUtil';
import { ErrorObj } from '../../../../../utils/ErrorUtil';
import { entities } from '../../../../../utils/constants';
import { StateInterface } from '../../../../../store/store';
import SetupLayout from '../../../layout/SetupLayout/SetupLayout';
import { FilterRule } from '../../../../shared/ListAction/FilterList';
import ListingTable from '../../../../shared/ListingTable/ListingTable';
import { getWhatsappChatbotsListingTableHeaders } from './TableHeaders';
import { ApiStateHandler } from '../../../../shared/ApiHandler/ApiStateHandler';
import { SortOrder } from '../../../../shared/ListingTable/models/ListingTable';
import ListActions from '../../../../shared/ListingTable/ListActions/ListActions';
import { updateSelectedPageAction } from '../../../listLayout/actions/ListActions';
import Can, { Permission } from '../../../../shared/Permission/UserPermission';
import { TableLoadingSkeleton } from '../../../../shared/Table/TableLoadingSkeleton';
import { getWhatsAppChatbotsList } from '../service';
import { getProfilePermissionForEntity, UPDATE } from '../../../../../utils/permissionUtil';
import { WithApiStateHandler } from '../../../DataManagement/Components/Import/WithApiCall';
import TableErrorFallback from '../../../../shared/ListingTable/components/TableErrorFallback/TableErrorFallback';
import { WhatsAppChatbot, WhatsAppChatbotListResponse } from '../model';

import './whatsapp-chatbots-list.scss';

interface StoreProps {
  timezone: string;
  dateFormat: string;
  profilePermissions: Permission[];
  currentUserId: number;
  page: any;
  filters: FilterRule[];
}

interface OwnProps {
  history: any;
  error: ErrorObj;
  loading: boolean;
  data: WhatsAppChatbotListResponse;
  fetchData: (params: any) => void;
  updateSelectedPage: (entity: string, page: number) => void;
  setSelectedRecordAction?: (id: string) => void;
}

type Props = StoreProps & OwnProps;

export const WhatsAppChatbotsList: React.FC<Props> = ({
  history,
  error,
  loading,
  data,
  page,
  timezone,
  dateFormat,
  fetchData,
  filters,
  currentUserId,
  profilePermissions,
  updateSelectedPage,
  setSelectedRecordAction
}) => {
  const [sortByField, setSortByField] = React.useState<string>('updated_at');
  const [sortOrder, setSortOrder] = React.useState<SortOrder>(SortOrder.DESC);
  const [tableHeaders, setTableHeaders] = React.useState(getWhatsappChatbotsListingTableHeaders(timezone, dateFormat));
  const [defaultColumns, setDefaultColumns] = React.useState(getWhatsappChatbotsListingTableHeaders(timezone, dateFormat));
  const [searchedText, setSearchedText] = React.useState<string>('');

  const fetchWhatsappChatbots = (pageNumber: number, sortField?: string, order?: SortOrder, searchText?: string) => {
    pageNumber && updateSelectedPage(entities.WHATSAPP_CHATBOTS, pageNumber);
    const selectedPage = { [entities.WHATSAPP_CHATBOTS]: pageNumber || page[entities.WHATSAPP_CHATBOTS] };

    const selectedSortField = !isBlank(sortField) ? sortField : sortByField;
    const selectedSortOrder = !isBlank(order) ? order : sortOrder;

    fetchData({
      filters,
      history,
      searchText: searchText === undefined ? searchedText : searchText,
      page: selectedPage,
      sortOrder: selectedSortOrder,
      sortByField: selectedSortField
    });
  };

  React.useEffect(() => {
    if (!data) {
      fetchWhatsappChatbots(1);
    }
  }, []);

  const updateSortOrder = (field: string, order: SortOrder) => {
    setSortByField(field);
    setSortOrder(order);
    fetchWhatsappChatbots(page[entities.WHATSAPP_CHATBOTS] || 1, field, order);
  };

  const updateCurrentPage = (pageNumber: number) => {
    fetchWhatsappChatbots(pageNumber);
  };

  const onTextSearch = (searchText: string) => {
    setSearchedText(searchText);
    fetchWhatsappChatbots(1, sortByField, sortOrder, searchText);
  };

  const onColumnSelectorApply = (selectedHeaders) => {
    setTableHeaders(selectedHeaders);
  };

  const whatsappChatbotsRowOptions = (chatbot: WhatsAppChatbot) => {
    const options = [];

    // For now, we're only adding view option since create/edit functionality is not required
    options.push(
      <Can I='["read"]' ability={chatbot.recordActions}>
        <a
          onClick={() => console.log('View chatbot:', chatbot.id)}
          className="dropdown-item d-flex justify-content-between"
        >
          View Details
        </a>
      </Can>
    );

    return options;
  };

  const goToChatbotDetailsPage = ({ id }, e?: any) => {
    const shouldOpenInNewTab = e && (e.ctrlKey || e.metaKey);
    setSelectedRecordAction && setSelectedRecordAction(id);

    if (shouldOpenInNewTab) {
      window.open(`/setup/whatsapp-business/chatbots/details/${id}`, '_blank');
      return;
    }
    // For now, just log the navigation since we don't have details page yet
    console.log('Navigate to chatbot details:', id);
  };

  return (
    <SetupLayout>
        <div className="main-content-wrapper position-relative whatsapp-chatbots-list">
        <div className="page-header mb-3">
          <div className="page-title-wrapper">
            <div className="page-title">
              <h1 className="h1">WhatsApp Chatbots</h1>
            </div>
          </div>
          <ListActions
            showSearch
            history={history}
            isFilterUpdated={false}
            onTextSearch={onTextSearch}
            entity={entities.WHATSAPP_CHATBOTS}
            refreshData={updateCurrentPage}
            fetchData={fetchWhatsappChatbots}
            addButtonLabel={'Create Chatbot'}
            totalItems={data && data.totalElements}
            currentPage={page[entities.WHATSAPP_CHATBOTS] || 1}
            sort={{ order: sortOrder, field: sortByField }}
            searchTooltip={'Search by Chatbot name'}
            showColumnSelector
            onColumnSelectorApply={onColumnSelectorApply}
            headers={tableHeaders}
            filterColumns={defaultColumns}
            onClickAdd={() => console.log('Create chatbot - not implemented yet')}
            profileBasedAbility={getProfilePermissionForEntity(profilePermissions, 'whatsappChatbots')}
            currentUserId={currentUserId}
          />
        </div>
        <ApiStateHandler
          error={error}
          history={history}
          loading={loading}
          SkeletonComponent={TableLoadingSkeleton}
          ErrorFallbackComponent={TableErrorFallback}
        >
          <ListingTable
            data={data}
            history={history}
            sortOrder={sortOrder}
            sortByField={sortByField}
            updateSortOrder={updateSortOrder}
            entity={entities.WHATSAPP_CHATBOTS}
            updateCurrentPage={updateCurrentPage}
            rowClickRequiredPermission={[UPDATE]}
            currentPage={page[entities.WHATSAPP_CHATBOTS] || 1}
            headers={tableHeaders.filter(header => header.isDefault)}
            allTableColumns={tableHeaders}
            RowOptions={(chatbot: WhatsAppChatbot) => whatsappChatbotsRowOptions(chatbot)}
            onClickRow={goToChatbotDetailsPage}
          />
        </ApiStateHandler>
        </div>

    </SetupLayout>
  );
};

const mapStateToProps = (state: StateInterface) => ({
  timezone: state.loginForm.userPreferences.timezone,
  dateFormat: state.loginForm.userPreferences.dateFormat,
  profilePermissions: state.appData.profilePermissions,
  currentUserId: state.loginForm.currentUserId,
  page: state.listLayout.page,
  filters: state.listLayout.filters
});

const service = (params) => {
  const { page, filters, history, searchText, sortByField = 'updated_at', sortOrder = SortOrder.DESC } = params;

  return getWhatsAppChatbotsList(filters, searchText, history);
};

export default connect(mapStateToProps, { updateSelectedPage: updateSelectedPageAction })(WithApiStateHandler(WhatsAppChatbotsList, service));

import * as React from 'react';
import { connect } from 'react-redux';

import { isBlank } from '../../../../../utils/globalUtil';
import { ErrorObj } from '../../../../../utils/ErrorUtil';
import { entities } from '../../../../../utils/constants';
import { StateInterface } from '../../../../../store/store';
import SetupLayout from '../../../layout/SetupLayout/SetupLayout';
import { FilterRule } from '../../../../shared/ListAction/FilterList';
import { ApiStateHandler } from '../../../../shared/ApiHandler/ApiStateHandler';
import { Permission } from '../../../../shared/Permission/UserPermission';
import { getWhatsAppChatbotsList } from '../service';
import { WithApiStateHandler } from '../../../DataManagement/Components/Import/WithApiCall';
import { WhatsAppChatbot, WhatsAppChatbotListResponse } from '../model';
import { DropdownIcon } from '../../../../shared/Icons/GlobalIcons';
import MultiActionModal from '../../../../shared/Input/MultiActionModal';
import { capitalizeLabel } from '../../../../../utils/entityUtils';
import { formatDate } from '../../../../../utils/dateUtils';
import Skeleton from 'react-loading-skeleton';
import * as WhatsAppChatbotIcon from '../../../../../assets/images/WhatsApp/whatsapp-chatbot.png';
import * as AIStarsIcon from '../../../../../assets/images/AI/AI-stars-logo.svg';
import * as whatsAppBusinessIcon from '../../../../../assets/icons/whatsapp-business-icon.svg';

import './whatsapp-chatbots-list.scss';

interface StoreProps {
  timezone: string;
  dateFormat: string;
  profilePermissions: Permission[];
}

interface OwnProps {
  history: any;
  error: ErrorObj;
  loading: boolean;
  data: WhatsAppChatbot[] | { content: WhatsAppChatbot[] } | any;
  fetchData: () => void;
}

type Props = StoreProps & OwnProps;

export const WhatsAppChatbotsList: React.FC<Props> = ({
  history,
  error,
  loading,
  data,
  timezone,
  dateFormat,
  fetchData,
  profilePermissions
}) => {

  React.useEffect(() => {
    if (!data) {
      fetchData();
    }
  }, []);

  const getMultiModalOptions = (chatbot: WhatsAppChatbot) => {
    const options = [];

    // Add view details option
    options.push({
      label: 'View Details',
      action: () => {
        // For now, just log since we don't have details page
        console.log('View chatbot details:', chatbot.id);
      }
    });

    return options;
  };

  const getPrimaryAction = (chatbot: WhatsAppChatbot) => {
    if (chatbot.status === 'ACTIVE') {
      return (
        <button className="btn btn-outline-primary manage-chatbot">
          Manage
        </button>
      );
    }

    if (chatbot.status === 'INACTIVE') {
      return (
        <button className="btn btn-outline-primary activate-chatbot">
          Activate
        </button>
      );
    }

    return (
      <button className="btn btn-outline-primary setup-chatbot">
        Setup
      </button>
    );
  };

  const getChatbotsListComponent = () => {
    if (loading) {
      return (
        <div className="chatbots-loading-section">
          <div className="loader-slot">
            <Skeleton count={3} height="100px" />
          </div>
          <div className="loader-slot">
            <Skeleton count={3} height="100px" />
          </div>
          <div className="loader-slot">
            <Skeleton count={3} height="100px" />
          </div>
        </div>
      );
    }

    // Debug: Log the data structure to understand what we're receiving
    console.log('Chatbots data received:', data);

    // Handle different data structures - API might return data directly or wrapped in content
    let chatbots = Array.isArray(data) ? data : (data?.content || []);

    // If no data from API, use mock data for testing
    if (!chatbots || chatbots.length === 0) {
      chatbots = [
        {
          id: 'chatbot-1',
          name: 'Customer Support Bot',
          type: 'AI',
          description: 'Handles customer inquiries and support requests',
          status: 'ACTIVE',
          welcomeMessage: 'Hello! How can I help you?',
          thankYouMessage: 'Thank you for contacting us!',
          connectedAccount: {
            displayName: 'Sales Team',
            entityType: 'LEAD',
            accountId: 12345
          },
          trigger: 'NEW_ENTITY',
          created_at: '2024-01-15T10:30:00Z',
          updated_at: '2024-01-15T10:30:00Z'
        },
        {
          id: 'chatbot-2',
          name: 'Lead Qualification Bot',
          type: 'RULE_BASED',
          description: 'Qualifies incoming leads automatically',
          status: 'INACTIVE',
          welcomeMessage: 'Welcome! Let me help qualify your inquiry.',
          thankYouMessage: 'Thanks for your information!',
          connectedAccount: {
            displayName: 'Marketing Team',
            entityType: 'LEAD',
            accountId: 12346
          },
          trigger: 'KEYWORD',
          created_at: '2024-01-10T08:15:00Z',
          updated_at: '2024-01-14T16:45:00Z'
        },
        {
          id: 'chatbot-3',
          name: 'Order Status Bot',
          type: 'AI',
          description: 'Provides order status updates to customers',
          status: 'DRAFT',
          welcomeMessage: 'Hi! I can help you check your order status.',
          thankYouMessage: 'Have a great day!',
          connectedAccount: {
            displayName: 'Support Team',
            entityType: 'CONTACT',
            accountId: 12347
          },
          trigger: 'MANUAL',
          created_at: '2024-01-12T14:20:00Z',
          updated_at: '2024-01-16T11:30:00Z'
        }
      ];
    }

    // Only show empty state if we have no mock data and no API data
    if (!chatbots || (chatbots.length === 0 && !data)) {
      return (
        <div className="no-chatbots-message">
          <div className="empty-state">
            <i className="fa fa-solid fa-message-bot" style={{ color: '#07835e', fontSize: '3rem' }}></i>
            <h3>No Chatbots Found</h3>
            <p>You haven't created any WhatsApp chatbots yet.</p>
          </div>
        </div>
      );
    }

    return (
      <div className="chatbots-list">
        {chatbots.map((chatbot, index) => (
          <div key={index} className="chatbot-card">
            <div className="header">
              <div className="icon">
                <img src={`${WhatsAppChatbotIcon}`} alt="Not Found" />
              </div>
              <div className="header-section">
                <div className="chatbot-display-name">{chatbot.name}</div>
                <div className="actions">
                  {getPrimaryAction(chatbot)}
                  <div className="line-separator"></div>
                  <div className="options-container" data-toggle="tooltip" data-trigger="hover" title="More">
                    <MultiActionModal
                      className={'btn-down-arrow btn-primary'}
                      icon={<DropdownIcon />}
                      options={getMultiModalOptions(chatbot)}
                    />
                  </div>
                </div>
              </div>
            </div>
            <div className="body">
              <div className="chatbot-info">
                <div className="info-grid">
                  <div className="info-item">
                    <div className="info-label">TYPE</div>
                    <div className="info-value">
                      {chatbot.type === 'AI' ? (
                        <img src={`${AIStarsIcon}`} className="type-icon ai-icon" alt="AI" />
                      ) : (
                        <i className="fa fa-circle type-indicator"></i>
                      )}
                      <span className="type-text">{capitalizeLabel(chatbot.type.replace('_', ' '))}</span>
                    </div>
                  </div>

                  <div className="info-item">
                    <div className="info-label">STATUS</div>
                    <div className="info-value">
                      <i className={`fa fa-circle status-indicator ${chatbot.status.toLowerCase()}`}></i>
                      <span className="status-text">{capitalizeLabel(chatbot.status)}</span>
                    </div>
                  </div>

                  <div className="info-item description-item">
                    <div className="info-label">DESCRIPTION</div>
                    <div className="info-value">
                      <i className="fa fa-circle description-indicator"></i>
                      <span className="description-text">{chatbot.description || 'No description provided'}</span>
                    </div>
                  </div>

                  <div className="info-item">
                    <div className="info-label">CONNECTED ACCOUNT</div>
                    <div className="info-value">
                      <img src={`${whatsAppBusinessIcon}`} className="account-icon whatsapp-business-icon" alt="WhatsApp Business" />
                      <span className="account-text">{chatbot.connectedAccount?.displayName || 'Not connected'}</span>
                    </div>
                  </div>
                </div>

                <div className="timestamps">
                  <div className="timestamp-item">
                    <span className="timestamp-label">Created:</span>
                    <span className="timestamp-value">{formatDate(chatbot.created_at, dateFormat, timezone)}</span>
                  </div>
                  <div className="timestamp-item">
                    <span className="timestamp-label">Updated:</span>
                    <span className="timestamp-value">{formatDate(chatbot.updated_at, dateFormat, timezone)}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  };

  return (
    <SetupLayout>
      <div className="main-content-wrapper position-relative whatsapp-chatbots-list">
        <div className="page-header mb-3">
          <div className="page-title-wrapper chatbots-header">
            <h1 className="h1 d-flex mw-fit-content">
              <p className="text-truncate">WhatsApp Chatbots</p>
            </h1>
            <button
              className="btn btn-primary"
              onClick={() => console.log('Create chatbot - not implemented yet')}
            >
              Create Chatbot
            </button>
          </div>
        </div>
        <div className="page-content min-height-0">
          <div className="page-inner-content">
            <div className="top-wrapper min-height-0">
              <div className="right-section">
                <ApiStateHandler
                  error={error}
                  history={history}
                  loading={false}
                  ErrorFallbackComponent={() => <div>Error loading chatbots</div>}
                  SkeletonComponent={() => <div>Loading...</div>}
                >
                  {getChatbotsListComponent()}
                </ApiStateHandler>
              </div>
            </div>
          </div>
        </div>
      </div>
    </SetupLayout>
  );
};

const mapStateToProps = (state: StateInterface) => ({
  timezone: state.loginForm.userPreferences.timezone,
  dateFormat: state.loginForm.userPreferences.dateFormat,
  profilePermissions: state.appData.profilePermissions
});

const service = () => {
  return getWhatsAppChatbotsList([], '', {});
};

export default connect(mapStateToProps)(WithApiStateHandler(WhatsAppChatbotsList, service));

@import 'src/assets/styles/scss/base/variables';


  .main-content-wrapper.whatsapp-chatbots-list {

    .badge {
      font-size: 0.75rem;
      padding: 0.25rem 0.5rem;
      border-radius: 0.25rem;
      font-weight: 500;

      &.badge-success {
        background-color: $success;
        color: white;
      }

      &.badge-secondary {
        background-color: $gray-500;
        color: white;
      }

      &.badge-warning {
        background-color: $warning;
        color: white;
      }
    }

    .table {
      .chatbot-name {
        font-weight: 500;
        color: $primary;
      }

      .chatbot-description {
        color: $gray-600;
        font-size: 0.875rem;
      }

      .chatbot-type {
        font-weight: 500;
        text-transform: capitalize;
      }

      .connected-account {
        color: $gray-700;
        font-size: 0.875rem;
      }

      .trigger-type {
        color: $gray-700;
        font-size: 0.875rem;
        text-transform: capitalize;
      }
    }
  }


// Status badge specific styles
.status-badge {
  &.active {
    background-color: $success;
    color: white;
  }

  &.inactive {
    background-color: $gray-500;
    color: white;
  }

  &.draft {
    background-color: $warning;
    color: white;
  }
}

// Row hover effects
.listing-table {
  .table-row {
    &:hover {
      background-color: $gray-500;
      cursor: pointer;
    }
  }
}

// Search and filter styles
.list-actions {
  .search-input {
    border-radius: 0.375rem;
    border: 1px solid $gray-300;
    
    &:focus {
      border-color: $primary;
      box-shadow: 0 0 0 0.125rem rgba($primary, 0.25);
    }
  }

  .filter-button {
    border-radius: 0.375rem;
    border: 1px solid $gray-300;
    background-color: white;
    
    &:hover {
      background-color: $gray-500;
    }
  }
}

// Loading skeleton styles
.chatbots-loading-skeleton {
  .skeleton-row {
    height: 3rem;
    background: linear-gradient(90deg, $gray-200 25%, $gray-100 50%, $gray-200 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: 0.25rem;
    margin-bottom: 0.5rem;
  }
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

@import 'src/assets/styles/scss/base/variables';

.chatbots-header {
  justify-content: space-between;
  width: 100%;

}

.main-content-wrapper.whatsapp-chatbots-list {
  .page-title-header{
    justify-content: space-between;
    align-items: center;
  }

  h1{
    p{
      margin: 0;
    }
  }

  .chatbots-list {
    overflow-y: auto;

    .chatbot-card {
      margin-left: 1.25rem;
      margin-right: 1.25rem;
      margin-top: 1.875rem;
      border: 1px solid #DFDFDF;
      border-radius: 4px;
      display: flex;
      flex-direction: column;

      .header {
        height: 4.25rem;
        background-color: #F4F4F4;
        display: flex;
        flex-direction: row;

        .icon {
          width: 64px;
          text-align: center;
          align-content: center;

          img {
            height: 2rem;
          }
        }

        .header-section {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          width: 100%;
          margin-right: 1.5rem;
          margin-left: 0.3rem;

          .chatbot-display-name {
            align-self: center;
            font-size: 1rem;
            font-weight: 500;
          }

          .actions {
            display: flex;
            flex-direction: row;

            .manage-chatbot, .activate-chatbot, .setup-chatbot {
              align-self: center;
              background: #fff;

              &:hover {
                color: #fff;
                background-color: $blue;
              }
            }

            .line-separator {
              padding-right: 0;
              margin-top: 0.7rem;
              margin-bottom: 0.7rem;
              margin-left: 1rem;
              margin-right: 1rem;
              border-left: 0.08rem solid rgba(198, 208, 240, 0.34);
            }

            .options-container {
              align-self: center;
            }
          }
        }
      }

      .body {
        .chatbot-info {
          margin-left: 64px;
          margin-right: 1.5rem;
          margin-top: 1rem;
          margin-bottom: 1rem;

          .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 2fr;
            gap: 1.5rem;
            margin-bottom: 1rem;

            .info-item {
              .info-label {
                font-size: 0.75rem;
                font-weight: 600;
                color: #687790;
                text-transform: uppercase;
                letter-spacing: 0.5px;
                margin-bottom: 0.5rem;
              }

              .info-value {
                display: flex;
                align-items: center;
                font-size: 0.875rem;

                .fa.fa-circle {
                  font-size: 0.5rem;
                  margin-right: 0.5rem;
                }

                .type-indicator {
                  color: #2e384d;
                }

                .status-indicator {
                  &.active {
                    color: $success-green;
                  }

                  &.inactive {
                    color: $red;
                  }

                  &.draft {
                    color: $yellow;
                  }
                }

                .description-indicator {
                  color: #007bff;
                }

                .type-text, .status-text {
                  color: #2e384d;
                  font-weight: 500;
                }

                .description-text {
                  color: #2e384d;
                  line-height: 1.4;
                }
              }

              &.description-item {
                .info-value {
                  align-items: flex-start;

                  .fa.fa-circle {
                    margin-top: 0.25rem;
                    flex-shrink: 0;
                  }
                }
              }
            }
          }

          .additional-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 1rem;
            padding-top: 0.5rem;
            border-top: 1px solid #f0f0f0;

            .trigger-info, .account-info {
              font-size: 0.875rem;

              .trigger-label, .account-label {
                color: #687790;
                margin-right: 0.5rem;
              }

              .trigger-value, .account-value {
                color: #2e384d;
                font-weight: 500;
              }
            }
          }

          .timestamps {
            display: flex;
            justify-content: space-between;
            padding-top: 0.5rem;
            border-top: 1px solid #f0f0f0;

            .timestamp-item {
              font-size: 0.75rem;

              .timestamp-label {
                color: #687790;
                margin-right: 0.5rem;
              }

              .timestamp-value {
                color: #2e384d;
              }
            }
          }
        }
      }
    }
  }

  .no-chatbots-message {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 300px;

    .empty-state {
      text-align: center;

      i {
        margin-bottom: 1rem;
      }

      h3 {
        color: #2e384d;
        margin-bottom: 0.5rem;
      }

      p {
        color: #687790;
        font-size: 0.875rem;
      }
    }
  }

  .chatbots-loading-section {
    margin-left: 1.25rem;
    margin-right: 1.25rem;
    margin-top: 1.875rem;

    .loader-slot {
      margin-bottom: 1.875rem;
    }
  }
}

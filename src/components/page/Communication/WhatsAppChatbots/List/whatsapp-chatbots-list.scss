@import 'src/assets/styles/scss/base/variables';

.chatbots-header {
  justify-content: space-between;
}

.main-content-wrapper.whatsapp-chatbots-list {
  .chatbots-list {
    overflow-y: auto;

    .chatbot-card {
      margin-left: 1.25rem;
      margin-right: 1.25rem;
      margin-top: 1.875rem;
      border: 1px solid #DFDFDF;
      border-radius: 4px;
      display: flex;
      flex-direction: column;

      .header {
        height: 4.25rem;
        background-color: #F4F4F4;
        display: flex;
        flex-direction: row;

        .icon {
          width: 64px;
          text-align: center;

          i {
            position: relative;
            top: 1rem;
            font-size: 1.5rem;
          }
        }

        .header-section {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          width: 100%;
          margin-right: 1.5rem;
          margin-left: 0.3rem;

          .chatbot-display-name {
            align-self: center;
            font-size: 1rem;
            font-weight: 500;
          }

          .actions {
            display: flex;
            flex-direction: row;

            .manage-chatbot, .activate-chatbot, .setup-chatbot {
              align-self: center;
              background: #fff;

              &:hover {
                color: #fff;
                background-color: $blue;
              }
            }

            .line-separator {
              padding-right: 0;
              margin-top: 0.7rem;
              margin-bottom: 0.7rem;
              margin-left: 1rem;
              margin-right: 1rem;
              border-left: 0.08rem solid rgba(198, 208, 240, 0.34);
            }

            .options-container {
              align-self: center;
            }
          }
        }
      }

      .body {
        .chatbot-info {
          margin-left: 64px;
          margin-right: 1.5rem;
          margin-top: 1rem;
          margin-bottom: 1rem;

          .type-and-status {
            display: flex;
            flex-direction: row;
            width: 100%;
            justify-content: space-between;

            .type {
              font-size: 0.875rem;
              color: #687790;

              .chatbot-type {
                color: #2e384d !important;
                font-weight: 500;
              }
            }

            .status {
              font-size: 0.875rem;

              .fa.fa-circle {
                margin-right: 0.5rem;
              }

              &.active {
                color: $success-green;
              }

              &.inactive {
                color: $red;
              }

              &.draft {
                color: $yellow;
              }
            }
          }

          .description {
            font-size: 0.875rem;
            color: #687790;
            margin-top: 0.5rem;

            .chatbot-description {
              color: #2e384d !important;
            }
          }

          .trigger-info {
            font-size: 0.875rem;
            color: #687790;
            margin-top: 0.5rem;

            .trigger-type {
              color: #2e384d !important;
              text-transform: capitalize;
            }
          }

          .connected-account-info {
            font-size: 0.875rem;
            color: #687790;
            margin-top: 0.5rem;

            .account-name {
              color: #2e384d !important;
            }
          }

          .timestamps {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            margin-top: 1rem;
            font-size: 0.75rem;
            color: #687790;
          }
        }
      }
    }
  }

  .no-chatbots-message {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 300px;

    .empty-state {
      text-align: center;

      i {
        margin-bottom: 1rem;
      }

      h3 {
        color: #2e384d;
        margin-bottom: 0.5rem;
      }

      p {
        color: #687790;
        font-size: 0.875rem;
      }
    }
  }

  .chatbots-loading-section {
    margin-left: 1.25rem;
    margin-right: 1.25rem;
    margin-top: 1.875rem;

    .loader-slot {
      margin-bottom: 1.875rem;
    }
  }
}

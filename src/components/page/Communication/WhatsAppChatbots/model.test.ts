import { WhatsAppChatbot, WhatsAppChatbotListResponse } from './model';

describe('WhatsApp Chatbot Models', () => {
  describe('WhatsAppChatbot Interface', () => {
    const validChatbot: WhatsAppChatbot = {
      id: 'chatbot-123',
      name: 'Customer Support Bot',
      type: 'AI',
      description: 'Handles customer inquiries',
      status: 'ACTIVE',
      welcomeMessage: 'Hello! How can I help you?',
      thankYouMessage: 'Thank you for contacting us!',
      connectedAccount: {
        displayName: 'Sales Team',
        entityType: 'LEAD',
        accountId: 12345
      },
      trigger: 'NEW_ENTITY',
      created_at: '2024-01-15T10:30:00Z',
      updated_at: '2024-01-15T10:30:00Z',
      createdBy: {
        id: 1,
        name: '<PERSON>'
      },
      updatedBy: {
        id: 2,
        name: '<PERSON>'
      }
    };

    it('should accept valid chatbot object', () => {
      expect(validChatbot.id).toBe('chatbot-123');
      expect(validChatbot.name).toBe('Customer Support Bot');
      expect(validChatbot.type).toBe('AI');
      expect(validChatbot.status).toBe('ACTIVE');
      expect(validChatbot.trigger).toBe('NEW_ENTITY');
    });

    it('should accept AI type', () => {
      const aiChatbot: WhatsAppChatbot = {
        ...validChatbot,
        type: 'AI'
      };
      expect(aiChatbot.type).toBe('AI');
    });

    it('should accept RULE_BASED type', () => {
      const ruleBasedChatbot: WhatsAppChatbot = {
        ...validChatbot,
        type: 'RULE_BASED'
      };
      expect(ruleBasedChatbot.type).toBe('RULE_BASED');
    });

    it('should accept ACTIVE status', () => {
      const activeChatbot: WhatsAppChatbot = {
        ...validChatbot,
        status: 'ACTIVE'
      };
      expect(activeChatbot.status).toBe('ACTIVE');
    });

    it('should accept INACTIVE status', () => {
      const inactiveChatbot: WhatsAppChatbot = {
        ...validChatbot,
        status: 'INACTIVE'
      };
      expect(inactiveChatbot.status).toBe('INACTIVE');
    });

    it('should accept DRAFT status', () => {
      const draftChatbot: WhatsAppChatbot = {
        ...validChatbot,
        status: 'DRAFT'
      };
      expect(draftChatbot.status).toBe('DRAFT');
    });

    it('should accept NEW_ENTITY trigger', () => {
      const newEntityChatbot: WhatsAppChatbot = {
        ...validChatbot,
        trigger: 'NEW_ENTITY'
      };
      expect(newEntityChatbot.trigger).toBe('NEW_ENTITY');
    });

    it('should accept KEYWORD trigger', () => {
      const keywordChatbot: WhatsAppChatbot = {
        ...validChatbot,
        trigger: 'KEYWORD'
      };
      expect(keywordChatbot.trigger).toBe('KEYWORD');
    });

    it('should accept MANUAL trigger', () => {
      const manualChatbot: WhatsAppChatbot = {
        ...validChatbot,
        trigger: 'MANUAL'
      };
      expect(manualChatbot.trigger).toBe('MANUAL');
    });

    it('should have valid connected account structure', () => {
      expect(validChatbot.connectedAccount).toHaveProperty('displayName');
      expect(validChatbot.connectedAccount).toHaveProperty('entityType');
      expect(validChatbot.connectedAccount).toHaveProperty('accountId');
      expect(typeof validChatbot.connectedAccount.accountId).toBe('number');
    });

    it('should accept optional recordActions', () => {
      const chatbotWithActions: WhatsAppChatbot = {
        ...validChatbot,
        recordActions: {
          read: true,
          update: true,
          delete: false
        }
      };
      
      expect(chatbotWithActions.recordActions).toBeDefined();
      expect(chatbotWithActions.recordActions?.read).toBe(true);
      expect(chatbotWithActions.recordActions?.update).toBe(true);
      expect(chatbotWithActions.recordActions?.delete).toBe(false);
    });

    it('should work without recordActions', () => {
      const chatbotWithoutActions: WhatsAppChatbot = {
        ...validChatbot
      };
      
      expect(chatbotWithoutActions.recordActions).toBeUndefined();
    });

    it('should have ISO date strings for timestamps', () => {
      expect(validChatbot.created_at).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}Z$/);
      expect(validChatbot.updated_at).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}Z$/);
    });
  });

  describe('WhatsAppChatbotListResponse Interface', () => {
    const validListResponse: WhatsAppChatbotListResponse = {
      content: [
        {
          id: 'chatbot-1',
          name: 'Customer Support Bot',
          type: 'AI',
          description: 'Handles customer inquiries',
          status: 'ACTIVE',
          welcomeMessage: 'Hello!',
          thankYouMessage: 'Thank you!',
          connectedAccount: {
            displayName: 'Sales Team',
            entityType: 'LEAD',
            accountId: 12345
          },
          trigger: 'NEW_ENTITY',
          created_at: '2024-01-15T10:30:00Z',
          updated_at: '2024-01-15T10:30:00Z'
        },
        {
          id: 'chatbot-2',
          name: 'Lead Qualification Bot',
          type: 'RULE_BASED',
          description: 'Qualifies leads',
          status: 'INACTIVE',
          welcomeMessage: 'Welcome!',
          thankYouMessage: 'Thanks!',
          connectedAccount: {
            displayName: 'Marketing Team',
            entityType: 'LEAD',
            accountId: 12346
          },
          trigger: 'KEYWORD',
          created_at: '2024-01-10T08:15:00Z',
          updated_at: '2024-01-14T16:45:00Z'
        }
      ],
      totalElements: 2,
      totalPages: 1,
      page: {
        no: 0,
        size: 10
      }
    };

    it('should accept valid list response', () => {
      expect(validListResponse.content).toHaveLength(2);
      expect(validListResponse.totalElements).toBe(2);
      expect(validListResponse.totalPages).toBe(1);
    });

    it('should have content array of chatbots', () => {
      expect(Array.isArray(validListResponse.content)).toBe(true);
      validListResponse.content.forEach(chatbot => {
        expect(chatbot).toHaveProperty('id');
        expect(chatbot).toHaveProperty('name');
        expect(chatbot).toHaveProperty('type');
        expect(chatbot).toHaveProperty('status');
      });
    });

    it('should have pagination information', () => {
      expect(validListResponse.page).toHaveProperty('no');
      expect(validListResponse.page).toHaveProperty('size');
      expect(typeof validListResponse.page.no).toBe('number');
      expect(typeof validListResponse.page.size).toBe('number');
    });

    it('should have total counts', () => {
      expect(typeof validListResponse.totalElements).toBe('number');
      expect(typeof validListResponse.totalPages).toBe('number');
    });

    it('should accept empty content array', () => {
      const emptyResponse: WhatsAppChatbotListResponse = {
        content: [],
        totalElements: 0,
        totalPages: 0,
        page: {
          no: 0,
          size: 10
        }
      };

      expect(emptyResponse.content).toHaveLength(0);
      expect(emptyResponse.totalElements).toBe(0);
    });

    it('should handle large page numbers', () => {
      const largePageResponse: WhatsAppChatbotListResponse = {
        ...validListResponse,
        page: {
          no: 999,
          size: 50
        },
        totalPages: 1000
      };

      expect(largePageResponse.page.no).toBe(999);
      expect(largePageResponse.page.size).toBe(50);
      expect(largePageResponse.totalPages).toBe(1000);
    });
  });

  describe('Type Safety', () => {
    it('should enforce chatbot type constraints', () => {
      // This test ensures TypeScript compilation catches invalid types
      const validTypes: Array<WhatsAppChatbot['type']> = ['AI', 'RULE_BASED'];
      expect(validTypes).toContain('AI');
      expect(validTypes).toContain('RULE_BASED');
    });

    it('should enforce status constraints', () => {
      const validStatuses: Array<WhatsAppChatbot['status']> = ['ACTIVE', 'INACTIVE', 'DRAFT'];
      expect(validStatuses).toContain('ACTIVE');
      expect(validStatuses).toContain('INACTIVE');
      expect(validStatuses).toContain('DRAFT');
    });

    it('should enforce trigger constraints', () => {
      const validTriggers: Array<WhatsAppChatbot['trigger']> = ['NEW_ENTITY', 'KEYWORD', 'MANUAL'];
      expect(validTriggers).toContain('NEW_ENTITY');
      expect(validTriggers).toContain('KEYWORD');
      expect(validTriggers).toContain('MANUAL');
    });
  });

  describe('Real-world Usage Examples', () => {
    it('should handle chatbot with minimal connected account info', () => {
      const minimalChatbot: WhatsAppChatbot = {
        id: 'chatbot-minimal',
        name: 'Minimal Bot',
        type: 'AI',
        description: 'Basic chatbot',
        status: 'DRAFT',
        welcomeMessage: 'Hi',
        thankYouMessage: 'Bye',
        connectedAccount: {
          displayName: 'Basic Account',
          entityType: 'CONTACT',
          accountId: 1
        },
        trigger: 'MANUAL',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
      };

      expect(minimalChatbot.connectedAccount.accountId).toBe(1);
      expect(minimalChatbot.connectedAccount.entityType).toBe('CONTACT');
    });

    it('should handle chatbot with full record actions', () => {
      const fullActionsChatbot: WhatsAppChatbot = {
        id: 'chatbot-full',
        name: 'Full Actions Bot',
        type: 'RULE_BASED',
        description: 'Bot with all actions',
        status: 'ACTIVE',
        welcomeMessage: 'Welcome',
        thankYouMessage: 'Thank you',
        connectedAccount: {
          displayName: 'Full Account',
          entityType: 'LEAD',
          accountId: 999
        },
        trigger: 'NEW_ENTITY',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        recordActions: {
          read: true,
          update: true,
          delete: true
        }
      };

      expect(fullActionsChatbot.recordActions?.read).toBe(true);
      expect(fullActionsChatbot.recordActions?.update).toBe(true);
      expect(fullActionsChatbot.recordActions?.delete).toBe(true);
    });
  });
});

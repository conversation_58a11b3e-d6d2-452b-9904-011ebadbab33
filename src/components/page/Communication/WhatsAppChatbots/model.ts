export interface WhatsAppChatbot {
  id: string;
  name: string;
  type: 'AI' | 'RULE_BASED';
  description: string;
  status: 'ACTIVE' | 'INACTIVE' | 'DRAFT';
  welcomeMessage: string;
  thankYouMessage: string;
  connectedAccount: {
    displayName: string;
    entityType: string;
    accountId: number;
  };
  trigger: 'NEW_ENTITY' | 'KEYWORD' | 'MANUAL';
  created_at: string;
  updated_at: string;
  createdBy: {
    id: number;
    name: string;
  };
  updatedBy: {
    id: number;
    name: string;
  };
  recordActions?: {
    read: boolean;
    update: boolean;
    delete: boolean;
  };
}

export interface WhatsAppChatbotListResponse {
  content: WhatsAppChatbot[];
  totalElements: number;
  totalPages: number;
  page: {
    no: number;
    size: number;
  };
}

import axios from 'axios';
import { getWhatsAppChatbotsList, getWhatsAppChatbot, deleteChatbot, toggleChatbotStatus } from './service';
import { SortOrder } from '../../../shared/ListingTable/models/ListingTable';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

// Mock API utilities
jest.mock('../../../../services/api', () => ({
  setHeaders: jest.fn(() => ({ 'Authorization': 'Bearer token' }))
}));

jest.mock('../../../../config/apiConfig', () => ({
  defaultApiConfig: {},
  generateBaseUrl: jest.fn(() => 'http://localhost:3000')
}));

jest.mock('../../Company/service', () => ({
  getJsonRule: jest.fn(() => ({ rules: [] }))
}));

describe('WhatsApp Chatbots Service', () => {
  const mockHistory = { push: jest.fn() };
  const mockFilters = [{ field: 'name', operator: 'contains', value: 'test' }];

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getWhatsAppChatbotsList', () => {
    const mockResponse = {
      data: {
        content: [
          {
            id: 'chatbot-1',
            name: 'Customer Support Bot',
            type: 'AI',
            status: 'ACTIVE'
          }
        ],
        totalElements: 1,
        totalPages: 1
      }
    };

    it('should call API with correct parameters', async () => {
      mockedAxios.mockResolvedValue(mockResponse);

      await getWhatsAppChatbotsList(
        1, // page
        'name', // sortByField
        SortOrder.ASC, // sortOrder
        mockFilters,
        'test search',
        mockHistory,
        true // includeDraft
      );

      expect(mockedAxios).toHaveBeenCalledWith({
        url: 'http://localhost:3000/v1/chatbot/?include_draft=true&sort=name,asc&page=1&size=10',
        headers: { 'Authorization': 'Bearer token' },
        history: mockHistory,
        method: 'get',
        shouldRetry: true,
        params: {
          include_draft: true,
          sort: 'name,asc',
          page: 1,
          size: 10
        }
      });
    });

    it('should use default parameters when not provided', async () => {
      mockedAxios.mockResolvedValue(mockResponse);

      await getWhatsAppChatbotsList(
        undefined, // page - should default to 1
        undefined, // sortByField
        undefined, // sortOrder
        [],
        '',
        mockHistory
      );

      expect(mockedAxios).toHaveBeenCalledWith(
        expect.objectContaining({
          params: expect.objectContaining({
            page: 1,
            include_draft: true
          })
        })
      );
    });

    it('should handle includeDraft parameter correctly', async () => {
      mockedAxios.mockResolvedValue(mockResponse);

      await getWhatsAppChatbotsList(
        1,
        'name',
        SortOrder.DESC,
        [],
        '',
        mockHistory,
        false // includeDraft = false
      );

      expect(mockedAxios).toHaveBeenCalledWith(
        expect.objectContaining({
          url: expect.stringContaining('include_draft=false'),
          params: expect.objectContaining({
            include_draft: false
          })
        })
      );
    });

    it('should return response data', async () => {
      mockedAxios.mockResolvedValue(mockResponse);

      const result = await getWhatsAppChatbotsList(1, 'name', SortOrder.ASC, [], '', mockHistory);

      expect(result).toEqual(mockResponse);
    });

    it('should handle API errors', async () => {
      const mockError = new Error('API Error');
      mockedAxios.mockRejectedValue(mockError);

      await expect(
        getWhatsAppChatbotsList(1, 'name', SortOrder.ASC, [], '', mockHistory)
      ).rejects.toThrow('API Error');
    });
  });

  describe('getWhatsAppChatbot', () => {
    const mockChatbot = {
      data: {
        id: 'chatbot-1',
        name: 'Customer Support Bot',
        type: 'AI',
        status: 'ACTIVE',
        description: 'Handles customer inquiries'
      }
    };

    it('should call API with correct chatbot ID', async () => {
      mockedAxios.mockResolvedValue(mockChatbot);

      await getWhatsAppChatbot('chatbot-123');

      expect(mockedAxios).toHaveBeenCalledWith({
        url: 'http://localhost:3000/v1/chatbot/chatbot-123',
        headers: { 'Authorization': 'Bearer token' },
        method: 'get',
        shouldRetry: true
      });
    });

    it('should return chatbot data', async () => {
      mockedAxios.mockResolvedValue(mockChatbot);

      const result = await getWhatsAppChatbot('chatbot-123');

      expect(result).toEqual(mockChatbot);
    });

    it('should handle API errors', async () => {
      const mockError = new Error('Chatbot not found');
      mockedAxios.mockRejectedValue(mockError);

      await expect(getWhatsAppChatbot('invalid-id')).rejects.toThrow('Chatbot not found');
    });
  });

  describe('deleteChatbot', () => {
    it('should call DELETE API with correct chatbot ID', async () => {
      const mockResponse = { data: { success: true } };
      mockedAxios.mockResolvedValue(mockResponse);

      await deleteChatbot('chatbot-123');

      expect(mockedAxios).toHaveBeenCalledWith({
        url: 'http://localhost:3000/v1/chatbot/chatbot-123',
        headers: { 'Authorization': 'Bearer token' },
        method: 'delete',
        shouldRetry: true
      });
    });

    it('should return delete response', async () => {
      const mockResponse = { data: { success: true } };
      mockedAxios.mockResolvedValue(mockResponse);

      const result = await deleteChatbot('chatbot-123');

      expect(result).toEqual(mockResponse);
    });

    it('should handle delete errors', async () => {
      const mockError = new Error('Delete failed');
      mockedAxios.mockRejectedValue(mockError);

      await expect(deleteChatbot('chatbot-123')).rejects.toThrow('Delete failed');
    });
  });

  describe('toggleChatbotStatus', () => {
    it('should call PATCH API with correct parameters', async () => {
      const mockResponse = { data: { success: true } };
      mockedAxios.mockResolvedValue(mockResponse);

      await toggleChatbotStatus('chatbot-123', 'INACTIVE');

      expect(mockedAxios).toHaveBeenCalledWith({
        url: 'http://localhost:3000/v1/chatbot/chatbot-123/status',
        headers: { 'Authorization': 'Bearer token' },
        method: 'patch',
        shouldRetry: true,
        data: { status: 'INACTIVE' }
      });
    });

    it('should handle different status values', async () => {
      const mockResponse = { data: { success: true } };
      mockedAxios.mockResolvedValue(mockResponse);

      // Test ACTIVE status
      await toggleChatbotStatus('chatbot-123', 'ACTIVE');
      expect(mockedAxios).toHaveBeenLastCalledWith(
        expect.objectContaining({
          data: { status: 'ACTIVE' }
        })
      );

      // Test DRAFT status
      await toggleChatbotStatus('chatbot-123', 'DRAFT');
      expect(mockedAxios).toHaveBeenLastCalledWith(
        expect.objectContaining({
          data: { status: 'DRAFT' }
        })
      );
    });

    it('should return status toggle response', async () => {
      const mockResponse = { data: { success: true, newStatus: 'ACTIVE' } };
      mockedAxios.mockResolvedValue(mockResponse);

      const result = await toggleChatbotStatus('chatbot-123', 'ACTIVE');

      expect(result).toEqual(mockResponse);
    });

    it('should handle status toggle errors', async () => {
      const mockError = new Error('Status update failed');
      mockedAxios.mockRejectedValue(mockError);

      await expect(
        toggleChatbotStatus('chatbot-123', 'ACTIVE')
      ).rejects.toThrow('Status update failed');
    });
  });

  describe('API Configuration', () => {
    it('should use correct base URL for all endpoints', async () => {
      const mockResponse = { data: {} };
      mockedAxios.mockResolvedValue(mockResponse);

      // Test all service functions use the same base URL
      await getWhatsAppChatbotsList(1, 'name', SortOrder.ASC, [], '', mockHistory);
      await getWhatsAppChatbot('test-id');
      await deleteChatbot('test-id');
      await toggleChatbotStatus('test-id', 'ACTIVE');

      const calls = mockedAxios.mock.calls;
      calls.forEach(call => {
        expect(call[0].url).toContain('http://localhost:3000/v1/chatbot');
      });
    });

    it('should include authorization headers in all requests', async () => {
      const mockResponse = { data: {} };
      mockedAxios.mockResolvedValue(mockResponse);

      await getWhatsAppChatbotsList(1, 'name', SortOrder.ASC, [], '', mockHistory);
      await getWhatsAppChatbot('test-id');
      await deleteChatbot('test-id');
      await toggleChatbotStatus('test-id', 'ACTIVE');

      const calls = mockedAxios.mock.calls;
      calls.forEach(call => {
        expect(call[0].headers).toEqual({ 'Authorization': 'Bearer token' });
      });
    });

    it('should enable retry for all requests', async () => {
      const mockResponse = { data: {} };
      mockedAxios.mockResolvedValue(mockResponse);

      await getWhatsAppChatbotsList(1, 'name', SortOrder.ASC, [], '', mockHistory);
      await getWhatsAppChatbot('test-id');
      await deleteChatbot('test-id');
      await toggleChatbotStatus('test-id', 'ACTIVE');

      const calls = mockedAxios.mock.calls;
      calls.forEach(call => {
        expect(call[0].shouldRetry).toBe(true);
      });
    });
  });
});

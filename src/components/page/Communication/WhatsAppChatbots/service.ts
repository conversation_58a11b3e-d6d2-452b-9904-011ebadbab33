import axios from 'axios';

import * as api from '../../../../services/api';
import { defaultApiConfig, generateBaseUrl } from '../../../../config/apiConfig';
import { FilterRule } from '../../../shared/ListAction/FilterList';
import { SortOrder } from '../../../shared/ListingTable/models/ListingTable';
import { getJsonRule } from '../../Company/service';

export const getWhatsAppChatbotsList = (
  filters: FilterRule[],
  searchText: string,
  history: any,
  includeDraft: boolean = true
) => {
  const headers = api.setHeaders();
  const baseUrl = generateBaseUrl(defaultApiConfig, '');

  const jsonRule = getJsonRule(filters || [], searchText);

  const url = `${baseUrl}/chatbot`;

  return Promise.resolve({
    data: {
      content : [
        {
          id: 'chatbot-uuid',
          name: 'Customer Support Bot',
          type: 'AI',
          description: 'Handles customer inquiries',
          status: 'ACTIVE',
          welcomeMessage: 'Hello! How can I help you?',
          thankYouMessage: 'Thank you for contacting us!',
          connectedAccount: {
            displayName: 'Sales Team',
            entityType: 'LEAD',
            accountId: 12345
          },
          trigger: 'NEW_ENTITY',
          created_at: '2024-01-15T10:30:00Z',
          updated_at: '2024-01-15T10:30:00Z'
        }
      ]
    }
  });
  // @ts-ignore
  return axios({
    url,
    headers,
    history,
    method: 'get',
    shouldRetry: true
  });
};

export const getWhatsAppChatbot = (chatbotId: string) => {
  const headers = api.setHeaders();
  const baseUrl = generateBaseUrl(defaultApiConfig, '');
  const url = `${baseUrl}/v1/chatbot/${chatbotId}`;

  // @ts-ignore
  return axios({
    url,
    headers,
    method: 'get',
    shouldRetry: true
  });
};

export const deleteChatbot = (chatbotId: string) => {
  const headers = api.setHeaders();
  const baseUrl = generateBaseUrl(defaultApiConfig, '');
  const url = `${baseUrl}/v1/chatbot/${chatbotId}`;

  // @ts-ignore
  return axios({
    url,
    headers,
    method: 'delete',
    shouldRetry: true
  });
};

export const toggleChatbotStatus = (chatbotId: string, status: string) => {
  const headers = api.setHeaders();
  const baseUrl = generateBaseUrl(defaultApiConfig, '');
  const url = `${baseUrl}/v1/chatbot/${chatbotId}/status`;

  // @ts-ignore
  return axios({
    url,
    headers,
    method: 'patch',
    shouldRetry: true,
    data: { status }
  });
};

import * as React from 'react';
import { shallow } from 'enzyme';
import to<PERSON><PERSON> from 'enzyme-to-json';

import { ConditionType, TriggerFrequency, TriggerName, WorkflowActionsType, WorkflowEntity } from '../../../../models/Workflow';
import { WorkflowTriggerActionsSection } from './WorkflowTriggerActionsSection';
import { EntityFormAction } from '../../../../../formLayout/models/Form';
import { WorkflowActionEditProperty } from './WorkflowActions/EditProperty/EditPropertyAction';
import { WebhookAction } from './WorkflowActions/Webhook/WebhookAction';
import TaskAction from './WorkflowActions/Task/TaskAction';
import ReassignEntityAction from './WorkflowActions/ReassignEntityAction/ReassignEntityAction';
import FormField from '../../../../../../../higherOrderComponents/FormField/FormField';
import SendEmailAction from './WorkflowActions/SendEmail/SendEmailAction';
import ShareAction from './WorkflowActions/ShareAction/ShareAction';
import WorkflowMarketPlaceAction from './WorkflowActions/WorkflowMarketplaceAction/WorkflowMarketplaceAction';
import { AssignToAction } from './WorkflowActions/AssignToAction/AssignToAction';
import GenerateCallSummaryAction from './WorkflowActions/GenerateCallSummaryAction/GenerateCallSummaryAction';
import GenerateCallSummaryPromptsModal from './WorkflowActions/GenerateCallSummaryAction/GenerateCallSummaryPromptsModal';
import { waitForComponentToPaint } from '../../../../../../../../tests/utils';
const onFormValueChangeMock = jest.fn();
describe('Workflow Trigger actions section component', () => {

  const propsWithoutConditionSet = {
    form : {
      name: 'my workflow',
      description: 'some description',
      entityType: WorkflowEntity.LEAD,
      trigger: {
        name: TriggerName.EVENT,
        triggerFrequency: TriggerFrequency.CREATED
      },
      actions: [{ type: null }]
    },
    layoutFields: [{
      id: 562,
      sectionId: 130,
      type: 'TEXT_FIELD',
      entity: null,
      displayName: 'First Name',
      internalName: 'firstName',
      pickLists: null,
      description: null,
      length: null,
      greaterThan: null,
      lessThan: null,
      lookupUrl: null,
      regex: null,
      standard: true,
      sortable: false,
      filterable: true,
      required: true,
      multiValue: false,
      unique: false,
      internal: false,
      readOnly: false
    }],
    onFormValueChange: onFormValueChangeMock,
    mode: EntityFormAction.CREATE
  };

  const propsWithConditionSet = {
    ...propsWithoutConditionSet,
    form : {
      ...propsWithoutConditionSet.form,
      condition: {
        conditionType: ConditionType.FOR_ALL
      },
      actions: [{
        type: null
      }]
    }
  };

  let component;

  beforeEach(() => {
    component = shallow(<WorkflowTriggerActionsSection {...propsWithConditionSet} />);
    jest.resetAllMocks();
  });

  it('should render itself', () => {
    // @ts-ignore
    expect(toJson(component)).toMatchSnapshot();
  });

  it('Should NOT render bottom border for section in create mode', () => {
    expect(component.find('.workflow-section').length).toBe(1);
    expect(component.find('.workflow-section').hasClass('border-bottom-0')).toBe(true);
  });

  it('Should render bottom border for section, if NOT in create mode', () => {
    component.setProps({ mode: EntityFormAction.VIEW });

    expect(component.find('.workflow-section').length).toBe(1);
    expect(component.find('.workflow-section').hasClass('border-bottom-0')).toBe(false);
  });

  it('should NOT render step content and step as disabled, when Trigger frequency is NOT selected', () => {
    component = shallow(<WorkflowTriggerActionsSection {...propsWithoutConditionSet} />);

    expect(component.find('.workflow-section.disabled').length).toBe(1);
    expect(component.find('.workflow-section__content').length).toBe(0);
  });

  it('should render step content, when trigger frequency is selected', () => {
    expect(component.find('.workflow-section.disabled').length).toBe(0);
    expect(component.find('.workflow-section').length).toBe(1);
    expect(component.find('.workflow-section__content').length).toBe(1);
  });

  describe('action item', () => {
    it('should be able to select action type', () => {
      expect(component.find(FormField).length).toBe(1);
      expect(component.find(FormField).props().value).toBe(undefined);

      component.find(FormField).props().onChange({ label: 'Edit property', value: WorkflowActionsType.EDIT_PROPERTY });

      expect(propsWithConditionSet.onFormValueChange).toHaveBeenCalledWith({
        ...propsWithConditionSet.form,
        actions: [{
          type: WorkflowActionsType.EDIT_PROPERTY,
          payload: null
        }]
      });
    });

    it('should show Re-assign action disabled if already selected', () => {
      component.setProps({ form: {
        ...propsWithConditionSet.form,
        actions: [{
          type: WorkflowActionsType.REASSIGN
        }]
      }});

      expect(component.find(FormField).props().options).toEqual([
        { label: 'Edit Field', value: WorkflowActionsType.EDIT_PROPERTY, isDisabled: false },
        { label: 'Webhook', value: WorkflowActionsType.WEBHOOK, isDisabled: false },
        { label: 'Reassign', value: WorkflowActionsType.REASSIGN, isDisabled: true },
        { label: 'Share', value: WorkflowActionsType.SHARE, isDisabled: false },
        { label: 'Convert', value: WorkflowActionsType.CONVERT_LEAD, isDisabled: false },
        { label: 'Create Task', value: WorkflowActionsType.CREATE_TASK, isDisabled: false },
        { label: 'Send Email', value: WorkflowActionsType.SEND_EMAIL, isDisabled: false },
        { label: 'Marketplace Actions', value: WorkflowActionsType.MARKETPLACE_ACTION, isDisabled: false },
        { label: 'Send WhatsApp Message', value: WorkflowActionsType.SEND_WHATSAPP_MESSAGE, isDisabled: false }
      ]);
    });

    it('should show Assign to action disabled if already selected', () => {
      component.setProps({ form: {
        ...propsWithConditionSet.form,
        entityType: WorkflowEntity.TASK,
        actions: [{
          type: WorkflowActionsType.ASSIGN_TO
        }]
      }});

      expect(component.find(FormField).props().options).toEqual([
        { label: 'Edit Field', value: WorkflowActionsType.EDIT_PROPERTY, isDisabled: false },
        { label: 'Webhook', value: WorkflowActionsType.WEBHOOK, isDisabled: false },
        { label: 'Assign To', value: WorkflowActionsType.ASSIGN_TO, isDisabled: true },
        { label: 'Send Email', value: WorkflowActionsType.SEND_EMAIL, isDisabled: false },
        { label: 'Execute Another Workflow', value: WorkflowActionsType.TRIGGER_WORKFLOW, isDisabled: false }
      ]);
    });

    it('should render "Edit property" action type', () => {
      component.setProps({ form: {
        ...propsWithConditionSet.form,
        actions: [{
          type: WorkflowActionsType.EDIT_PROPERTY
        }, {
          type: null
        }]
      }});

      expect(component.find(WorkflowActionEditProperty).length).toBe(1);
      expect(component.find(WebhookAction).length).toBe(0);
    });

    it('should reset action type', () => {
      component.setProps({ form: {
        ...propsWithConditionSet.form,
        actions: [{
          type: WorkflowActionsType.EDIT_PROPERTY,
          payload: {
            name: 'firstName',
            value: 'john'
          }
        }]
      }});

      component.find(FormField).at(0).props().onChange({ label: 'Webhook', value: WorkflowActionsType.WEBHOOK }, 1);
      expect(onFormValueChangeMock).toHaveBeenCalledWith({
        ...propsWithConditionSet.form,
        actions: [{
          type: WorkflowActionsType.WEBHOOK,
          payload: null
        }]
      });
    });

    it('should set action payload', () => {
      component.setProps({ form: {
        ...propsWithConditionSet.form,
        actions: [{
          type: WorkflowActionsType.EDIT_PROPERTY
        }, {
          type: null
        }]
      }});

      component.find(WorkflowActionEditProperty).props().setWorkflowActionPayload({ name: 'firstName', value: 'john' }, 0);
      expect(onFormValueChangeMock).toHaveBeenCalledWith({
        ...propsWithConditionSet.form,
        actions: [{
          type: WorkflowActionsType.EDIT_PROPERTY,
          payload: {
            name: 'firstName',
            value: 'john'
          }
        }, {
          type: null
        }]
      });
    });

    it('should render "Reassign" action', () => {
      component.setProps({ form: {
        ...propsWithConditionSet.form,
        actions: [{
          type: WorkflowActionsType.REASSIGN
        }]
      }});

      expect(component.find(ReassignEntityAction).length).toBe(1);
    });

    it('should render "Webhook" action type', () => {
      component.setProps({ form: {
        ...propsWithConditionSet.form,
        actions: [{
          type: WorkflowActionsType.EDIT_PROPERTY
        }, {
          type: WorkflowActionsType.WEBHOOK
        }]
      }});

      expect(component.find(WorkflowActionEditProperty).length).toBe(1);
      expect(component.find(WebhookAction).length).toBe(1);
    });

    it('should render "Create Task" action', () => {
      component.setProps({ form: {
        ...propsWithConditionSet.form,
        actions: [{
          type: WorkflowActionsType.EDIT_PROPERTY
        }, {
          type: WorkflowActionsType.CREATE_TASK
        }]
      }});

      expect(component.find(WorkflowActionEditProperty).length).toBe(1);
      expect(component.find(TaskAction).length).toBe(1);
    });

    it('should render "Assign To" action', () => {
      component.setProps({ form: {
        ...propsWithConditionSet.form,
        entityType: WorkflowEntity.TASK,
        actions: [{
          type: WorkflowActionsType.ASSIGN_TO
        }]
      }});

      expect(component.find(AssignToAction).length).toBe(1);
    });

    it('should render "Share" action', () => {
      component.setProps({ form: {
        ...propsWithConditionSet.form,
        actions: [{
          type: WorkflowActionsType.EDIT_PROPERTY
        }, {
          type: WorkflowActionsType.SHARE
        }]
      }});

      expect(component.find(WorkflowActionEditProperty).length).toBe(1);
      expect(component.find(ShareAction).length).toBe(1);
    });

    it('should render "Send Email" action', () => {
      component.setProps({ form: {
        ...propsWithConditionSet.form,
        actions: [{
          type: WorkflowActionsType.EDIT_PROPERTY
        }, {
          type: WorkflowActionsType.SEND_EMAIL
        }]
      }});

      expect(component.find(WorkflowActionEditProperty).length).toBe(1);
      expect(component.find(SendEmailAction).length).toBe(1);
    });

    it('should render "Generate Call Summary" action type and show action disclaimer', () => {
      component.setProps({ form: {
        ...propsWithConditionSet.form,
        entityType: WorkflowEntity.CALL_LOG,
        actions: [{
          type: WorkflowActionsType.GENERATE_CALL_SUMMARY
        }]
      }});

      expect(component.find(GenerateCallSummaryAction).length).toBe(1);
    });

    it('should show use a suggested prompt link if action type is "Generate Call Summary"', async () => {
      component.setProps({ form: {
        ...propsWithConditionSet.form,
        entityType: WorkflowEntity.CALL_LOG,
        actions: [{
          type: WorkflowActionsType.GENERATE_CALL_SUMMARY
        }]
      }});

      expect(component.find('.show-suggested-prompt').length).toBe(1);
      component.find('.show-suggested-prompt').simulate('click');
    });

    it('should render "Marketplace Actions" action', () => {
      component.setProps({ form: {
        ...propsWithConditionSet.form,
        actions: [{
          type: WorkflowActionsType.MARKETPLACE_ACTION
        }]
      }});

      expect(component.find(WorkflowMarketPlaceAction).length).toBe(1);
    });

    it('should render "Marketplace Actions" action for deal', () => {
      component.setProps({ form: {
        ...propsWithConditionSet.form,
        entityType: WorkflowEntity.DEAL,
        actions: [{
          type: WorkflowActionsType.MARKETPLACE_ACTION
        }]
      }});

      expect(component.find(FormField).props().options.find(option => option.value === WorkflowActionsType.MARKETPLACE_ACTION)).toBeTruthy();
    });

    it('should render "Send WhatsApp Message" action for deal', () => {
      component.setProps({ form: {
        ...propsWithConditionSet.form,
        entityType: WorkflowEntity.DEAL,
        actions: [{
          type: WorkflowActionsType.SEND_WHATSAPP_MESSAGE
        }]
      }});

      expect(component.find(FormField).props().options.find(option => option.value === WorkflowActionsType.SEND_WHATSAPP_MESSAGE)).toBeTruthy();
    });

    it('should render "Marketplace Actions" action for meeting under workflow', () => {
      component.setProps({ form: {
        ...propsWithConditionSet.form,
        entityType: WorkflowEntity.MEETING,
        actions: [{
          type: WorkflowActionsType.MARKETPLACE_ACTION
        }]
      }});

      expect(component.find(FormField).props().options.find(option => option.value === WorkflowActionsType.MARKETPLACE_ACTION)).toBeTruthy();
    });

    it('should render "Marketplace Actions" action for call log under workflow', () => {
      component.setProps({ form: {
        ...propsWithConditionSet.form,
        entityType: WorkflowEntity.CALL_LOG,
        actions: [{
          type: WorkflowActionsType.MARKETPLACE_ACTION
        }]
      }});
      expect(component.find(FormField).props().options.find(option => option.value === WorkflowActionsType.MARKETPLACE_ACTION)).toBeTruthy();
    });

    it('should NOT SHOW add and delete buttons, when in readonly mode', () => {
      component.setProps({ form: {
        ...propsWithConditionSet.form,
        actions: [{
          type: WorkflowActionsType.EDIT_PROPERTY
        }, {
          type: null
        }]},
        mode: EntityFormAction.VIEW
      });

      expect(component.find('FormRowDeleteAction').length).toBe(0);
      expect(component.find('FormRowAddAction').length).toBe(0);
    });

    it('should delete action item, if more than 1 are present', () => {
      component.setProps({ form: {
        ...propsWithConditionSet.form,
        actions: [{
          type: WorkflowActionsType.EDIT_PROPERTY
        }, {
          type: null
        }]
      }});
      expect(component.find('FormRowDeleteAction').length).toBe(2);

      component.find('FormRowDeleteAction').at(1).props().onDelete([{
        type: WorkflowActionsType.EDIT_PROPERTY
      }]);

      expect(propsWithConditionSet.onFormValueChange).toHaveBeenCalledWith({
        ...propsWithConditionSet.form,
        actions: [{
          type: WorkflowActionsType.EDIT_PROPERTY
        }]
      });
    });

    it('should show Add button for last item, and should Add action item on click', () => {
      component.setProps({ form: {
        ...propsWithConditionSet.form,
        actions: [{
          type: WorkflowActionsType.EDIT_PROPERTY
        }, {
          type: null
        }]
      }});
      expect(component.find('FormRowAddAction').length).toBe(2);

      component.find('FormRowAddAction').at(1).props().onAdd([
        {
          type: WorkflowActionsType.EDIT_PROPERTY
        }, {
          type: null
        }, {
          type: null
        }
      ]);

      expect(propsWithConditionSet.onFormValueChange).toHaveBeenCalledWith({
        ...propsWithConditionSet.form,
        actions: [{
          type: WorkflowActionsType.EDIT_PROPERTY
        }, {
          type: null
        }, {
          type: null
        }]
      });
    });

    it('should NOT show webhook option as disabled when entityType is CONTACT', () => {
      const newProps = {
        ...propsWithConditionSet,
        form: {
          ...propsWithConditionSet.form,
          entityType: WorkflowEntity.CONTACT
        }
      };
      const newComponent = shallow(<WorkflowTriggerActionsSection {...newProps} />);
      const select = newComponent.find(FormField);
      const webhook = select.prop('options')[1];

      expect(webhook.label).toBe('Webhook');
      expect(webhook.isDisabled).toBeFalsy();
    });

    it('should NOT show webhook option as disabled when entityType is DEAL', () => {
      const newProps = {
        ...propsWithConditionSet,
        form: {
          ...propsWithConditionSet.form,
          entityType: WorkflowEntity.DEAL
        }
      };
      const newComponent = shallow(<WorkflowTriggerActionsSection {...newProps} />);
      const select = newComponent.find(FormField);
      const webhook = select.prop('options')[1];

      expect(webhook.label).toBe('Webhook');
      expect(webhook.isDisabled).toBeFalsy();
    });

    it('should NOT show reassign option as disabled when entityType is DEAL', () => {
      const newProps = {
        ...propsWithConditionSet,
        form: {
          ...propsWithConditionSet.form,
          entityType: WorkflowEntity.DEAL
        }
      };
      const newComponent = shallow(<WorkflowTriggerActionsSection {...newProps} />);
      const select = newComponent.find(FormField);
      const reassign = select.prop('options')[2];

      expect(reassign.label).toBe('Reassign');
      expect(reassign.isDisabled).toBeFalsy();
    });

    it('should NOT show reassign option as disabled when entityType is CONTACT', () => {
      const newProps = {
        ...propsWithConditionSet,
        form: {
          ...propsWithConditionSet.form,
          entityType: WorkflowEntity.CONTACT
        }
      };
      const newComponent = shallow(<WorkflowTriggerActionsSection {...newProps} />);
      const select = newComponent.find(FormField);
      const reassign = select.prop('options')[2];

      expect(reassign.label).toBe('Reassign');
      expect(reassign.isDisabled).toBeFalsy();
    });
  });

});

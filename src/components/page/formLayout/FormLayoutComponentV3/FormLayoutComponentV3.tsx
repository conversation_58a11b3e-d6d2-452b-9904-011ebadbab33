import * as React from 'react';
import { cloneDeep } from 'lodash';
import { connect } from 'react-redux';
import { toastr } from 'react-redux-toastr';
import * as debounce from 'lodash/debounce';
import { change, reduxForm, getFormValues, destroy } from 'redux-form';

import { StateInterface } from '../../../../store/store';
import { EntityLabelMap } from '../../login/models/login';
import { FieldTypes } from '../../FieldSettings/models/Field';
import { Campaign, CampaignStatus } from '../../Campaign/model';
import { CurrencyValue } from '../../../shared/Input/Money/Money';
import { ActionType } from '../../../shared/ListLayoutFilters/model';
import { Header } from '../../../shared/ListingTable/models/ListingTable';
import { SmartList } from '../../../shared/ListAction/model/SelectedSmartList';
import { EntityFormAction, FormLayout, Item, LayoutItem } from '../models/Form';

import { isBlank } from '../../../../utils/globalUtil';
import { validateFilters } from '../../BulkJobs/utils';
import { entities, ReferrerFlow } from '../../../../utils/constants';
import { showErrorToast } from '../../../../middlewares/errorToastr';
import { isCampaignActivityDisabled, isCampaignDisabled } from '../../Campaign/utils';
import { formatFormlayoutV3Payload, getFormLookupUrl, getFormPickListOptions } from '../utils';
import { formErrorsSelector } from '../../../../selectors/formErrorsSelectors/formErrorsSelector';
import { capitalizeLabel, isSmartListApplicable, routeToEntity } from '../../../../utils/entityUtils';

import { resetFormLayout } from '../actions/FormActions';
import { getCampaignDetails } from '../../Campaign/service';
import { getEntityListLayout, getSmartlist } from '../../Export/service';

import { FormFields } from '../components/FormFields';
import ConfirmModal from '../../../shared/Modal/ConfirmModal';
import SubmitButton from '../../../shared/Button/SubmitButton';
import CustomToastrs from '../../../shared/CustomToastr/CustomToastrs';
import LeftNavSearchV2 from '../../../shared/LeftNavSearchV2/LeftNavSearchV2';
import RouteLeavingGuard from '../../../shared/RouteLeavingGuard/RouteLeavingGuard';
import ListLayoutFilters from '../../../shared/ListLayoutFilters/ListLayoutFilters';
import FormErrorsNavigator, { Error } from '../../../shared/FormErrorsNavigator/FormErrorsNavigator';

import './_form-layout-component-v3.scss';

interface StoreProps {
  timezone: string;
  dateFormat: string;
  generalSetting: any;
  userCurrency: string;
  currentUserId: number;
  entityLabelMap: EntityLabelMap;
  formValues: { [internalName: string]: any };
  resetFormLayoutAction: () => void;
}

interface DispatchProps {
  updateForm: (fieldName: string, value) => void;
}

interface OwnProps {
  history: any;
  entity: string;
  errors: Error[];
  pristine: boolean;
  mode: EntityFormAction;
  entityLayout: FormLayout;
  campaignRemainingBudgetValue: CurrencyValue;
  initialValues: { [internalName: string]: any };
  formFooterRouteActions: {
    onPrimarySaveBtnClick: (id: number, campaignId: number) => void;
    onSecondarySaveBtnClick: () => void;
    onCancelBtnClick: () => void;
  };
  isFieldDisabled: (field: Item) => boolean;
  initialize: (fieldValues: { [internalName: string]: any }) => void;
  handleSubmit: (func: any) => React.FormEventHandler<HTMLFormElement>;
  onFormSubmit: (formValues: { [internalName: string]: any }) => Promise<any>;
}

type Props = StoreProps & DispatchProps & OwnProps;

enum FORM_LAYOUT_V3_SAVE_BUTTON_TYPE { PRIMARY, SECONDARY }

export const FormLayoutComponentV3:React.FC<Props> = ({
  mode,
  entity,
  errors,
  history,
  timezone,
  pristine,
  dateFormat,
  formValues,
  updateForm,
  initialize,
  onFormSubmit,
  entityLayout,
  userCurrency,
  handleSubmit,
  initialValues,
  entityLabelMap,
  generalSetting,
  isFieldDisabled,
  resetFormLayoutAction,
  formFooterRouteActions,
  campaignRemainingBudgetValue
}) => {
  const [submitting, setSubmitting] = React.useState<boolean>(false);
  const [showConfirmModal, toggleConfirmModal] = React.useState<boolean>(false);
  const [filterErrors, setFilterErrors] = React.useState<Set<string>>(new Set([]));
  const [entityLayoutColumns, setEntityLayoutColumns] = React.useState<Header[]>([]);
  const [utmFieldsCopyCheckbox, setUTMFieldsCopyCheckbox] = React.useState<boolean>(false);
  const [showFormDiscardWarning, toggleFormDiscardWarning] = React.useState<boolean>(true);
  const [listOfEntitySmartlist, setListOfEntitySmartlist] = React.useState<SmartList[]>([]);
  const [campaignRemainingBudget, setCampaignRemainingBudget] = React.useState<CurrencyValue>(null);
  const [formValuesPayload, setFormValuesPayload] = React.useState<{ [internalName: string]: any }>(null);
  const [saveBtnClickedType, setSaveBtnClickedType] = React.useState<FORM_LAYOUT_V3_SAVE_BUTTON_TYPE>(null);

  React.useEffect(() => {
    initialize(initialValues);

    if(!isBlank(entityLayout)) {
      const fieldToFocus = getFocusOnField();

      !isBlank(fieldToFocus) && focusAndScrollToField(fieldToFocus);
    }

    if((entity === entities.CAMPAIGN_ACTIVITIES) && (mode === EntityFormAction.EDIT)) {
      fetchEntityLayoutAndSmartlist(initialValues.entity.id, false);
    }

    return () => {
      destroy(FORM_LAYOUT_V3);
      resetFormLayoutAction();
    };
  },              []);

  React.useEffect(() => {
    if(!isBlank(saveBtnClickedType) && !isBlank(errors)) {
      setSaveBtnClickedType(null);

      !isBlank(errors[0]?.fieldId) && focusAndScrollToField(errors[0].fieldId);
    }
  },              [saveBtnClickedType, errors]);

  const fetchEntityLayoutAndSmartlist = (entityName: string, shouldClearRequiredFormValues: boolean) => {
    setFilterErrors(new Set<string>([]));

    if(shouldClearRequiredFormValues) {
      updateForm('selectedSmartlist', null);
      updateForm('filters', []);
      updateForm('whatsappTemplate', null);
    }

    getEntityListLayout(entityName, entityName === 'LEAD')
      .then(response => setEntityLayoutColumns(response.data.pageConfig.tableConfig.columns))
      .catch(err => showErrorToast(err));

    if(isSmartListApplicable(entityName.toLowerCase())) {
      getSmartlist(entityName.toLowerCase())
        .then(response => setListOfEntitySmartlist(response.data.content))
        .catch(err => showErrorToast(err));
    }
  };

  const fetchCampaignDetails = debounce((value: boolean, campaignId: number, shouldChangeFormCurrency: boolean) => {
    if(!value) return;

    getCampaignDetails(campaignId, null, null)
      .then((response) => {
        const { utmCampaign, utmSource, utmMedium, utmContent, utmTerm, estimatedBudget, activities } = response.data as Campaign;

        if(shouldChangeFormCurrency) {
          updateForm('estimatedBudget.currencyId', estimatedBudget.currencyId);
          updateForm('actualExpense.currencyId', estimatedBudget.currencyId);

          (mode === EntityFormAction.CREATE) && setCampaignRemainingBudget({ currencyId: estimatedBudget.currencyId, value: activities.reduce((list, v) => list - v.estimatedBudget.value, estimatedBudget.value) });
        } else {
          updateForm('utmCampaign', utmCampaign);
          updateForm('utmSource', utmSource);
          updateForm('utmMedium', utmMedium);
          updateForm('utmContent', utmContent);
          updateForm('utmTerm', utmTerm);
        }
      })
      .catch(err => showErrorToast(err));
  },                                    300);

  const onFieldValueChange = (fieldName: string, value) => {
    if((entity === entities.CAMPAIGN_ACTIVITIES) && !isBlank(value)) {
      switch(fieldName) {
        case 'entity':
          fetchEntityLayoutAndSmartlist(value.name, true);
          break;

        case 'selectedSmartlist':
          const selectedSmartlist = listOfEntitySmartlist.find(smartlist => smartlist.id === value.id);
          const filters = selectedSmartlist?.searchRequest?.jsonRule?.rules || [];

          setFilterErrors(new Set<string>([]));
          updateForm('filters', filters);
          break;

        case 'connectedAccount':
          updateForm('whatsappTemplate', null);
          break;

        case 'campaign':
          setUTMFieldsCopyCheckbox(false);
          fetchCampaignDetails(true, value.id, true);
          break;
      }
    }

    updateForm(fieldName, value);
  };

  const clearError = (id: string) => {
    const errorValues = new Set<string>(filterErrors);

    errorValues.delete(id);
    setFilterErrors(errorValues);
  };

  const getLabelForSubmitAction = () => {
    if(mode === EntityFormAction.CREATE) {
      if(entity === entities.CAMPAIGNS) return 'Save and Add Actvity';
      if(entity === entities.CAMPAIGN_ACTIVITIES) return 'Save and Run';
    }

    return 'Save';
  };

  const getFocusOnField = () => {
    let elementId = null;

    for(let sectionIndex = 0; sectionIndex < entityLayout.layoutItems.length; sectionIndex += 1) {
      const layoutItem = entityLayout.layoutItems[sectionIndex];

      const fieldToFocus = layoutItem.layoutItems.find((item: Item) => item.item.required);

      if (!isBlank(fieldToFocus)) {
        elementId = `${sectionIndex}_${fieldToFocus.row}${fieldToFocus.column}_input_${fieldToFocus.item.internalName}`;
        break;
      }
    }

    return elementId;
  };

  const focusAndScrollToField = (fieldName: string) => {
    const documentField = document.getElementById(fieldName);

    documentField.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    setTimeout(() => documentField.focus(), 100);
  };

  const submit = (values: { [internalName: string]: any }) => {
    if(entity === entities.CAMPAIGN_ACTIVITIES) {
      const { isValid, errors: errorValues } = validateFilters(values?.filters);

      setFilterErrors(errorValues);

      if(!isValid) return;

      if((mode === EntityFormAction.CREATE) && (saveBtnClickedType === FORM_LAYOUT_V3_SAVE_BUTTON_TYPE.PRIMARY)) {
        toggleConfirmModal(true);
        setFormValuesPayload(values);

        return;
      }
    }

    submitAction(values);
  };

  const submitAction = (values: { [internalName: string]: any }) => {
    const existingData = cloneDeep(values);

    entityLayout.layoutItems
      .flatMap((item: LayoutItem) => item.layoutItems)
      .forEach((item: Item) => {
        if((item.item.type === FieldTypes.TEXT_FIELD) && existingData && existingData[item.item.internalName]){
          existingData[item.item.internalName] = existingData[item.item.internalName].trim();
        }
      });

    const formattedPayload = formatFormlayoutV3Payload(entity, existingData);

    setSubmitting(true);

    onFormSubmit(formattedPayload)
      .then((response) => {
        toggleFormDiscardWarning(false);

        if(saveBtnClickedType === FORM_LAYOUT_V3_SAVE_BUTTON_TYPE.PRIMARY) {
          formFooterRouteActions.onPrimarySaveBtnClick(response.data.id, (entity === entities.CAMPAIGN_ACTIVITIES) ? response.data.campaign.id : null);
        } else {
          formFooterRouteActions.onSecondarySaveBtnClick();
        }

        toastr.success('Success', {
          component: (
            <CustomToastrs
              entityAction={mode}
              entityId={response.data.id}
              customLabel={capitalizeLabel(routeToEntity(entity))}
              historyState={(entity === entities.CAMPAIGN_ACTIVITIES) ? { activityId: response.data.id } : null}
              url={`/sales/${(entity === entities.CAMPAIGN_ACTIVITIES) ? entities.CAMPAIGNS : entity}/details/${(entity === entities.CAMPAIGN_ACTIVITIES) ? response.data.campaign.id : response.data.id}`}
            />
          )
        });
      })
      .catch(err => showErrorToast(err))
      .finally(() => setSubmitting(false));
  };

  const isFieldInReadOnlyMode = (internalName: string) => {
    switch(mode) {
      case EntityFormAction.CREATE:
        if((internalName === 'campaign') && ((history?.location?.state?.referrer !== ReferrerFlow.CAMPAIGN_ACTIVITY_LIST))) {
          return true;
        }

        return false;

      case EntityFormAction.EDIT:
        if((entity === entities.CAMPAIGNS) && isCampaignDisabled(formValues?.status)) {
          return !['endDate', 'estimatedBudget'].includes(internalName);
        }

        if((entity === entities.CAMPAIGN_ACTIVITIES)) {
          return isCampaignActivityDisabled(formValues?.status) ? !['endDate', 'estimatedBudget', 'actualExpense'].includes(internalName) : (internalName === 'campaign');
        }

        return false;
    }
  };

  const getSections = () => entityLayout.layoutItems.map(layoutItem => ({ value: layoutItem.id, label: layoutItem.item.heading, description: layoutItem.item.description }));

  const sectionBody = (val: LayoutItem, index: number) => {
    if ((val.type === 'SECTION') && !isBlank(val.layoutItems) && (val.item.heading !== '')) {
      return (
        <div key={index} id={val.id.toString()} className="data-container">
          <div className="layout-header">
            <h2 className="h2">{val.item.heading}</h2>
            { (entity === entities.CAMPAIGN_ACTIVITIES) && (val.item.name === 'utmInformation') && !isBlank(formValues?.campaign) &&
              <div className="custom-control custom-checkbox">
                <input
                  type="checkbox"
                  id="copy-from-campaign"
                  checked={utmFieldsCopyCheckbox}
                  className="custom-control-input"
                  disabled={isCampaignActivityDisabled(formValues?.status)}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                    setUTMFieldsCopyCheckbox(e.target.checked);
                    fetchCampaignDetails(e.target.checked, formValues.campaign.id, false);
                  }}
                />

                <label className="custom-control-label mr-2" htmlFor="copy-from-campaign">Same as Campaign</label>
              </div>
            }
          </div>

          <div className="layout-body">
            <div className="row">
              {
                val.layoutItems.map((field, index2) => {
                  if(field.type === 'FIELD') {
                    if((entity === entities.CAMPAIGN_ACTIVITIES) && (field.item.internalName === 'filters')) {
                      return (
                        <ListLayoutFilters
                          key={index2}
                          clearError={clearError}
                          changeValue={updateForm}
                          filters={formValues?.filters}
                          errors={Array.from(filterErrors)}
                          entityFields={entityLayoutColumns}
                          actionType={ActionType.CAMPAIGN_ACTIVITY}
                          entity={formValues?.entity?.name?.toLowerCase()}
                          isDisabled={(mode === EntityFormAction.EDIT) && isCampaignActivityDisabled(formValues?.status)}
                        />
                      );
                    }

                    return (
                      <FormFields
                        key={index2}
                        entity={entity}
                        sectionId={index}
                        entityAction={mode}
                        timezone={timezone}
                        dateFormat={dateFormat}
                        formLayout={entityLayout}
                        userCurrency={userCurrency}
                        tenantCurrency={generalSetting.currency}
                        disableDependentField={isFieldDisabled(field)}
                        nameAlreadyResolved={entity === entities.CAMPAIGN_ACTIVITIES}
                        parent={{
                          props: {
                            formValues,
                            entityAction: mode,
                            change: onFieldValueChange,
                            campaignRemainingBudgetValue: campaignRemainingBudget || campaignRemainingBudgetValue
                          }
                        }}
                        form={{
                          ...field,
                          item: {
                            ...field.item,
                            pickLists: getFormPickListOptions(
                              entity,
                              entityLabelMap,
                              field,
                              (field.item.internalName === 'selectedSmartlist') ?
                                  listOfEntitySmartlist.map((smartlist: SmartList) => ({ name: smartlist.name, id: smartlist.id, displayName: smartlist.name }))
                                :
                                  null
                            ),
                            lookupUrl: getFormLookupUrl(entity, field, formValues),
                            isReadOnly: isFieldInReadOnlyMode(field.item.internalName)
                          }
                        }}
                      />
                    );
                  }
                })
              }
            </div>
          </div>
        </div>
      );
    }
  };

  return (
    <React.Fragment>
      <form className="form-layout-v3" onSubmit={handleSubmit(values => submit(values))}>
        <div className="form-header">
          <div className="form-title">{((entity === entities.CAMPAIGN_ACTIVITIES) && (mode === EntityFormAction.CREATE)) ? 'Add' : capitalizeLabel(mode)} {capitalizeLabel(routeToEntity(entity))}</div>
        </div>

        <div className="form-body page-content min-height-0">
          <div className="left-section">
            { !isBlank(entityLayout) && <LeftNavSearchV2 sectionMap={getSections()} /> }
          </div>

          <div className="right-section">
            <div
              data-spy="scroll"
              data-target="#v-pills-tab"
              className="overflow-scroll position-relative"
            >
              { !isBlank(entityLayout) && entityLayout.layoutItems.map((val, index) => sectionBody(val, index)) }
            </div>
          </div>
        </div>

        <div className="form-footer">
          <div className="helptext-wrapper">{!isBlank(errors) && <FormErrorsNavigator errors={errors}/>}</div>

          <div className="form-buttons">
            <button
              type="button"
              disabled={submitting}
              className="btn btn-outline-primary cursor-pointer"
              onClick={formFooterRouteActions.onCancelBtnClick}
            >
              Cancel
            </button>

            { (mode === EntityFormAction.CREATE) && [entities.CAMPAIGNS, entities.CAMPAIGN_ACTIVITIES].includes(entity) &&
              <button
                type="submit"
                disabled={pristine || submitting}
                className="btn btn-outline-primary"
                onClick={() => setSaveBtnClickedType(FORM_LAYOUT_V3_SAVE_BUTTON_TYPE.SECONDARY)}
              >
                Save as Draft
              </button>
            }

            <SubmitButton
              pristine={pristine}
              loading={submitting}
              shouldAlsoDisableOn={() => null}
              submitText={getLabelForSubmitAction()}
              extraProps={{ onClick: () => setSaveBtnClickedType(FORM_LAYOUT_V3_SAVE_BUTTON_TYPE.PRIMARY) }}
            />
          </div>
        </div>
      </form>

      <RouteLeavingGuard
        showRouteChangeWarning
        confirmBtnLabel="Okay"
        cancelBtnLabel="Cancel"
        confirmBtnClass="btn-primary"
        title={`Discard ${capitalizeLabel(routeToEntity(entity))}`}
        isNavigationBlocked={() => !pristine && showFormDiscardWarning}
        message={
          <div>
            <div className="mb-3">Are you sure you want to discard the {capitalizeLabel(routeToEntity(entity))}?</div>
            Please note, that choosing to Okay will delete all the changes made to this {routeToEntity(entity)}
            <br/>
            <br/>
            Do you want to proceed?
          </div>
        }
      />

      { showConfirmModal &&
        <ConfirmModal
          show
          confirmBtnLabel="Yes, Start"
          confirmBtnClass="btn-primary"
          title="Ready to Start Your Activity?"
          message={
            <div>
              <div>Starting this activity under this {routeToEntity(entities.CAMPAIGNS)} will start the {routeToEntity(entities.CAMPAIGNS)}(If not started). All other activities under this {routeToEntity(entities.CAMPAIGNS)} will remain in their original state.</div>
              <div className="mt-1">Are you sure you want to <b>Start</b> the activity?</div>
            </div>
          }
          onConfirm={() => {
            toggleConfirmModal(false);
            submitAction(formValuesPayload);
          }}
          onCancel={() => toggleConfirmModal(false)}
        />
      }
    </React.Fragment>
  );
};

export const FORM_LAYOUT_V3 = 'FormlayoutV3';

const mapStateToProps = (state: StateInterface) => ({
  formValues: getFormValues(FORM_LAYOUT_V3)(state),
  errors: formErrorsSelector(state, FORM_LAYOUT_V3),
  generalSetting: state.genSettings.payload,
  userCurrency: state.header.profile.currency,
  currentUserId: state.loginForm.currentUserId,
  entityLabelMap: state.loginForm.entityLabelMap,
  timezone: state.loginForm.userPreferences.timezone,
  dateFormat: state.loginForm.userPreferences.dateFormat
});

const mapDispatchToProps = dispatch => ({
  resetFormLayoutAction: () => dispatch(resetFormLayout()),
  updateForm: (fieldName, value) => {
    dispatch(change(FORM_LAYOUT_V3, fieldName, value));
  }
});

export default connect(mapStateToProps, mapDispatchToProps)(reduxForm({ form: FORM_LAYOUT_V3 })(FormLayoutComponentV3));

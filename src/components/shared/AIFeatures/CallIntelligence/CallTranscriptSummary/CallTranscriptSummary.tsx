import * as React from 'react';
import { connect } from 'react-redux';

import Toogle from '../../../Input/Toogle';
import { callIntelligenceTabs } from '../constant';
import { appName, isBlank } from '../../../../../utils/globalUtil';
import { SmartFeature } from '../../EmailAssistant/model';
import { entities } from '../../../../../utils/constants';
import TabsSection from '../../../TabsSection/TabsSection';
import { StateInterface } from '../../../../../store/store';
import ConsentModal from '../../../ConsentModal/ConsentModal';
import { getEntityLabel } from '../../../../../utils/entityUtils';
import ApiStateHandler from '../../../ApiHandler/ApiStateHandler';
import { showSuccessToast } from '../../../../../middlewares/api';
import { setDataInLocalStorage, shouldShowAIAssistants } from '../../../../../utils/util';
import { toggleCallTranscriptSummaryActivation } from '../service';
import { LoadingSkeleton } from '../../EmailAssistant/LoadingSkeleton';
import * as NewTab from '../../../../../assets/icons/new-tab-blue.svg';
import { showErrorToast } from '../../../../../middlewares/errorToastr';
import SetupLayout from '../../../../page/layout/SetupLayout/SetupLayout';
import { WorkflowEntity } from '../../../../page/Workflows/models/Workflow';
import { EntityLabelMap, Login } from '../../../../page/login/models/login';
import { getSmartAssistantActivationInfo } from '../../EmailAssistant/service';
import { WaveformRecordingPlayer } from '../../../WaveformRecordingPlayer/WaveformRecordingPlayer';
import { ErrorComponent, WithApiStateHandler } from '../../../../page/DataManagement/Components/Import/WithApiCall';
import { PlanTypes } from '../../../../page/Billing/models/UsageStatistic';
import PlanUpgradeModal from '../../../PlanUpgradeModal/PlanUpgradeModal';

import './_call-transcript-summary.scss';

interface Props {
  error: any;
  history: any;
  loginObj: Login;
  loading: boolean;
  data: SmartFeature[];
  entityLabelMap: EntityLabelMap;
  tenantId: number;
  planName: PlanTypes;
  isTenantUser: boolean;
}

export const CallTranscriptSummary: React.FC<Props> = ({ history, loginObj, data, loading, error, entityLabelMap, tenantId, planName, isTenantUser }) => {
  const [showConsentModal, setShowConsentModal] = React.useState(false);
  const [isRecordingPlayed, setIsRecordingPlayed] = React.useState(false);
  const [isCallTranscriptSummaryActivated, setIsCallTranscriptSummaryActivated] = React.useState(false);
  const [showPlanUpgradeModal, setShowPlanUpgradeModal] = React.useState(false);

  React.useEffect(() => {
    if (data) {
      const feature = data.find(f => f?.featureType === 'call');
      setIsCallTranscriptSummaryActivated(feature?.featureResponse?.enabled);
    }
  },              [data]);

  const toggleCallTranscriptSummary = () => {
    if(planName === PlanTypes.FREEMIUM){
      setShowPlanUpgradeModal(true);
      return;
    }

    if (!isCallTranscriptSummaryActivated) {
      setShowConsentModal(true);
    } else {
      activateDeactivateCallTranscriptSummary(false);
    }
  };

  const activateDeactivateCallTranscriptSummary = (val: boolean) => {
    toggleCallTranscriptSummaryActivation(val, history)
    .then(() => {
      setShowConsentModal(false);
      setIsCallTranscriptSummaryActivated(val);
      showSuccessToast(`Call Intelligence has been ${val ? 'enabled' : 'disabled'} on your account.`);
      setDataInLocalStorage(`${localStorage.getItem('tenantId')}.call.enabled`, 'smartAssistant', val);
    })
    .catch((err) => {
      showErrorToast(err);
    });
  };

  const onWorkflowSetupClick = () => {
    const newWindow = window.open('/setup/workflow-automation/workflows/create', '_blank');
    // @ts-ignore
    newWindow.workflowEntity = WorkflowEntity.CALL_LOG;
  };

  return (
    <SetupLayout {... { loginObj, history, entity: entities.AI }}>
      <div className="main-content-wrapper position-relative call-transcript-summary">
        <TabsSection
          loading={false}
          history={history}
          section={'Transcript & Summary'}
          tabs={callIntelligenceTabs}
        />
        <div className="page-content overflow-hidden min-height-0">
          <div className="page-inner-content h-100">
            <div className="top-wrapper min-height-0">
              <h1 className="h1">{'Call Transcript and Summary'}</h1>
              <div className="sub-heading mt-1 mb-3">
                {`Call recordings will now be converted into AI-powered transcripts and summaries, detailing sentiments, objections and action items. These summaries will be seamlessly integrated as notes within the call log and linked to the corresponding ${getEntityLabel(entityLabelMap, entities.LEADS)} and ${getEntityLabel(entityLabelMap, entities.CONTACTS)}.`}
              </div>
              <ApiStateHandler loading={loading} ErrorFallbackComponent={ErrorComponent} error={error} SkeletonComponent={LoadingSkeleton}>
                <Toogle
                  id={'callTranscriptSummaryToggle'}
                  value={isCallTranscriptSummaryActivated}
                  handleClick={toggleCallTranscriptSummary}
                  label="Enable"
                  disabled={false}
                />
              </ApiStateHandler>
              <div className="container">
                <div className="header">Example Summary</div>
                <div>Here’s a sample call showing how the summary will be generated once the feature is enabled.</div>
                <WaveformRecordingPlayer
                  setIsRecordingPlayed={setIsRecordingPlayed}
                  audioUrl="https://assets.kylas.io/resources/call-summary-sample.mp3"
                />
                {
                  isRecordingPlayed &&
                  <div className="transcript-summary-container">
                    <div className="chip summary active">Summary</div>
                    <div className="content">
                      Sanket from Kylas CRM contacted Samiksha from ETC company, an edtech company, regarding their CRM inquiry. ABC company needs lead management, database management, and WhatsApp integration for 5-6 users. Call recording is a desirable feature. Their leads are generated through their Google website and through Facebook and Instagram. The customer is interested in implementing a CRM solution as soon as possible to improve lead follow-up. Sanket offered a per-user plan at ₹1500 per user per month and scheduled a demo for the next day at 4 pm.
                    </div>
                  </div>
                }
              </div>
              {
                shouldShowAIAssistants(tenantId) &&
                <div className="workflow-setup">
                  <div className="header">Set It Up in Workflow Automation</div>
                  <div className="description">
                    To start generating call transcripts and summaries, add the relevant actions in your workflows. Once added, the system will automatically process calls based on your defined criteria. <a className="link-primary" target="_blank" href="https://support.kylas.io/portal/en">Learn More</a>
                    <ul className="mt-2">
                      <li>This feature delivers a complete <strong>call transcript</strong> and a concise <strong>AI summary</strong> highlighting key discussion points and <strong>overall sentiments</strong>.</li>
                      <li>It also intelligently identifies action items, automatically creates tasks linked to the relevant records.</li>
                    </ul>
                  </div>
                  <div className="link-primary" onClick={onWorkflowSetupClick}>Go to Workflow Setup <img src={`${NewTab}`} alt="new-tab-icon" className="new-tab-icon" /></div>
                </div>
              }
            </div>
          </div>
        </div>
        {
          showConsentModal &&
          <ConsentModal
            title="Enable Sentiment Analysis for Call Recordings"
            message={`By enabling this feature, you allow ${appName()} to process your call recordings using AI to detect sentiments like positive, negative, or neutral. This data may be used to help improve call quality insights and user experience.`}
            termsMessage={`I understand and consent to ${appName()} using call transcripts and recordings for sentiment analysis`}
            confirmButtonText="Confirm"
            onClose={() => setShowConsentModal(false)}
            onConfirm={() => activateDeactivateCallTranscriptSummary(true)}
          />
        }
        {
          showPlanUpgradeModal &&
          <PlanUpgradeModal
            entity={'aiFeatures'}
            isTenantUser={isTenantUser}
            isFreePlan={planName === PlanTypes.FREEMIUM}
            onClose={() => setShowPlanUpgradeModal(false)}
          />
        }
      </div>
    </SetupLayout>
  );
};

const mapStateToProps = (state: StateInterface) => {
  return {
    entityLabelMap: state.appData.entityLabelMap,
    tenantId: state.genSettings?.payload?.id,
    planName: state.genSettings?.payload?.planName,
    isTenantUser: isBlank(state.header?.profile?.createdBy)
  };
};

export default connect(mapStateToProps, {})(WithApiStateHandler(CallTranscriptSummary, getSmartAssistantActivationInfo));

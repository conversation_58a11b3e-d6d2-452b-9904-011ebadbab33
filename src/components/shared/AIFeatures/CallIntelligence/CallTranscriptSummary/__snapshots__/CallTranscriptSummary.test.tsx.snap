// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CallTranscriptSummary Component should render Call Transcript Summary Properly 1`] = `
<Provider
  store={
    Object {
      "dispatch": [Function],
      "getState": [Function],
      "replaceReducer": [Function],
      "subscribe": [Function],
      Symbol(observable): [Function],
    }
  }
>
  <BrowserRouter>
    <Router
      history={
        Object {
          "action": "POP",
          "block": [Function],
          "createHref": [Function],
          "go": [Function],
          "goBack": [Function],
          "goForward": [Function],
          "length": 1,
          "listen": [Function],
          "location": Object {
            "hash": "",
            "pathname": "/",
            "search": "",
            "state": undefined,
          },
          "push": [Function],
          "replace": [Function],
        }
      }
    >
      <CallTranscriptSummary
        data={
          Array [
            Object {
              "featureResponse": Object {
                "enabled": true,
                "updatedBy": Object {
                  "id": 1,
                  "name": "<PERSON>",
                },
              },
              "featureType": "call",
            },
          ]
        }
        entityLabelMap={
          Object {
            "COMPANY": Object {
              "displayName": "Company",
              "displayNamePlural": "Companies",
            },
            "CONTACT": Object {
              "displayName": "Student",
              "displayNamePlural": "Contacts",
            },
            "DEAL": Object {
              "displayName": "Deal",
              "displayNamePlural": "Deals",
            },
            "LEAD": Object {
              "displayName": "Teacher",
              "displayNamePlural": "Teachers",
            },
            "TASK": Object {
              "displayName": "Task",
              "displayNamePlural": "Tasks",
            },
            "TEAM": Object {
              "displayName": "Team",
              "displayNamePlural": "Teams",
            },
            "USER": Object {
              "displayName": "User",
              "displayNamePlural": "Users",
            },
          }
        }
        error={null}
        history={
          Object {
            "location": Object {
              "search": "",
            },
            "push": [MockFunction],
          }
        }
        isTenantUser={true}
        loading={false}
        loginObj={
          Object {
            "currentUserId": 123,
            "currentUserName": "sant",
            "entityLabelMap": Object {
              "COMPANY": Object {
                "displayName": "Company",
                "displayNamePlural": "Companies",
              },
              "CONTACT": Object {
                "displayName": "Student",
                "displayNamePlural": "Contacts",
              },
              "DEAL": Object {
                "displayName": "Deal",
                "displayNamePlural": "Deals",
              },
              "LEAD": Object {
                "displayName": "Teacher",
                "displayNamePlural": "Teachers",
              },
              "TASK": Object {
                "displayName": "Task",
                "displayNamePlural": "Tasks",
              },
              "TEAM": Object {
                "displayName": "Team",
                "displayNamePlural": "Teams",
              },
              "USER": Object {
                "displayName": "User",
                "displayNamePlural": "Users",
              },
            },
            "isLogin": true,
            "loading": false,
            "token": "invalid",
            "userPreferences": Object {
              "dateFormat": "MMM D, YYYY [at] h:mm a",
              "numberFormat": "INDIAN_NUMBER_FORMAT",
              "timezone": "Asia/Calcutta",
            },
          }
        }
        planName="elevate-annual"
        tenantId={123}
      >
        <withRouter(Connect(SetupLayout))
          entity="ai"
          history={
            Object {
              "location": Object {
                "search": "",
              },
              "push": [MockFunction],
            }
          }
          loginObj={
            Object {
              "currentUserId": 123,
              "currentUserName": "sant",
              "entityLabelMap": Object {
                "COMPANY": Object {
                  "displayName": "Company",
                  "displayNamePlural": "Companies",
                },
                "CONTACT": Object {
                  "displayName": "Student",
                  "displayNamePlural": "Contacts",
                },
                "DEAL": Object {
                  "displayName": "Deal",
                  "displayNamePlural": "Deals",
                },
                "LEAD": Object {
                  "displayName": "Teacher",
                  "displayNamePlural": "Teachers",
                },
                "TASK": Object {
                  "displayName": "Task",
                  "displayNamePlural": "Tasks",
                },
                "TEAM": Object {
                  "displayName": "Team",
                  "displayNamePlural": "Teams",
                },
                "USER": Object {
                  "displayName": "User",
                  "displayNamePlural": "Users",
                },
              },
              "isLogin": true,
              "loading": false,
              "token": "invalid",
              "userPreferences": Object {
                "dateFormat": "MMM D, YYYY [at] h:mm a",
                "numberFormat": "INDIAN_NUMBER_FORMAT",
                "timezone": "Asia/Calcutta",
              },
            }
          }
        >
          <Route>
            <Connect(SetupLayout)
              entity="ai"
              history={
                Object {
                  "action": "POP",
                  "block": [Function],
                  "createHref": [Function],
                  "go": [Function],
                  "goBack": [Function],
                  "goForward": [Function],
                  "length": 1,
                  "listen": [Function],
                  "location": Object {
                    "hash": "",
                    "pathname": "/",
                    "search": "",
                    "state": undefined,
                  },
                  "push": [Function],
                  "replace": [Function],
                }
              }
              location={
                Object {
                  "hash": "",
                  "pathname": "/",
                  "search": "",
                  "state": undefined,
                }
              }
              loginObj={
                Object {
                  "currentUserId": 123,
                  "currentUserName": "sant",
                  "entityLabelMap": Object {
                    "COMPANY": Object {
                      "displayName": "Company",
                      "displayNamePlural": "Companies",
                    },
                    "CONTACT": Object {
                      "displayName": "Student",
                      "displayNamePlural": "Contacts",
                    },
                    "DEAL": Object {
                      "displayName": "Deal",
                      "displayNamePlural": "Deals",
                    },
                    "LEAD": Object {
                      "displayName": "Teacher",
                      "displayNamePlural": "Teachers",
                    },
                    "TASK": Object {
                      "displayName": "Task",
                      "displayNamePlural": "Tasks",
                    },
                    "TEAM": Object {
                      "displayName": "Team",
                      "displayNamePlural": "Teams",
                    },
                    "USER": Object {
                      "displayName": "User",
                      "displayNamePlural": "Users",
                    },
                  },
                  "isLogin": true,
                  "loading": false,
                  "token": "invalid",
                  "userPreferences": Object {
                    "dateFormat": "MMM D, YYYY [at] h:mm a",
                    "numberFormat": "INDIAN_NUMBER_FORMAT",
                    "timezone": "Asia/Calcutta",
                  },
                }
              }
              match={
                Object {
                  "isExact": true,
                  "params": Object {},
                  "path": "/",
                  "url": "/",
                }
              }
            >
              <SetupLayout
                dispatch={[Function]}
                entity="ai"
                history={
                  Object {
                    "action": "POP",
                    "block": [Function],
                    "createHref": [Function],
                    "go": [Function],
                    "goBack": [Function],
                    "goForward": [Function],
                    "length": 1,
                    "listen": [Function],
                    "location": Object {
                      "hash": "",
                      "pathname": "/",
                      "search": "",
                      "state": undefined,
                    },
                    "push": [Function],
                    "replace": [Function],
                  }
                }
                location={
                  Object {
                    "hash": "",
                    "pathname": "/",
                    "search": "",
                    "state": undefined,
                  }
                }
                loginObj={
                  Object {
                    "authKey": null,
                    "currentUserId": NaN,
                    "currentUserName": null,
                    "emailId": null,
                    "entityLabelMap": Object {},
                    "isAccountLocked": false,
                    "isIPRestricted": false,
                    "isLoggedOutDueToExpiredToken": false,
                    "isLogin": false,
                    "isOtpInvalid": false,
                    "loading": false,
                    "resendOtpSuccess": false,
                    "token": "invalid",
                    "userPreferences": Object {
                      "dateFormat": "DD/MM/YYYY",
                      "numberFormat": "INDIAN_NUMBER_FORMAT",
                      "timezone": "Asia/Kolkata",
                    },
                  }
                }
                match={
                  Object {
                    "isExact": true,
                    "params": Object {},
                    "path": "/",
                    "url": "/",
                  }
                }
              >
                <div
                  className="d-flex flex-column h-100"
                >
                  <Connect(VerticalNavbar)
                    history={
                      Object {
                        "action": "POP",
                        "block": [Function],
                        "createHref": [Function],
                        "go": [Function],
                        "goBack": [Function],
                        "goForward": [Function],
                        "length": 1,
                        "listen": [Function],
                        "location": Object {
                          "hash": "",
                          "pathname": "/",
                          "search": "",
                          "state": undefined,
                        },
                        "push": [Function],
                        "replace": [Function],
                      }
                    }
                  >
                    <VerticalNavbar
                      headerList={Array []}
                      history={
                        Object {
                          "action": "POP",
                          "block": [Function],
                          "createHref": [Function],
                          "go": [Function],
                          "goBack": [Function],
                          "goForward": [Function],
                          "length": 1,
                          "listen": [Function],
                          "location": Object {
                            "hash": "",
                            "pathname": "/",
                            "search": "",
                            "state": undefined,
                          },
                          "push": [Function],
                          "replace": [Function],
                        }
                      }
                      isTenantUser={true}
                      navbar={
                        Object {
                          "activeChild": "",
                          "activeParent": "",
                        }
                      }
                      openedMarketplaceActions={Object {}}
                      resetSelectedPageAction={[Function]}
                      tenantUsage={null}
                    >
                      <aside
                        className="left-navbar"
                      >
                        <nav
                          className="left-navbar__Setup"
                        >
                          <ul
                            className="left-navbar__side-nav"
                          >
                            <li
                              className="left-navbar__side-nav__item api-guidelines border-top mt-auto mb-1"
                            >
                              <NavIcon
                                iconColor="#fff"
                                name="api-guidelines"
                              >
                                <APIGuidelinesIcon
                                  color="#fff"
                                  key="api guidelines"
                                >
                                  <svg
                                    height={12.798}
                                    viewBox="0 0 16 12.798"
                                    width={16}
                                  >
                                    <path
                                      d="M6.948,12.823,5.423,12.38a.3.3,0,0,1-.2-.372L8.63.254A.3.3,0,0,1,9,.049l1.525.442a.3.3,0,0,1,.2.372L7.32,12.618A.3.3,0,0,1,6.948,12.823Zm-2.85-2.8,1.087-1.16a.3.3,0,0,0-.02-.43L2.9,6.436,5.165,4.443a.3.3,0,0,0,.02-.43L4.1,2.854a.3.3,0,0,0-.425-.012L.071,6.216a.3.3,0,0,0,0,.437l3.6,3.377a.3.3,0,0,0,.425-.012Zm8.179.015,3.6-3.377a.3.3,0,0,0,0-.437l-3.6-3.38a.3.3,0,0,0-.425.012l-1.087,1.16a.3.3,0,0,0,.02.43L13.05,6.436,10.785,8.428a.3.3,0,0,0-.02.43l1.087,1.16a.3.3,0,0,0,.425.015Z"
                                      fill="#fff"
                                      transform="translate(0.025 -0.037)"
                                    />
                                  </svg>
                                </APIGuidelinesIcon>
                              </NavIcon>
                              <a
                                className="left-navbar__side-nav__item-section"
                                href="https://documenter.getpostman.com/view/3365774/UzBiQ8vJ"
                                rel="noopener noreferrer"
                                target="_blank"
                              >
                                Click for API guidelines
                              </a>
                            </li>
                          </ul>
                        </nav>
                      </aside>
                    </VerticalNavbar>
                  </Connect(VerticalNavbar)>
                  <main
                    className="main-content min-height-0 position-relative main-parent-wrapper left-nav-open"
                  >
                    <div
                      className="main-content-wrapper position-relative call-transcript-summary"
                    >
                      <TabsSection
                        history={
                          Object {
                            "location": Object {
                              "search": "",
                            },
                            "push": [MockFunction],
                          }
                        }
                        loading={false}
                        section="Transcript & Summary"
                        tabs={
                          Array [
                            Object {
                              "displayName": "Transcript & Summary",
                              "route": "/setup/ai/call-intelligence/transcript-summary",
                            },
                          ]
                        }
                      >
                        <div
                          className="nav nav-tabs header-tabs"
                          role="tablist"
                        >
                          <a
                            className="nav-item nav-link active"
                            data-toggle="tab"
                            href="#"
                            key="0"
                            onClick={[Function]}
                            role="tab"
                          >
                            Transcript & Summary
                          </a>
                        </div>
                      </TabsSection>
                      <div
                        className="page-content overflow-hidden min-height-0"
                      >
                        <div
                          className="page-inner-content h-100"
                        >
                          <div
                            className="top-wrapper min-height-0"
                          >
                            <h1
                              className="h1"
                            >
                              Call Transcript and Summary
                            </h1>
                            <div
                              className="sub-heading mt-1 mb-3"
                            >
                              Call recordings will now be converted into AI-powered transcripts and summaries, detailing sentiments, objections and action items. These summaries will be seamlessly integrated as notes within the call log and linked to the corresponding Teacher and Student.
                            </div>
                            <withRouter(ApiStateHandler)
                              ErrorFallbackComponent={[Function]}
                              SkeletonComponent={[Function]}
                              error={null}
                              loading={false}
                            >
                              <Route>
                                <ApiStateHandler
                                  ErrorFallbackComponent={[Function]}
                                  SkeletonComponent={[Function]}
                                  error={null}
                                  history={
                                    Object {
                                      "action": "POP",
                                      "block": [Function],
                                      "createHref": [Function],
                                      "go": [Function],
                                      "goBack": [Function],
                                      "goForward": [Function],
                                      "length": 1,
                                      "listen": [Function],
                                      "location": Object {
                                        "hash": "",
                                        "pathname": "/",
                                        "search": "",
                                        "state": undefined,
                                      },
                                      "push": [Function],
                                      "replace": [Function],
                                    }
                                  }
                                  loading={false}
                                  location={
                                    Object {
                                      "hash": "",
                                      "pathname": "/",
                                      "search": "",
                                      "state": undefined,
                                    }
                                  }
                                  match={
                                    Object {
                                      "isExact": true,
                                      "params": Object {},
                                      "path": "/",
                                      "url": "/",
                                    }
                                  }
                                >
                                  <Toogle
                                    disabled={false}
                                    handleClick={[Function]}
                                    id="callTranscriptSummaryToggle"
                                    label="Enable"
                                    value={true}
                                  >
                                    <div
                                      className="custom-control custom-switch "
                                      onClick={[Function]}
                                    >
                                      <input
                                        checked={true}
                                        className="custom-control-input"
                                        disabled={false}
                                        id="callTranscriptSummaryToggle"
                                        type="checkbox"
                                      />
                                      <label
                                        className="custom-control-label"
                                      >
                                        Enable
                                      </label>
                                    </div>
                                  </Toogle>
                                </ApiStateHandler>
                              </Route>
                            </withRouter(ApiStateHandler)>
                            <div
                              className="container"
                            >
                              <div
                                className="header"
                              >
                                Example Summary
                              </div>
                              <div>
                                Here’s a sample call showing how the summary will be generated once the feature is enabled.
                              </div>
                              <WaveformRecordingPlayer
                                audioUrl="https://assets.kylas.io/resources/call-summary-sample.mp3"
                                setIsRecordingPlayed={[Function]}
                              >
                                <div
                                  className="waveform-recording-player"
                                >
                                  <div
                                    className="play-pause-button"
                                    onClick={[Function]}
                                  >
                                    <img
                                      alt="play-icon"
                                      src="test-file-stub"
                                    />
                                  </div>
                                  <div
                                    id="waveform"
                                    style={
                                      Object {
                                        "width": "100%",
                                      }
                                    }
                                  />
                                </div>
                              </WaveformRecordingPlayer>
                            </div>
                            <div
                              className="workflow-setup"
                            >
                              <div
                                className="header"
                              >
                                Set It Up in Workflow Automation
                              </div>
                              <div
                                className="description"
                              >
                                To start generating call transcripts and summaries, add the relevant actions in your workflows. Once added, the system will automatically process calls based on your defined criteria. 
                                <a
                                  className="link-primary"
                                  href="https://support.kylas.io/portal/en"
                                  target="_blank"
                                >
                                  Learn More
                                </a>
                                <ul
                                  className="mt-2"
                                >
                                  <li>
                                    This feature delivers a complete 
                                    <strong>
                                      call transcript
                                    </strong>
                                     and a concise 
                                    <strong>
                                      AI summary
                                    </strong>
                                     highlighting key discussion points and 
                                    <strong>
                                      overall sentiments
                                    </strong>
                                    .
                                  </li>
                                  <li>
                                    It also intelligently identifies action items, automatically creates tasks linked to the relevant records.
                                  </li>
                                </ul>
                              </div>
                              <div
                                className="link-primary"
                                onClick={[Function]}
                              >
                                Go to Workflow Setup 
                                <img
                                  alt="new-tab-icon"
                                  className="new-tab-icon"
                                  src="test-file-stub"
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </main>
                </div>
              </SetupLayout>
            </Connect(SetupLayout)>
          </Route>
        </withRouter(Connect(SetupLayout))>
      </CallTranscriptSummary>
    </Router>
  </BrowserRouter>
</Provider>
`;

import * as React from 'react';
import { mount } from 'enzyme';
import to<PERSON>son from 'enzyme-to-json';

import { CampaignActivityRecipientStatusType } from '../../page/Campaign/model';

import { getCampaignActivityRecipientStatusTableHeaders } from '../../page/Campaign/CampaignActivityRecipientStatusList/TableHeaders';

import { ChipDropdown } from './ChipDropdown';

describe('ChipDropdown component', () => {
  const defaultProps = {
    value: CampaignActivityRecipientStatusType.ALL,
    options: getCampaignActivityRecipientStatusTableHeaders().find(f => f.id === 'status').picklist.picklistValues,
    onValueChange: jest.fn()
  };

  let component;
  beforeEach(() => {
    jest.clearAllMocks();
    component = mount(<ChipDropdown {...defaultProps} />);
  });

  it('should render correctly', () => {
    // @ts-ignore
    expect(toJson(component)).toMatchSnapshot();
  });

  it('should call onValueChange when an option is selected', () => {
    component.find('.chip-dropdown__trigger').simulate('click');
    component.update();

    component.find('.chip-dropdown__option').at(1).simulate('click');
    component.update();

    expect(defaultProps.onValueChange).toHaveBeenCalledWith(CampaignActivityRecipientStatusType.SENT);
    expect(component.find('.chip-dropdown__menu').length).toEqual(0);
  });

  it('should use first option as default when value is null', () => {
    component.setProps({ value: null });
    expect(component.find('.chip-dropdown__label').text()).toBe('All');
  });
});

import * as React from 'react';
import classnames from 'classnames';
import <PERSON>Click<PERSON><PERSON><PERSON> from 'react-outside-click-handler';

import { PicklistValue } from '../ListAction/PickListModal';

import './_chip-dropdown.scss';

interface Props {
  value: string;
  options: PicklistValue[];
  onValueChange: (value: string) => void;
}

export const ChipDropdown: React.FC<Props> = ({ options, value, onValueChange }) => {
  const [isOpen, setIsOpen] = React.useState<boolean>(false);
  const [selectedValue, setSelectedValue] = React.useState<string>(value ?? options[0].id as string);

  const toggleDropdown = () => {
    setIsOpen(prevState => !prevState);
  };

  return (
    <OutsideClickHandler disabled={false} onOutsideClick={() => setIsOpen(false)}>
      <div className={classnames('chip-dropdown', { 'chip-dropdown--open': isOpen })}>
        <div
          tabIndex={0}
          role="button"
          onClick={toggleDropdown}
          className="chip-dropdown__trigger"
        >
          <span className="chip-dropdown__label">{(options?.find(f => f.id === selectedValue))?.displayName}</span>

          <i className="fas fa-caret-down" />
        </div>

        { isOpen && (
          <div className="chip-dropdown__menu">
            { options.map(val => (
                <div
                  role="option"
                  key={val.id as string}
                  aria-selected={val.id === selectedValue}
                  className={classnames('chip-dropdown__option', { 'chip-dropdown__option--selected': val.id === selectedValue })}
                  onClick={() => {
                    setIsOpen(false);

                    onValueChange(val.id as string);
                    setSelectedValue(val.id as string);
                  }}
                >
                  {val.displayName}
                </div>
              ))
            }
          </div>
        )}
      </div>
    </OutsideClickHandler>
  );
};

export default ChipDropdown;

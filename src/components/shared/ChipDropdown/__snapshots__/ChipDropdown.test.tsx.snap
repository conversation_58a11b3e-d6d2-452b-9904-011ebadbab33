// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ChipDropdown component should render correctly 1`] = `
<ChipDropdown
  onValueChange={[MockFunction]}
  options={
    Array [
      Object {
        "displayName": "All",
        "id": "ALL",
        "name": "ALL",
      },
      Object {
        "displayName": "Sent",
        "id": "SENT",
        "name": "SENT",
      },
      Object {
        "displayName": "Read",
        "id": "READ",
        "name": "READ",
      },
      Object {
        "displayName": "Delivered",
        "id": "DELIVERED",
        "name": "DELIVERED",
      },
      Object {
        "displayName": "Failed",
        "id": "FAILED",
        "name": "FAILED",
      },
    ]
  }
  value="ALL"
>
  <OutsideClickHandler
    disabled={false}
    display="block"
    onOutsideClick={[Function]}
    useCapture={true}
  >
    <div>
      <div
        className="chip-dropdown"
      >
        <div
          className="chip-dropdown__trigger"
          onClick={[Function]}
          role="button"
          tabIndex={0}
        >
          <span
            className="chip-dropdown__label"
          >
            All
          </span>
          <i
            className="fas fa-caret-down"
          />
        </div>
      </div>
    </div>
  </OutsideClickHandler>
</ChipDropdown>
`;

@import 'src/assets/styles/scss/base/_variables.scss';

.chip-dropdown {
  position: relative;
  display: inline-block;

  &__trigger {
    display: flex;
    cursor: pointer;
    min-width: 5rem;
    color: #374151;
    align-items: center;
    font-size: 0.875rem;
    border-radius: 1.25rem;
    background-color:$white;
    padding: 0.375rem 0.75rem;
    border: 1px solid #d1d5db;
    justify-content: space-between;
    transition: all 0.2s ease-in-out;

    &:hover {
      border-color: #9ca3af;
      background-color: #f9fafb;
    }

    &:focus {
      outline: none;
      border-color: $primary;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    i {
      color: #6b7280;
      font-size: 0.75rem;
      margin-left: 0.5rem;
      transition: transform 0.2s ease-in-out;
    }
  }

  &__label {
    flex: 1;
    font-size: 0.8rem;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  &__menu {
    left: 0;
    right: 0;
    top: 100%;
    width: 10rem;
    overflow-y: auto;
    position: absolute;
    margin-top: 0.25rem;
    max-height: 12.5rem;
    border-radius: 0.5rem;
    background-color: $white;
    border: 1px solid #d1d5db;
    animation: fadeIn 0.15s ease-out;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }

  &__option {
    cursor: pointer;
    color: #374151;
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
    transition: background-color 0.15s ease-in-out;

    &:hover {
      background-color: #f3f4f6;
    }

    &:first-child, &:last-child {
      border-top-left-radius: 0.5rem;
      border-top-right-radius: 0.5rem;
    }

    &--selected {
      color: $primary;
      background-color: #eff6ff;

      &:hover {
        background-color: #dbeafe;
      }
    }
  }

  &--open {
    .chip-dropdown__trigger {
      border-color: $primary;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

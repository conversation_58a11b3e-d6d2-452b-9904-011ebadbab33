import * as React from 'react';
import thunk from 'redux-thunk';
import { Provider } from 'react-redux';
import { shallow, mount } from 'enzyme';
import configureStore from 'redux-mock-store';
import { BrowserRouter as Router } from 'react-router-dom';

jest.mock('react-beautiful-dnd', () => ({
  Droppable: ({ children }) => children({
    draggableProps: {
      style: {}
    },
    innerRef: jest.fn()
  },                                    {}),
  Draggable: ({ children }) => children({
    draggableProps: {
      style: {}
    },
    innerRef: jest.fn()
  },                                    {}),
  DragDropContext: ({ children }) => children
}));

const debounceMock = jest.fn();
jest.mock('lodash/debounce', () => fn => debounceMock);

const getEntityDetailsMock = jest.fn();
const getDealValueActionMock = jest.fn();
jest.mock('../EntityDetailsTooltip/EntityDetailsAction/EntityDetailsAction', () => ({
  getEntityDetailsAction: getEntityDetailsMock,
  getDealValueAction: getDealValueActionMock
}));

import { ListLayout } from './models/ListLayout';
import { SortOrder } from './models/ListingTable';
import { DiscountType } from '../Input/Products/model';
import { FieldTypes } from '../../page/FieldSettings/models/Field';
import { CampaignActivityRecipientStatusType } from '../../page/Campaign/model';

import { entities } from '../../../utils/constants';
import { storeData } from '../../../store/mockStore';
import { READ, UPDATE } from '../../../utils/permissionUtil';
import { mockUserShiftList } from '../../page/UserShifts/stub';
import { dummyMarketplaceActions } from '../../products/marketplace/stub';
import { mockGetCampaignActivityRecipientStatusList } from '../../page/Campaign/stub';
import { apiKeysTableHeaders } from '../../page/ApiKeys/components/list/tableHeaders';
import { getUserShiftTableHeaders } from '../../page/UserShifts/UserShiftList/TableHeaders';
import { productsServicesTableHeaders } from '../../page/ProductsServices/Components/ProductsList/tableHeaders';
import { mockGetWorkflowActionLogsListResponse, mockWorkflowActionLogsListLayout } from '../../page/WorkflowActionLogs/stubs';
import { getCampaignActivityRecipientStatusTableHeaders } from '../../page/Campaign/CampaignActivityRecipientStatusList/TableHeaders';

import { ListingTable } from './ListingTable';
import { TableDataFormatter } from './TableDataFormatter';
import { RedirectionForScheduledJobs } from '../RedirectionForScheduledJobs/RedirectionForScheduledJobs';
import Pagination from '../Table/Pagination';
import BulkActions from '../BulkActions/BulkActions';
import ChipDropdown from '../ChipDropdown/ChipDropdown';
import MultiActionModal from '../Input/MultiActionModal';
import GoalValuesTable from '../GoalValuesTable/GoalValuesTable';
import ReadOnlyProducts from '../ReadOnlyProducts/ReadOnlyProducts';
import EntityInfoTooltipOnListingPages from '../EntityInfoTooltipOnListingPages/EntityInfoTooltipOnListingPages';
import LatestNotesModal from '../Table/LatestNotesModal/LatestNotesModal';

describe('Listing Table Component', () => {
  const updateSortOrderMock = jest.fn();
  const updateCurrentPageMock = jest.fn();

  const props = {
    error: '',
    currentPage: 1,
    entity: 'deals',
    currentUserId: 789,
    sortOrder: undefined,
    selectedRecord: null,
    shouldSaveSort: true,
    sortByField: undefined,
    selectedRecordDetailsUrl: '',
    rowClickRequiredPermission: [UPDATE],
    headers: productsServicesTableHeaders,
    profilePermissions: storeData.appData.profilePermissions,
    history: {
      push: jest.fn()
    },
    smartListRules: {
      deals: {
        id: 1,
        name: 'My Custom Smartlist',
        size: 30,
        searchRequest: {
          jsonRule: null
        },
        recordActions: {
          read: true,
          update: true
        }
      }
    },
    data: {
      content: [{
        isActive: true,
        description: 'This is test',
        id: 1,
        name: '',
        price: {
          currency: {
            id: 400,
            code: 'USD'
          },
          value: 1234
        },
        recordActions: {
          update: true
        },
        ownerId: 214
      }],
      totalElements: 20,
      totalPages: 10
    },
    onClickRow: jest.fn(),
    updateColumnOrder: jest.fn(),
    onClickAddFilters: jest.fn(),
    resetEntityDetails: jest.fn(),
    removeSelectedRecordAction: jest.fn(),
    setPreferredSmartListAction: jest.fn(),
    removeRecordDetailsUrlAction: jest.fn(),
    updateSortOrder: updateSortOrderMock,
    updateCurrentPage: updateCurrentPageMock,
    RowOptions: () => [<li key="a" id="li_a">a</li>, <li key="b" id="li_b">b</li>]
  };

  const mockmiddlewares = [thunk];
  const mockStore = configureStore(mockmiddlewares);

  const store = mockStore(storeData);

  const requiredColumns = [
    {
      id: 'name',
      header: 'Name',
      isFilterable: true,
      isSortable: true,
      isDefault: true
    },
    {
      id: 'description',
      header: 'Description',
      isFilterable: false,
      isSortable: false,
      isDefault: true
    },
    {
      id: 'isActive',
      header: 'Status',
      isFilterable: true,
      isSortable: true,
      isDefault: true
    },
    {
      id: 'price',
      header: 'Price',
      isFilterable: true,
      isSortable: true,
      isDefault: true
    }
  ];

  const getEntityDetailsPromise = Promise.resolve({ data: { id: 1, name: 'Test company' } });
  getEntityDetailsMock.mockImplementation(() => () => getEntityDetailsPromise);

  const getDealValueActionPromise = Promise.resolve({ data: { numberOfDeals: 0 } });
  getDealValueActionMock.mockImplementation(() => () => getDealValueActionPromise);

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render all required columns', () => {
    const wrapper = shallow(<ListingTable {...props} />);
    wrapper.update();
    const reactTableComponent = wrapper.find('ReactTableFixedColumns');
    const tableProps = reactTableComponent.props();
    expect(tableProps.columns.length).toEqual(requiredColumns.length + 1);
    const receivedColumns = reactTableComponent.props().columns.map(column => column.id);
    const expectedColumns = requiredColumns.map(column => column.id);
    expectedColumns.push('option');
    expect(receivedColumns).toEqual(expectedColumns);

    expect(tableProps.data).toEqual(
      [
        {
          isActive: true,
          description: 'This is test',
          id: 1,
          name: '',
          price: {
            currency: {
              id: 400,
              code: 'USD'
            },
            value: 1234
          },
          recordActions: {
            update: true
          },
          ownerId: 214
        }
      ]
    );
  });

  it('should show the list row options', () => {
    const prodOne = { ...props.data.content[0], recordActions: { read: true, update: true } };
    const newProps = { ...props, data: { content: [prodOne], totalElements: 20, totalPages: 10 } };
    const wrapper = shallow(<ListingTable {...newProps} />);
    const reactTableComponent = wrapper.find('ReactTableFixedColumns');

    const listRowsOptions = reactTableComponent.dive().dive().find('ListRowsOptions');
    expect(listRowsOptions.length).toEqual(1);
  });

  it('should call debounce function when column is resized', () => {
    const wrapper = shallow(<ListingTable {...props} />);
    const reactTableComponent = wrapper.find('ReactTableFixedColumns');

    reactTableComponent.props().onResizedChange([{ id: 'id', value: 223 }]);

    expect(debounceMock).toHaveBeenCalledWith([{ id: 'id', value: 223 }]);
  });

  it('should set column width stored in local storage for particular column or else show default width', () => {
    jest.clearAllMocks();
    localStorage.setItem('columnProperties', JSON.stringify({
      789: {
        deals : {
          phones: {
            width: 334
          },
          city: {
            width: 190
          }
        }
      }
    }));
    const headers = [
      {
        id: 'emails',
        header: 'Emails',
        isFilterable: false,
        isSortable: true,
        fieldType: 'EMAIL',
        isStandard: true
      },
      {
        id: 'phones',
        header: 'Phones',
        isFilterable: false,
        isSortable: true,
        fieldType: 'PHONE',
        isStandard: true
      },
      {
        id: 'city',
        header: 'City',
        isFilterable: false,
        isSortable: true,
        fieldType: 'TEXT_FIELD',
        isStandard: true
      }
    ];

    const newProps = {
      ...props,
      headers,
      data: {
        content: [{
          id: 1,
          name: 'ABC corporation',
          recordActions: {
            update: true
          }
        }],
        totalElements: 20,
        totalPages: 10
      },
      currentUserId: 789,
      entity: 'deals',
      marketplaceCallActions: [dummyMarketplaceActions[0]]
    };
    const wrapper = shallow(<ListingTable {...newProps} />);
    const reactTableComponent = wrapper.find('ReactTableFixedColumns');

    expect(reactTableComponent.props().columns[0].id).toEqual('emails');
    expect(reactTableComponent.props().columns[0].minWidth).toEqual(120);

    expect(reactTableComponent.props().columns[1].id).toEqual('phones');
    expect(reactTableComponent.props().columns[1].minWidth).toEqual(334);

    expect(reactTableComponent.props().columns[2].id).toEqual('city');
    expect(reactTableComponent.props().columns[2].minWidth).toEqual(190);
  });

  it('should show the formatted content if present', () => {
    const wrapper = shallow(<ListingTable {...props} />);
    const reactTableComponent = wrapper.find('ReactTableFixedColumns');
    const tdComponent = reactTableComponent.dive().dive().find('Tbody').find('TdComponent');
    expect(tdComponent.at(0).find(TableDataFormatter).props()).toEqual({
      type: 'TEXT_FIELD',
      column: props.headers[0],
      row: props.data.content[0]
    });
    expect(tdComponent.at(1).find(TableDataFormatter).props()).toEqual({
      type: 'PARAGRAPH_TEXT',
      column: props.headers[1],
      row: props.data.content[0]
    });
    expect(tdComponent.at(2).find('div').text()).toEqual('Active');
    expect(tdComponent.at(3).find('div').text()).toEqual('USD 1,234');
  });

  it('should show the formatted content if column does not have "formattedValue" function', () => {
    const headers = [
      {
        id: 'numberOfEmployees',
        header: 'Employees',
        isFilterable: false,
        isSortable: true,
        fieldType: 'PICK_LIST',
        isStandard: true
      },
      {
        id: 'city',
        header: 'City',
        isFilterable: false,
        isSortable: true,
        fieldType: 'TEXT_FIELD',
        isStandard: true
      },
      {
        id: 'annualRevenue',
        header: 'Revenue',
        isFilterable: false,
        isSortable: true,
        fieldType: 'MONEY',
        isStandard: true
      }
    ];

    const newProps = {
      ...props,
      headers,
      data: {
        ...props.data,
        content: [{
          numberOfEmployees: {
            name: '10-50',
            id: 5
          },
          city: 'city',
          annualRevenue: {
            currencyId: 400,
            value: 1234
          }
        }]
      }
    };
    const wrapper = shallow(<ListingTable {...newProps} />);
    const reactTableComponent = wrapper.find('ReactTableFixedColumns');
    const tdComponent = reactTableComponent.dive().dive().find('Tbody').find('TdComponent');
    expect(tdComponent.at(0).find(TableDataFormatter).props()).toEqual({
      type: 'PICK_LIST',
      column: headers[0],
      row: newProps.data.content[0]
    });
    expect(tdComponent.at(1).find(TableDataFormatter).props()).toEqual({
      type: 'TEXT_FIELD',
      column: headers[1],
      row: newProps.data.content[0]
    });
    expect(tdComponent.at(2).find(TableDataFormatter).props()).toEqual({
      type: 'MONEY',
      column: headers[2],
      row: newProps.data.content[0]
    });
  });

  it('should show the formatted content for PHONE and EMAIL', () => {
    const headers = [
      {
        id: 'numberOfEmployees',
        header: 'Employees',
        isFilterable: false,
        isSortable: true,
        fieldType: 'PICK_LIST',
        isStandard: true
      },
      {
        id: 'emails',
        header: 'Emails',
        isFilterable: false,
        isSortable: true,
        fieldType: 'EMAIL',
        isStandard: true
      },
      {
        id: 'phoneNumbers',
        header: 'Phone Numbers',
        isFilterable: false,
        isSortable: true,
        fieldType: 'PHONE',
        isStandard: true
      }
    ];

    const newProps = {
      ...props,
      headers,
      data: {
        ...props.data,
        content: [{
          numberOfEmployees: {
            name: '10-50',
            id: 5
          },
          city: 'city',
          annualRevenue: {
            currencyId: 400,
            value: 1234
          }
        }]
      }
    };
    const wrapper = shallow(<ListingTable {...newProps} />);
    const reactTableComponent = wrapper.find('ReactTableFixedColumns');
    const tdComponent = reactTableComponent.dive().dive().find('Tbody').find('TdComponent');
    expect(tdComponent.at(0).find(TableDataFormatter).props()).toEqual({
      type: 'PICK_LIST',
      column: headers[0],
      row: newProps.data.content[0]
    });
    expect(tdComponent.at(1).find('ReadOnlyMultiValue').props().itemType).toEqual(FieldTypes.EMAIL);
    expect(tdComponent.at(2).find('ReadOnlyMultiValue').props().itemType).toEqual(FieldTypes.PHONE);
    expect(tdComponent.at(2).find('ReadOnlyMultiValue').props().fieldName).toEqual('phoneNumbers');
  });

  it('should pass props required for marketplace action for PHONE field', () => {
    const headers = [
      {
        id: 'emails',
        header: 'Emails',
        isFilterable: false,
        isSortable: true,
        fieldType: 'EMAIL',
        isStandard: true
      },
      {
        id: 'phones',
        header: 'Phones',
        isFilterable: false,
        isSortable: true,
        fieldType: 'PHONE',
        isStandard: true
      }
    ];

    const newProps = {
      ...props,
      headers,
      data: {
        content: [{
          id: 1,
          name: 'ABC corporation',
          recordActions: {
            update: true
          }
        }],
        totalElements: 20,
        totalPages: 10
      },
      entity: 'companies',
      marketplaceCallActions: [dummyMarketplaceActions[0]]
    };
    const wrapper = shallow(<ListingTable {...newProps} />);
    const reactTableComponent = wrapper.find('ReactTableFixedColumns');
    const tdComponent = reactTableComponent.dive().dive().find('Tbody').find('TdComponent');

    const emailField = tdComponent.at(0).find('ReadOnlyMultiValue');
    const phoneField = tdComponent.at(1).find('ReadOnlyMultiValue');

    expect(emailField.props().itemType).toEqual(FieldTypes.EMAIL);
    expect(emailField.props().entity).toEqual(undefined);
    expect(emailField.props().entityId).toEqual(undefined);
    expect(emailField.props().marketplaceActions).toEqual(undefined);

    expect(phoneField.props().itemType).toEqual(FieldTypes.PHONE);
    expect(phoneField.props().entity).toEqual('companies');
    expect(phoneField.props().entityId).toEqual(1);
    expect(phoneField.props().entityName).toEqual('ABC corporation');
    expect(phoneField.props().marketplaceActions).toEqual(newProps.marketplaceCallActions);

    expect(reactTableComponent.props().columns[1].id).toBe('phones');
    expect(reactTableComponent.props().columns[1].minWidth).toBe(120);
  });

  it('should freeze columns till freezedColumn in smartList', () => {
    const newProps = {
      ...props,
      smartListRules: {
        deals: {
          ...props.smartListRules.deals,
          freezedColumn: 'isActive'
        }
      }
    };
    const wrapper = shallow(<ListingTable {...newProps} />);
    const tableComponent = wrapper.find('ReactTableFixedColumns');

    expect(tableComponent.props().columns[0].id).toBe('name');
    expect(tableComponent.props().columns[0].fixed).toBe('left');

    expect(tableComponent.props().columns[1].id).toBe('description');
    expect(tableComponent.props().columns[1].fixed).toBe('left');

    expect(tableComponent.props().columns[2].id).toBe('isActive');
    expect(tableComponent.props().columns[2].fixed).toBe('left');

    expect(tableComponent.props().columns[3].id).toBe('price');
    expect(tableComponent.props().columns[3].fixed).toBe('');
  });

  it('should not freeze columns if there is no freezedColumn in smartList', () => {
    const newProps = {
      ...props,
      smartListRules: {
        deals: {
          ...props.smartListRules.deals,
          freezedColumn: ''
        }
      }
    };
    const wrapper = shallow(<ListingTable {...newProps} />);
    const tableComponent = wrapper.find('ReactTableFixedColumns');

    expect(tableComponent.props().columns[0].id).toBe('name');
    expect(tableComponent.props().columns[0].fixed).toBe('');

    expect(tableComponent.props().columns[1].id).toBe('description');
    expect(tableComponent.props().columns[1].fixed).toBe('');

    expect(tableComponent.props().columns[2].id).toBe('isActive');
    expect(tableComponent.props().columns[2].fixed).toBe('');

    expect(tableComponent.props().columns[3].id).toBe('price');
    expect(tableComponent.props().columns[3].fixed).toBe('');
  });

  it('should call setPreferredSmartListAction with freezedColumn as payload', () => {
    const newProps = {
      ...props,
      entity: entities.DEALS,
      smartListRules: {
        deals: {
          ...props.smartListRules.deals,
          id: 1,
          freezedColumn: ''
        }
      }
    };
    const wrapper = shallow(<ListingTable {...newProps} />);
    const columnHeaderComponent = shallow(wrapper.find('ReactTableFixedColumns').props().columns[1].Header);

    columnHeaderComponent.find(MultiActionModal).props().options[2].action();
    expect(newProps.setPreferredSmartListAction).toHaveBeenCalledWith(1, 'deals', { freezedColumn: 'description' }, undefined);
  });

  it('should render ReadOnlyMultiValueLookup if multiValue is true and fetch contact details when not already present', () => {
    const headers = [
      {
        id: 'associatedContacts',
        header: 'Contacts',
        isFilterable: false,
        isSortable: true,
        multiValue: true,
        fieldType: 'LOOK_UP',
        isStandard: true
      }];

    const newProps = {
      ...props,
      headers,
      data: {
        ...props.data,
        content: [{
          associatedContacts: [
            {
              name: 'Contact 1',
              id: 1
            },
            {
              name: 'Contact 2',
              id: 2
            }
          ]
        }]
      }
    };
    const wrapper = shallow(<ListingTable {...newProps} />);
    const reactTableComponent = wrapper.find('ReactTableFixedColumns');
    const tdComponent = reactTableComponent.dive().dive().find('Tbody').find('TdComponent');
    expect(tdComponent.at(0).find('ReadOnlyMultiValueLookup').props()).toEqual({
      values: newProps.data.content[0].associatedContacts,
      textWrapOrClip: 'clip-text',
      entity: 'deals',
      fieldName: 'associatedContacts'

    });
  });

  it('should render ReadOnlyMultiValueLookup for multi-value picklist', () => {
    const headers = [
      {
        id: 'cfCustomMultiPickList',
        header: 'Custom Multi PickList',
        isFilterable: false,
        isSortable: false,
        multiValue: true,
        fieldType: 'MULTI_PICKLIST',
        isStandard: false
      }];

    const newProps = {
      ...props,
      headers,
      data: {
        ...props.data,
        content: [{
          customFieldValues: {
            cfCustomMultiPickList: [
              {
                name: 'Value 1',
                id: 1
              },
              {
                name: 'Value 2',
                id: 2
              }
            ]
          }
        }]
      }
    };
    const wrapper = shallow(<ListingTable {...newProps} />);
    const reactTableComponent = wrapper.find('ReactTableFixedColumns');
    const tdComponent = reactTableComponent.dive().dive().find('Tbody').find('TdComponent');
    expect(tdComponent.at(0).find('ReadOnlyMultiValueLookup').props()).toEqual({
      values: newProps.data.content[0].customFieldValues.cfCustomMultiPickList,
      textWrapOrClip: 'clip-text',
      fieldName: 'cfCustomMultiPickList',
      entity: 'deals'
    });
  });

  it('should render RedirectionForScheduledJobs component if entity is scheduled jobs and header is entity id', () => {
    const headers = [
      {
        id: 'entityId',
        header: 'Entity Id',
        isFilterable: false,
        isSortable: true,
        multiValue: true,
        fieldType: 'ID',
        isStandard: true
      }];

    const newProps = {
      ...props,
      headers,
      data: {
        ...props.data,
        content: [{
          entityId: 123
        }]
      },
      entity: entities.SCHEDULED_JOBS
    };

    const wrapper = shallow(<ListingTable {...newProps} />);
    const reactTableComponent = wrapper.find('ReactTableFixedColumns');
    const tdComponent = reactTableComponent.dive().dive().find('Tbody').find('TdComponent');
    const redirectionForScheduledJobsComponent = tdComponent.at(0).find(RedirectionForScheduledJobs);
    expect(reactTableComponent.length).toEqual(1);
    expect(redirectionForScheduledJobsComponent.length).toEqual(1);
    // @ts-ignore
    expect(JSON.stringify(redirectionForScheduledJobsComponent.props())).toEqual(JSON.stringify({
      row: { entityId: 123 },
      column: {
        id: 'entityId',
        header: 'Entity Id',
        isFilterable: false,
        isSortable: true,
        multiValue: true,
        fieldType: 'ID',
        isStandard: true
      },
      history: {
        push: jest.fn()
      },
      metaData: undefined,
      profilePermissions: props.profilePermissions
    }));
  });

  it('should render Goal values table component entity is goals and header is goal value', () => {
    const headers = [
      {
        id: 'value',
        header: 'Goal Value',
        isFilterable: false,
        isSortable: true,
        multiValue: true,
        fieldType: 'NUMBER',
        isStandard: true
      }];

    const newProps = {
      ...props,
      headers,
      data: {
        ...props.data,
        content: [{
          fieldValues: [
            {
              fieldName: 'owner',
              fieldType: 'USER',
              fieldValueId: 1,
              fieldValueName: 'user one',
              value: 1000
            },
            {
              fieldName: 'owner',
              fieldType: 'USER',
              fieldValueId: 2,
              fieldValueName: 'user one',
              value: 1000
            }
          ],
          value: 10000
        }]
      },
      entity: entities.GOALS
    };
    const wrapper = shallow(<ListingTable {...newProps} />);
    const reactTableComponent = wrapper.find('ReactTableFixedColumns');
    const tdComponent = reactTableComponent.dive().dive().find('Tbody').find('TdComponent');
    expect(tdComponent.at(0).find(GoalValuesTable).props()).toEqual({
      goalDetails: newProps.data.content[0]
    });
  });

  it('should render EntityInfoTooltipOnListingPages when header is company and fetch company details if not already fetched', async () => {
    const headers = [
      {
        id: 'company',
        header: 'Company',
        isFilterable: false,
        isSortable: true,
        multiValue: false,
        fieldType: 'LOOK_UP',
        isStandard: true
      }];

    const newProps = {
      ...props,
      headers,
      data: {
        ...props.data,
        content: [{
          company: {
            id: 11,
            name: 'Test Company'
          }
        },
          {
            company: {
              id: 12,
              name: 'Test Company 2'
            }
          }]
      },
      entity: entities.CONTACTS
    };
    const wrapper = shallow(<ListingTable {...newProps} />);
    const reactTableComponent = wrapper.find('ReactTableFixedColumns');
    const tdComponent = reactTableComponent.dive().dive().find('Tbody').find('TdComponent');

    expect(tdComponent.at(0).find(EntityInfoTooltipOnListingPages).length).toBe(1);
    expect(tdComponent.at(0).find(EntityInfoTooltipOnListingPages).props()).toEqual({
      entityType: 'companies',
      value: 'Test Company',
      entityId: 11,
      textWrapOrClip: 'clip-text',
      valueToAddInPosX: 8,
      valueToSubtractFromPosY: 88
    });
  });

  it('should trigger onClickRow if update permission is true', () => {
    const wrapper = shallow(<ListingTable {...props} />);
    const reactTableComponent = wrapper.find('ReactTableFixedColumns');
    const tdComponent = reactTableComponent.dive().dive().find('Tbody').find('TdComponent');
    const event = { ctrlKey: false };

    tdComponent.at(2).simulate('click', event);

    expect(props.onClickRow).toHaveBeenCalledWith(
      {
        description: 'This is test',
        id: 1,
        isActive: true,
        name: '',
        price: {
          currency: {
            id: 400,
            code: 'USD'
          },
          value: 1234
        },
        ownerId: 214,
        recordActions: { update: true }
      },
      {
        ctrlKey: false
      }
    );
  });

  it('should not trigger onClickRow if update permission is false', () => {
    const newProps = {
      ...props,
      onClickRow: jest.fn(),
      data: {
        content: [{
          isActive: true,
          description: 'This is test',
          id: 1,
          name: '',
          price: {
            currency: {
              id: 400,
              code: 'USD'
            },
            value: 1234
          },
          recordActions: {
            update: false
          }
        }],
        totalElements: 20,
        totalPages: 10
      }
    };

    const wrapper = shallow(<ListingTable {...newProps} />);
    const reactTableComponent = wrapper.find('ReactTableFixedColumns');
    const tdComponent = reactTableComponent.dive().dive().find('Tbody').find('TdComponent');

    tdComponent.at(2).simulate('click');

    expect(newProps.onClickRow).not.toHaveBeenCalled();
  });

  it('should route user list instead of shift details page on click assignedUsers field on shift listing page', () => {
    const originalWindow = window;
    window.open = jest.fn();

    const dateFormat = storeData.loginForm.userPreferences.dateFormat;
    const timezone = storeData.loginForm.userPreferences.timezone;
    const timezonePickListValue = storeData.appData.standardPickList.TIMEZONE;

    const newProps = {
      ...props,
      entity: 'shifts',
      data: mockUserShiftList,
      rowClickRequiredPermission: [READ],
      headers: getUserShiftTableHeaders(dateFormat, timezone, timezonePickListValue)
    };

    const wrapper = shallow(<ListingTable {...newProps} />);

    wrapper.find('ReactTableFixedColumns').props().getTdProps(null, { original: { assignUsers: 2, id: 1, recordActions: { write: true, read: true } } }, { id: 'assignedUsers' }, null).onClick({ target: { className: 'assigned-users' } });

    expect(newProps.history.push).not.toHaveBeenCalled();

    const assignedUsersColumnData = wrapper.find('ReactTableFixedColumns').props().columns.find(f => f.id === 'assignedUsers');
    assignedUsersColumnData.accessor(mockUserShiftList.content[0]).props.onClick({ stopPropagation: jest.fn(), ctrlKey: true });

    expect(window.open).toHaveBeenCalledWith('/setup/users/list?shift=1', '_blank');

    window = originalWindow;
  });

  it('should modify column if column id is `apiKey`', () => {
    const apiKeyOptionsMock = jest.fn();

    const newProps = {
      ...props,
      headers: apiKeysTableHeaders,
      apiKeyOptions: apiKeyOptionsMock
    };

    const wrapper = shallow(<ListingTable {...newProps} />);

    const apiKeyColumn = wrapper.find('ReactTableFixedColumns').props().columns[1];

    expect(apiKeyColumn.minWidth).toBe(200);

    apiKeyColumn.Cell({});

    expect(apiKeyOptionsMock).toHaveBeenCalled();
  });

  it('should not show options column if RowOptions are undefined', () => {
    const wrapper = shallow(<ListingTable {...props} RowOptions={undefined}/>);

    const columns = wrapper.find('ReactTableFixedColumns').props().columns;

    expect(columns.find(col => col.id === 'option')).not.toBeDefined();
  });

  it('should render ViewProductImages component for products images', () => {
    const headers = [
      {
        id: 'productImages',
        header: 'Product Images',
        isFilterable: true,
        isSortable: false,
        fieldType: 'IMAGE',
        isStandard: true,
        isDefault: true
      }
    ];

    const newProps = {
      ...props,
      headers,
      layout: [...headers],
      data: {
        content: [{
          id: 1,
          name: 'test product',
          recordActions: {
            update: true
          },
          productImages: [
            {
              id: 1,
              name: 'Image 1',
              url: 'https://product-image.sgp1.digitaloceanspaces.com/image1',
              default: false
            },{
              id: 2,
              name: 'Image 2',
              url: 'https://product-image.sgp1.digitaloceanspaces.com/image2',
              default: false
            },{
              id: 3,
              name: 'Image 3',
              url: 'https://product-image.sgp1.digitaloceanspaces.com/image3',
              default: true
            }
          ]
        }],
        totalElements: 20,
        totalPages: 10
      },
      entity: 'products-services',
      marketplaceCallActions: [dummyMarketplaceActions[0]]
    };
    const wrapper = shallow(<ListingTable {...newProps} />);
    const reactTableComponent = wrapper.find('ReactTableFixedColumns');
    const tdComponent = reactTableComponent.dive().dive().find('Tbody').find('TdComponent');

    const viewProductImagesComponent = tdComponent.at(0).find('ViewProductImages');
    expect(reactTableComponent.length).toEqual(1);
    expect(viewProductImagesComponent.props()).toEqual({
      productImages: newProps.data.content[0].productImages,
      viewProductImagesOnEditPage: false
    });
  });

  it('should render ReadOnlyProductsComponent for deal products', () => {
    const headers = [
      {
        id: 'products',
        header: 'Product or Services',
        isFilterable: true,
        isSortable: false,
        fieldType: 'LOOK_UP',
        isStandard: true,
        isDefault: true
      }
    ];

    const layout = [
      ...headers,
      {
        id: 'discount',
        header: 'Discount',
        isFilterable: true,
        isSortable: false,
        fieldType: 'DISCOUNT',
        isStandard: true,
        isDefault: false
      }
    ];

    const newProps = {
      ...props,
      headers,
      layout,
      data: {
        content: [{
          id: 1,
          name: 'test Deal',
          recordActions: {
            update: true
          },
          products: [
            { id: 1, name: 'Product 1', price: { currencyId: 431, value: 1000 }, quantity: 1, discount: { type: DiscountType.FIXED, value: 1234 } },
            { id: 2, name: 'Product 2', price: { currencyId: 431, value: 2000 }, quantity: 3, discount: { type: DiscountType.PERCENTAGE, value: 10 } }
          ]
        }],
        totalElements: 20,
        totalPages: 10
      },
      entity: 'deals',
      marketplaceCallActions: [dummyMarketplaceActions[0]]
    };
    const wrapper = shallow(<ListingTable {...newProps} />);
    const reactTableComponent = wrapper.find('ReactTableFixedColumns');
    const tdComponent = reactTableComponent.dive().dive().find('Tbody').find('TdComponent');

    const readOnlyProductsComponent = tdComponent.at(0).find(ReadOnlyProducts);
    expect(reactTableComponent.length).toEqual(1);
    expect(readOnlyProductsComponent.props()).toEqual({
      dealId: 1,
      products: newProps.data.content[0].products,
      fields: {
        products: 'Product or Services',
        discount:  'Discount'
      }
    });
  });

  it('should render view notes link for latest notes column if user has notes permission and open LatestNotesModal when clicked on it', () => {
    const headers = [
      {
        id: 'latestNotes',
        header: 'Latest Notes',
        isFilterable: true,
        isSortable: false,
        fieldType: 'LATEST_NOTES',
        isStandard: true,
        isDefault: true
      }
    ];

    const newProps = {
      ...props,
      headers,
      layout: [...headers],
      data: {
        content: [{
          id: 1,
          name: 'test notes',
          ownerId: 793,
          recordActions: {
            note: true
          }
        }],
        totalElements: 20,
        totalPages: 10
      },
      entity: 'deals',
      marketplaceCallActions: [dummyMarketplaceActions[0]]
    };
    const wrapper = shallow(<ListingTable {...newProps} />);
    const reactTableComponent = wrapper.find('ReactTableFixedColumns');
    const tdComponent = reactTableComponent.dive().dive().find('Tbody').find('TdComponent');

    expect(wrapper.find(LatestNotesModal).length).toEqual(0);
    const latestNotesComponent = tdComponent.at(0).find('a.link-primary');
    expect(reactTableComponent.length).toEqual(1);
    expect(latestNotesComponent.length).toBe(1);

    latestNotesComponent.props().onClick({ stopPropagation: jest.fn() });
    expect(wrapper.find(LatestNotesModal).length).toEqual(1);
  });

  it('should not render view notes link for latest notes column if user does not have notes permission', () => {
    const headers = [
      {
        id: 'latestNotes',
        header: 'Latest Notes',
        isFilterable: true,
        isSortable: false,
        fieldType: 'LATEST_NOTES',
        isStandard: true,
        isDefault: true
      }
    ];

    const newProps = {
      ...props,
      headers,
      layout: [...headers],
      data: {
        content: [{
          id: 1,
          name: 'test notes',
          ownerId: 793,
          recordActions: {
            note: false
          }
        }],
        totalElements: 20,
        totalPages: 10
      },
      entity: 'deals',
      marketplaceCallActions: [dummyMarketplaceActions[0]]
    };
    const wrapper = shallow(<ListingTable {...newProps} />);
    const reactTableComponent = wrapper.find('ReactTableFixedColumns');
    const tdComponent = reactTableComponent.dive().dive().find('Tbody').find('TdComponent');


    expect(tdComponent.at(0).find('span').text()).toBe(' - ');
  });

  it('should route to workflow/entity details page from workflow action logs list on click workflowId/entityId and also open on row click', () => {
    const originalWindow = window;
    window.open = jest.fn();

    const newProps = {
      ...props,
      rowClickRequiredPermission: [],
      entity: entities.WORKFLOW_ACTION_LOGS,
      data: mockGetWorkflowActionLogsListResponse,
      headers: new ListLayout(mockWorkflowActionLogsListLayout).getColumns()
    };

    const wrapper = shallow(<ListingTable {...newProps} />);

    const workflowIdColumnData = wrapper.find('ReactTableFixedColumns').props().columns.find(f => f.id === 'workflowId');
    workflowIdColumnData.accessor(mockGetWorkflowActionLogsListResponse.content[0]).props.onClick({ stopPropagation: jest.fn(), ctrlKey: true });

    expect(window.open).toHaveBeenCalledWith('/setup/workflow-automation/workflows/details/123', '_blank');

    const entityIdColumnData = wrapper.find('ReactTableFixedColumns').props().columns.find(f => f.id === 'entityId');
    entityIdColumnData.accessor(mockGetWorkflowActionLogsListResponse.content[0]).props.onClick({ stopPropagation: jest.fn(), ctrlKey: true });

    expect(window.open).toHaveBeenCalledWith('/sales/leads/details/12345', '_blank');

    wrapper.find('ReactTableFixedColumns').props().getTdProps(null, { original: { ...mockGetWorkflowActionLogsListResponse.content[0] } }, { id: 'workflowName' }, null).onClick({ ctrlKey: true, metaKey: true });

    expect(newProps.onClickRow).toHaveBeenCalledWith(mockGetWorkflowActionLogsListResponse.content[0], { ctrlKey: true, metaKey: true });

    window = originalWindow;
  });

  it('should route to entity details page from campaign activity recipient status list on click entityId and also open on row click', () => {
    const originalWindow = window;
    window.open = jest.fn();

    const newProps = {
      ...props,
      rowClickRequiredPermission: [],
      data: mockGetCampaignActivityRecipientStatusList,
      entity: entities.CAMPAIGN_ACTIVITY_RECIPIENT_STATUS,
      headers: getCampaignActivityRecipientStatusTableHeaders()
    };

    const wrapper = shallow(<ListingTable {...newProps} />);

    const entityIdColumnData = wrapper.find('ReactTableFixedColumns').props().columns.find(f => f.id === 'entityId');
    entityIdColumnData.accessor(mockGetCampaignActivityRecipientStatusList.content[0]).props.onClick({ stopPropagation: jest.fn(), ctrlKey: true });

    expect(window.open).toHaveBeenCalledWith('/sales/contacts/details/1', '_blank');

    window = originalWindow;
  });

  it('should render chip dropdown menu, when entity is campaign-activity-recipient-status and column id is status and call updateSelectedFilter on value change', () => {
    const newProps = {
      ...props,
      rowClickRequiredPermission: [],
      data: mockGetCampaignActivityRecipientStatusList,
      entity: entities.CAMPAIGN_ACTIVITY_RECIPIENT_STATUS,
      selectedFilter: CampaignActivityRecipientStatusType.ALL,
      headers: getCampaignActivityRecipientStatusTableHeaders(),
      updateSelectedFilter: jest.fn()
    };

    const wrapper = mount(
      <Provider store={store}>
        <Router>
          <ListingTable {...newProps} />
        </Router>
      </Provider>
    );

    expect(wrapper.find(ChipDropdown).length).toEqual(1);

    wrapper.find(ChipDropdown).props().onValueChange(CampaignActivityRecipientStatusType.SENT);
    expect(newProps.updateSelectedFilter).toHaveBeenCalledWith(CampaignActivityRecipientStatusType.SENT);
  });

  describe('Pagination Component', () => {
    it('should render pagination component with required props', () => {
      const wrapper = shallow(<ListingTable {...props} />);
      expect(wrapper.find(Pagination).length).toEqual(1);
      expect(wrapper.find(Pagination).props()).toEqual(
        { searchRowLimitExceeds: false,
          currentPage: 1,
          showPageSizeDropdown: false,
          itemList: props.data,
          changePage: updateCurrentPageMock
        });
    });

    it('should render pagination component with required props with page size dropdown', () => {
      const setPageSizeMock = jest.fn();
      const wrapper = shallow(<ListingTable {...props} setPageSize={setPageSizeMock} />);
      expect(wrapper.find(Pagination).length).toEqual(1);
      expect(wrapper.find(Pagination).props()).toEqual(
        { searchRowLimitExceeds: false,
          currentPage: 1,
          showPageSizeDropdown: true,
          itemList: props.data,
          setPageSize: setPageSizeMock,
          changePage: updateCurrentPageMock
        });
    });
  });

  describe('Sorting on table columns', () => {
    it('should show arrow in column header according to sortOrder in props', () => {
      const wrapper = mount(
        <Provider store={store}>
          <Router>
            <ListingTable {...{ ...props, sortByField: 'name', sortOrder: SortOrder.ASC }} />
          </Router>
        </Provider>
      );
      const reactTableComponent = wrapper.find('ReactTableFixedColumns');
      const headers = reactTableComponent.find('ThComponent').find('.align-items-center.cursor-pointer');

      expect(headers.at(0).find('i').props().className).toEqual('ml-2 fas fa-long-arrow-alt-up');
      expect(headers.at(1).find('i').length).toEqual(0);

    });

    it('should call updateSortOrder when clicked on sortable column header', () => {
      const wrapper = mount(
        <Provider store={store}>
          <Router>
            <ListingTable {...{ ...props, sortByField: 'name', sortOrder: SortOrder.ASC, columnOrder: ['id','name','ownedBy'] }} />
          </Router>
        </Provider>
      );
      const reactTableComponent = wrapper.find('ReactTableFixedColumns');
      const headers = reactTableComponent.find('ThComponent').find('.sort-header');

      headers.at(0).simulate('click');

      expect(headers.get(0).props.className.includes('cursor-pointer')).toBe(true);
      expect(props.setPreferredSmartListAction).toHaveBeenCalledWith(1, 'deals', { ...props.smartListRules, sort: 'name,desc' }, ['id','name','ownedBy']);
    });

    it('should NOT allow sorting on non-sortable fields', () => {
      const wrapper = mount(
        <Provider store={store}>
          <Router>
            <ListingTable {...{ ...props, sortByField: 'name', sortOrder: SortOrder.ASC }} />
          </Router>
        </Provider>
      );
      const reactTableComponent = wrapper.find('ReactTableFixedColumns');
      const headers = reactTableComponent.find('ThComponent').find('.sort-header');

      headers.at(1).simulate('click');

      expect(updateSortOrderMock).toHaveBeenCalledTimes(0);
      expect(headers.get(1).props.className.includes('cursor-pointer')).toBe(false);
    });
  });

  describe('More than 10k records are accessed', () => {
    const component = shallow(<ListingTable {...props} error={'003014'} />);
    component.setProps({ error: '003014' });

    it('should render CustomNoDataComponent with error in fetching div', () => {

      const reactTableWrapper = (component.find('ReactTableFixedColumns'));
      const noDataWrapper = shallow(reactTableWrapper.props().NoDataComponent());

      expect(noDataWrapper.find('.error-in-fetching-records').length).toBe(1);
      expect(noDataWrapper.find('.error-in-fetching-records').text()).toBe('Nothing here!You tried to access more than 10,000 records in one go. Add filters to fetch less than 10,000 recordsAdd Filters');

      noDataWrapper.find('.btn').simulate('click');

      expect(props.onClickAddFilters).toHaveBeenCalledWith();
    });

    it('should render CustomNoDataComponent with No rows found for other non-errors', () => {
      component.setProps({ error: '' });
      const reactTableWrapper = (component.find('ReactTableFixedColumns'));
      const noDataWrapper = shallow(reactTableWrapper.props().NoDataComponent());

      expect(noDataWrapper.find('.rt-noData').text()).toBe('No rows found');
    });
  });

  describe('Bulk selection', () => {
    let wrapper;
    const newData = {
      ...props.data,
      content: [{
        isActive: true,
        description: 'This is test',
        id: 1,
        name: '',
        price: {
          currency: {
            id: 400,
            code: 'USD'
          },
          value: 1234
        },
        recordActions: {
          update: true
        }
      }, {
        isActive: true,
        description: 'This is test',
        id: 2,
        name: 'Second',
        price: {
          currency: {
            id: 440,
            code: 'USD'
          },
          value: 134
        },
        recordActions: {
          update: true
        }
      }]
    };
    beforeEach(() => {
      wrapper = mount(<Provider store={store}><Router><ListingTable {...props} showRowSelectionCheckBox={true} data={newData} /></Router></Provider>);
    });

    it('should select a row when check box before it is clicked', () => {
      expect(wrapper.find(`#check_${newData.content[0].id}`).props().checked).toBe(false);

      wrapper.find(`#check_${newData.content[0].id}`).props().onChange();
      wrapper.update();
      expect(wrapper.find(`#check_${newData.content[0].id}`).props().checked).toBe(true);
    });

    it('should select all rows when select all check box is clicked', () => {
      const selectAllCheckbox = wrapper.find('#check_all');

      expect(selectAllCheckbox.props().checked).toBe(false);
      selectAllCheckbox.props().onChange();

      wrapper.update();
      expect(wrapper.find('#check_all').props().checked).toBe(true);

      const rows = wrapper.find('ReactTableFixedColumns').props().data;
      expect(rows.length).toBe(2);

      const entityIds = rows.map(row => row.id);

      expect(wrapper.find(`#check_${entityIds[0]}`).props().checked).toBe(true);
      expect(wrapper.find(`#check_${entityIds[1]}`).props().checked).toBe(true);
    });

    it('should unselect all rows when first a checkbox is clicked then select all check box is clicked twice', () => {
      expect(wrapper.find('#check_1').props().onChange());

      wrapper.update();
      expect(wrapper.find('#check_1').props().checked).toBe(true);
      const selectAllCheckbox = wrapper.find('#check_all');
      selectAllCheckbox.props().onChange();

      wrapper.update();
      expect(wrapper.find('#check_all').props().checked).toBe(true);
      wrapper.find('#check_all').props().onChange();

      wrapper.update();
      expect(wrapper.find('#check_all').props().checked).toBe(false);

      const rows = wrapper.find('ReactTableFixedColumns').props().data;
      expect(rows.length).toBe(2);

      const entityIds = rows.map(row => row.id);

      expect(wrapper.find(`#check_${entityIds[0]}`).props().checked).toBe(false);
      expect(wrapper.find(`#check_${entityIds[1]}`).props().checked).toBe(false);
    });

    it('should unselect the row and select all check box if user clicks on a rows checkbox after all rows are selected', () => {
      const selectAllCheckbox = wrapper.find('#check_all');
      selectAllCheckbox.props().onChange();

      wrapper.update();
      expect(wrapper.find('#check_all').props().checked).toBe(true);
      wrapper.find('#check_1').props().onChange();

      wrapper.update();
      expect(wrapper.find('#check_all').props().checked).toBe(false);

      expect(wrapper.find('#check_1').props().checked).toBe(false);
    });

    it('should mark select all check box as checked if all rows are selected', () => {
      const rows = wrapper.find('ReactTableFixedColumns').props().data;
      expect(rows.length).toBe(2);

      const entityIds = rows.map(row => row.id);

      wrapper.find(`#check_${entityIds[0]}`).props().onChange();
      wrapper.update();
      wrapper.find(`#check_${entityIds[1]}`).props().onChange();
      wrapper.update();

      expect(wrapper.find('#check_all').props().checked).toBe(true);
    });

    it('should disabled checkboxes when all entities accross the pages are selected', () => {
      wrapper = mount(
        <Provider store={store}>
          <Router>
            <ListingTable
              {...props}
              allEntitiesSelected={true}
              showRowSelectionCheckBox={true}
              data={newData}
            />
          </Router>
        </Provider>
      );
      const rows = wrapper.find('ReactTableFixedColumns').props().data;
      expect(rows.length).toBe(2);

      const entityIds = rows.map(row => row.id);

      expect(wrapper.find(`#check_${entityIds[0]}`).props().disabled).toEqual(true);
      expect(wrapper.find(`#check_${entityIds[0]}`).props().checked).toEqual(true);
      expect(wrapper.find(`#check_${entityIds[1]}`).props().disabled).toEqual(true);
      expect(wrapper.find(`#check_${entityIds[1]}`).props().checked).toEqual(true);

      expect(wrapper.find('#check_all').props().checked).toBe(true);
      expect(wrapper.find('#check_all').props().disabled).toBe(true);
    });

    it('should toggle selectAll entities option', () => {
      const toggleSelectAllEntities = jest.fn();
      wrapper = mount(<Provider store={store}>
        <Router>
          <ListingTable
            {...props}
            entity={entities.DEALS}
            allEntitiesSelected={true}
            toggleSelectAllEntities={toggleSelectAllEntities}
            showRowSelectionCheckBox={true}
            data={newData}
          />
        </Router>
      </Provider>);
      wrapper.find(BulkActions).props().toggleSelectAllEntities();
      expect(toggleSelectAllEntities).toHaveBeenCalled();
    });
  });

  describe('Bulk action', () => {
    let wrapper;
    const bulkActionProps = {
      ...props,
      entity: entities.DEALS,
      openEditForm: jest.fn(),
      deleteEntity: jest.fn()
    };

    beforeEach(() => {
      wrapper = shallow(<ListingTable {...bulkActionProps} />);
    });

    it('should redirect to first page when bulk action modal is closed after successfull operation', () => {
      props.updateCurrentPage.mockClear();
      wrapper.find(BulkActions).props().onClose(true);
      expect(props.updateCurrentPage).toHaveBeenCalledWith(1, true);
    });

    it('should set default sort field and sort order in bulk action modal', () => {
      props.updateCurrentPage.mockClear();
      expect(wrapper.find(BulkActions).props().sort).toEqual('updatedAt,desc');
    });

    it('should NOT redirect to first page when bulk action modal is closed if user cancels the operation', () => {
      props.updateCurrentPage.mockClear();
      wrapper.find(BulkActions).props().onClose(false);
      expect(props.updateCurrentPage).not.toHaveBeenCalled();
    });

    it('should send recordActions and ownerId of entity when only one entity is selected', () => {
      wrapper.instance().toggleSelect(1);

      expect(wrapper.find(BulkActions).props().selectedEntityRecordActions).toEqual({
        update: true
      });
      expect(wrapper.find(BulkActions).props().selectedEntityOwnerId).toEqual(214);
    });
  });

  describe('Column Reordering', () => {
    const tableHeaders = [
      {
        id: 'name',
        header: 'Name',
        isStandard: true,
        isFilterable: true,
        isSortable: true,
        isInternal: false,
        multiValue: false,
        fieldType: 'TEXT_FIELD',
        picklist: null,
        lookup: null,
        isDefault: true
      }, {
        id: 'ownedBy',
        header: 'Owner',
        isStandard: true,
        isFilterable: true,
        isSortable: false,
        isInternal: false,
        multiValue: false,
        fieldType: 'LOOK_UP',
        picklist: null,
        lookup: { entity: 'USER', lookupUrl: '/users/lookup?q=firstName:' },
        isDefault: true
      }, {
        id: 'pipeline',
        header: 'Pipeline',
        isStandard: true,
        isFilterable: false,
        isSortable: false,
        isInternal: false,
        multiValue: false,
        fieldType: 'LOOK_UP',
        picklist: null,
        lookup: { entity: null, lookupUrl: '/pipelines/lookup?entityType=DEAL&q=name:' },
        isDefault: true
      }, {
        id: 'estimatedValue',
        header: 'Revenue',
        isStandard: true,
        isFilterable: true,
        isSortable: true,
        isInternal: false,
        multiValue: false,
        fieldType: 'MONEY',
        picklist: null,
        lookup: null,
        isDefault: true
      }, {
        id: 'estimatedClosureOn',
        header: 'Closure Date',
        isStandard: true,
        isFilterable: true,
        isSortable: true,
        isInternal: false,
        multiValue: false,
        fieldType: 'DATE_PICKER',
        picklist: null,
        lookup: null,
        isDefault: true
      }, {
        id: 'product',
        header: 'Product',
        isStandard: true,
        isFilterable: true,
        isSortable: false,
        isInternal: false,
        multiValue: false,
        fieldType: 'LOOK_UP',
        picklist: null,
        lookup: { entity: null, lookupUrl: '/products/lookup?q=name:' },
        isDefault: true
      }, {
        id: 'pipelineStage',
        header: 'Status',
        isStandard: true,
        isFilterable: true,
        isSortable: false,
        isInternal: false,
        multiValue: false,
        fieldType: 'TEXT_FIELD',
        picklist: null,
        lookup: null,
        isDefault: true
      }
    ];
    it('should change the column order when drag and drop columns', () => {
      const newProps = {
        ...props,
        headers: tableHeaders
      };
      const wrapper = shallow(<ListingTable {...newProps} />);

      wrapper.instance().onDragEnd({
        source: { index: 1 },
        destination: { index: 3 }
      });

      expect(props.updateColumnOrder).toHaveBeenCalledWith([
        'name',
        'pipeline',
        'estimatedValue',
        'ownedBy',
        'estimatedClosureOn',
        'product',
        'pipelineStage',
      ],                                                   undefined);
    });

    it('should not called updateColumnOrder, when source and destination is same', () => {
      const wrapper = shallow(<ListingTable {...props} />);
      wrapper.setProps({ headers: tableHeaders });

      wrapper.instance().onDragEnd({
        source: { index: 2 },
        destination: { index: 2 }
      });

      expect(props.updateColumnOrder).not.toHaveBeenCalled();
    });
  });
});

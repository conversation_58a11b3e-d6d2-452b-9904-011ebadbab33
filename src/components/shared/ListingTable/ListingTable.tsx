import * as $ from 'jquery';
import * as React from 'react';
import classnames from 'classnames';
import ReactTable from 'react-table';
import { connect } from 'react-redux';
import * as debounce from 'lodash/debounce';
import { ReactTableDefaults } from 'react-table';
import withFixedColumns from 'react-table-hoc-fixed-columns';
import { DragDropContext, Draggable, Droppable } from 'react-beautiful-dnd';

import 'react-table-hoc-fixed-columns/lib/styles.css';

import * as addFilters from '../../../assets/icons/add-filters.svg';

import { StateInterface } from '../../../store/store';
import { Permission } from '../Permission/UserPermission';
import { BulkJobCategory } from '../../page/BulkJobs/model';
import { PicklistValue } from '../ListAction/PickListModal';
import { FieldTypes } from '../../page/FieldSettings/models/Field';
import { WorkflowActionStatus } from '../../page/WorkflowActionLogs/model';
import { CampaignActivityRecipientStatusType } from '../../page/Campaign/model';
import { Column, PaginatedList, SortOrder, toggleSort } from './models/ListingTable';
import { AppAction } from '../../products/marketplace/ManageApps/Models/MarketplaceApp';

import { isBlank } from '../../../utils/globalUtil';
import { entities } from '../../../utils/constants';
import { getEntityRouteUrl } from '../Notification/utils';
import { bulkActionEntities } from '../BulkActions/constants';
import { NOTE, isActionAllowed } from '../../../utils/permissionUtil';
import { getWorkflowRouteUrlBasedOnPermission } from '../../page/Workflows/utils';
import { getDataFromLocalStorage, setDataInLocalStorage } from '../../../utils/util';
import { entitiesWithGPSCoordinateField, gpsCoordinateRelationField } from '../../page/Layouts/constants';
import { capitalizeLabel, getEntityLabel, isSmartListApplicable, routeToEntity } from '../../../utils/entityUtils';

import { setPreferredSmartListAction } from '../../shared/ListAction/actions/smartListActions';
import { resetEntityDetails } from '../EntityDetailsTooltip/EntityDetailsAction/EntityDetailsAction';
import { removeRecordDetailsUrlAction, removeSelectedRecordAction } from '../ListAction/actions/selectedRecordActions';

import { TableDataFormatter, getColumnsWithFormattedValue } from './TableDataFormatter';
import { RedirectionForScheduledJobs } from '../RedirectionForScheduledJobs/RedirectionForScheduledJobs';
import Pagination from '../Table/Pagination';
import BulkActions from '../BulkActions/BulkActions';
import ChipDropdown from '../ChipDropdown/ChipDropdown';
import MultiActionModal from '../Input/MultiActionModal';
import ListRowsOptions from './ListRowsOptions/ListRowsOptions';
import GoalValuesTable from '../GoalValuesTable/GoalValuesTable';
import ReadOnlyProducts from '../ReadOnlyProducts/ReadOnlyProducts';
import ViewProductImages from '../ViewProductImages/ViewProductImages';
import ReadOnlyMultiValue from '../ReadOnlyMultiValue/ReadOnlyMultiValue';
import LatestNotesModal from '../Table/LatestNotesModal/LatestNotesModal';
import GPSAddressFieldValue from '../GPSAddressFieldContainer/GPSAddressFieldValue/GPSAddressFieldValue';
import ReadOnlyMultiValueLookup from '../ReadOnlyMultiValue/ReadOnlyMultiValueLookup/ReadOnlyMultiValueLookup';
import EntityInfoTooltipOnListingPages from '../EntityInfoTooltipOnListingPages/EntityInfoTooltipOnListingPages';

import WhatsAppMessageModal from '../Table/WhatsAppMessageModal/WhatsAppMessageModal';

import './ListingTable.scss';

type TextStyle = 'wrap-text' | 'clip-text';

const API_KEY_COLUMN_ID = 'apiKey';
const SearchRowLimitExceeds = '003014';

const ReactTableWithFixedColumns = withFixedColumns(ReactTable);

interface Header {
  id: string;
  header: string;
  isFilterable: boolean;
  isSortable: boolean;
  fieldType: string;
  isStandard: boolean;
  isDefault?: boolean;
  picklist?: { picklistValues: PicklistValue[]; };
}

interface Props {
  entity?: string;
  data?: PaginatedList;
  history: any;
  pageSize?: number;
  currentPage: number;
  updateSortOrder?: (sortByField: string, SortOrder: SortOrder) => void;
  updateCurrentPage?: (currentPage: number, unselectAllEntities?: boolean) => void;
  setPageSize?: (size: number) => void;
  headers?: Header[];
  sortByField?: string;
  sortOrder?: SortOrder;
  RowOptions?: any;
  layout?: Column[];
  smartListRules?: any;
  onClickRow?: ({ }: any, e?: any) => void;
  rowClickRequiredPermission: string[];
  apiKeyOptions?: any;
  allEntitiesSelected?: boolean;
  toggleSelectAllEntities?: () => void;
  showRowSelectionCheckBox?: boolean;
  marketplaceCallActions?: AppAction[];
  openEditForm?: (entityId: number) => void;
  deleteEntity?: (entityId: number) => void;
  setPreferredSmartListAction?: (smartListId: number, entity: string, data?: any, currentColumns?: string[]) => void;
  removeRecordDetailsUrlAction: () => void;
  removeSelectedRecordAction: () => void;
  shouldSaveSort?: boolean;
  updateColumnOrder?: (fields: string[], freezedColumn) => void;
  isColumnReorderingDisabled?: boolean;
  onClickAddFilters?: () => void;
  error?: number | string;
  columnOrder?: string[];
  currentUserId?: number;
  noDataComponent?: React.ReactNode;
  selectedRecord?: any;
  selectedRecordDetailsUrl?: any;
  provisionalSmartlist?: any;
  resetEntityDetails?: () => void;
  subEntity?: string;
  dateFormat?: any;
  timezone?: any;
  tenantCurrency?: any;
  currenciesPickList?: any;
  entityLabelMap?: any;
  allTableColumns?: Header[];
  standardPicklist?: any;
  showEventDetails?: (event, id) => void;
  profilePermissions: Permission[];
  showSendWhatsAppMessageModalFor?: number;
  setShowSendWhatsAppMessageModalFor?: (entityId: number) => void;
  selectedFilter?: CampaignActivityRecipientStatusType;
  updateSelectedFilter?: (val: CampaignActivityRecipientStatusType) => void;
}

export class ListingTable extends React.Component<Props, any> {

  constructor(props) {
    super(props);
    this.state = {
      selectAll: false,
      selectedEntities: [],
      columns: [],
      latestNoteParams: {},
      idWiseEntityDetails: {},
      idWiseEntityDetailsLoading: {},
      isOpenInNewTabAction: false
    };
  }

  componentDidMount() {
    const { currentUserId, entity, headers, provisionalSmartlist, subEntity, allTableColumns } = this.props;

    $('[data-toggle="tooltip"]').tooltip({ boundary: 'window' });

    const localSmartlistColumnOrder = getDataFromLocalStorage(`${currentUserId}.${entity}${subEntity ? `.${subEntity}` : ''}.columnOrder`, 'provisionalSmartlist');
    let columns;
    if(localSmartlistColumnOrder && (provisionalSmartlist || !isSmartListApplicable(routeToEntity(entity)))){

      const remainingColumns = allTableColumns.filter(col => !localSmartlistColumnOrder.some(c => (typeof(c) === 'string' ? c : c.id) === col.id));
      const unselectedRemainingColumns = remainingColumns.map(column => ({ ...column, isDefault: false }));

      columns = [...this.getColumnsByLocalColumnOrder(allTableColumns), ...unselectedRemainingColumns];

    }else{
      columns = headers;
    }

    this.setState({ columns });
    this.scrollHighlightedRowIntoView();
  }

  getCursorPointerType = (isEnabled) => {
    if (isEnabled) return classnames('cursor-pointer');
    return '';
  }

  setColumnTextStyle = (columnName: string, textStyle: TextStyle) => {
    const { entity, currentUserId, subEntity } = this.props;

    setDataInLocalStorage(`${currentUserId}.${entity}.${subEntity ? `${subEntity}.` : ''}${columnName}.textStyle`, 'columnProperties', textStyle);
  }


  getColumnTextStyle = (columnName: string) => {
    const { entity, currentUserId, subEntity } = this.props;
    const textStyle = getDataFromLocalStorage(`${currentUserId}.${entity}.${subEntity ? `${subEntity}.` : ''}${columnName}.textStyle`, 'columnProperties');

    return (textStyle || 'clip-text');
  }

  toggleSort(sortBy: string) {
    const { sortByField, sortOrder, smartListRules, entity, shouldSaveSort, columnOrder } = this.props;
    const newSortOrder = (sortByField === sortBy) ?
      toggleSort[sortOrder]
      : 'asc' as SortOrder;

    ([entities.DEALS, entities.COMPANIES].includes(entity) && shouldSaveSort) ? this.props.setPreferredSmartListAction(smartListRules[entity].id, entity, { ...smartListRules, sort: `${sortBy},${newSortOrder}`, freezedColumn: smartListRules[entity]?.freezedColumn }, columnOrder || [])
    : this.props.updateSortOrder(sortBy, newSortOrder);
  }

  getSortableHeaders = (column: any) => {
    const { entity, headers, selectedFilter, updateSelectedFilter } = this.props;

    return (
      <div
        onClick={() => (column.isSortable && this.toggleSort(column.id))}
        className={classnames('w-90 d-flex align-items-center sort-header text-break', { 'cursor-pointer': column.isSortable })}
      >
        <span className="thead-text">{column.header}</span>

        {
          (column.isSortable && column.id === this.props.sortByField) &&
          <i className={`ml-2 fas fa-long-arrow-alt-${this.props.sortOrder === 'desc' ? 'down' : 'up'}`} />
        }

        {(entity === entities.CAMPAIGN_ACTIVITY_RECIPIENT_STATUS) && (column.id === 'status') &&
          <div className="ml-2">
            <ChipDropdown
              value={selectedFilter ?? null}
              options={headers?.find(f => f.id === 'status')?.picklist?.picklistValues}
              onValueChange={(val: CampaignActivityRecipientStatusType) => updateSelectedFilter(val)}
            />
          </div>
        }
      </div>
    );
  }

  freezeColumn = (columnName) => {
    const { smartListRules, entity, columnOrder, currentUserId, provisionalSmartlist, subEntity } = this.props;
    if(isBlank(provisionalSmartlist) && [entities.DEALS, entities.COMPANIES].includes(entity)){
      this.props.setPreferredSmartListAction(smartListRules[entity].id, entity, { freezedColumn: columnName }, columnOrder);
    }else{
      setDataInLocalStorage(`${currentUserId}.${entity}${subEntity ? `.${subEntity}` : ''}`, 'freezedColumn', columnName);
      this.setState({});
    }
  }

  unFreezeColumn = () => {
    const { smartListRules, entity, columnOrder, currentUserId, provisionalSmartlist, subEntity } = this.props;
    if(isBlank(provisionalSmartlist) && [entities.DEALS, entities.COMPANIES].includes(entity)){
      this.props.setPreferredSmartListAction(smartListRules[entity].id, entity, { freezedColumn: null }, columnOrder);
    }else{
      setDataInLocalStorage(`${currentUserId}.${entity}${subEntity ? `.${subEntity}` : ''}`, 'freezedColumn', '');
      this.setState({});
    }
  }


  shouldShowFreezeOption = (id) =>{
    const { entity, smartListRules, currentUserId, provisionalSmartlist, subEntity } = this.props;

    if([entities.DEALS, entities.COMPANIES].includes(entity) && isBlank(provisionalSmartlist)){
      return smartListRules[entity] && id !== smartListRules[entity]?.freezedColumn;
    }

    const freezedColumn = getDataFromLocalStorage(`${currentUserId}.${entity}${subEntity ? `.${subEntity}` : ''}`, 'freezedColumn');

    return id !== freezedColumn;
  }

  shouldShowUnfreezeOption = () =>{
    const { entity, smartListRules, currentUserId, provisionalSmartlist, subEntity } = this.props;

    if([entities.DEALS, entities.COMPANIES].includes(entity) && isBlank(provisionalSmartlist)){
      return smartListRules[entity] && !isBlank(smartListRules[entity]?.freezedColumn);
    }

    const freezedColumn = getDataFromLocalStorage(`${currentUserId}.${entity}${subEntity ? `.${subEntity}` : ''}`, 'freezedColumn');

    return !isBlank(freezedColumn);
  }

  scrollHighlightedRowIntoView = () => {
    const highlightedRow = document.getElementsByClassName('highlighted-row')[0];
    if (highlightedRow) {
      highlightedRow.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      });
    }
  }

  getHeaderWithDropDown = (column: any, index: number)=> {
    const { isColumnReorderingDisabled = false, entity, smartListRules } = this.props;
    const textStyleOptions = [
      {
        label: 'Wrap Text',
        action: () => this.setColumnTextStyle(column.id, 'wrap-text')
      },
      {
        label: 'Clip Text',
        action: () => this.setColumnTextStyle(column.id, 'clip-text')
      }
    ];

    if(this.shouldShowFreezeOption(column.id)){
      textStyleOptions.push({
        label: 'Freeze Column',
        action: () => this.freezeColumn(column.id)
      });
    }

    if(this.shouldShowUnfreezeOption()){
      textStyleOptions.push({
        label: 'Unfreeze All Columns',
        action: () => this.unFreezeColumn()
      });
    }

    return(
      <Draggable draggableId={`${column.id}`} index={index}>
        {(p, snapshot) => {
          const style = {
            ...p.draggableProps.style,
            backgroundColor: snapshot.isDragging ? '#EEF3F5' : 'inherit'
          } as const;
          return (
          <div ref={p.innerRef} {...p.draggableProps} style={style} className={`d-flex flex-column ${isColumnReorderingDisabled ? 'draggable-header-disabled': 'draggable-header'}`}>
              <div className="align-self-center drag-handle-container">
                <i className="fas fa-grip-horizontal" {...p.dragHandleProps} />
              </div>
              <div className="d-flex align-items-center justify-content-between text-format-dd">
                {this.getSortableHeaders(column)}
                <MultiActionModal icon={<i className="fas fa-caret-down" />}
                           options={textStyleOptions}/>
              </div>
            </div>
          );
        }}
      </Draggable>
    );
  }

  showLatestNotes = (entityType: string, entityId: number, entityName: string, ownerId: number) => {
    this.setState({ latestNoteParams: { entityType, entityId, entityName, ownerId } });
  }

  getDataColumnAccessor = (row: any, column: any) => {
    const { entity, marketplaceCallActions, layout, profilePermissions, history, entityLabelMap } = this.props;

    if (column.id === 'products' && entity === entities.DEALS) {
      const fields = {};

      layout.forEach((field) => {
        fields[field.id] = field.header;
      });

      return <ReadOnlyProducts
              dealId={row.id}
              fields={fields}
              products={row[column.id]}
             />;
    }
    if(entity === entities.PRODUCT_SERVICES && column.id === 'productImages') {
      return <ViewProductImages productImages={row[column.id]} viewProductImagesOnEditPage={false}/>;
    }

    if (entity === entities.SCHEDULED_JOBS && ['entityId', 'workflow'].includes(column.id)) {
      return <RedirectionForScheduledJobs
              row={row}
              column={column}
              history={this.props.history}
              metaData={this.props.data?.metaData}
              profilePermissions={profilePermissions}
             />;
    }

    if(entity === entities.GOALS && column.id === 'value') {
      return <GoalValuesTable goalDetails={row}/>;
    }

    if(['company', 'associatedCompany', 'associatedDeal'].includes(column.id)) {
      return <EntityInfoTooltipOnListingPages
        entityType={column.id === 'associatedDeal' ? entities.DEALS : entities.COMPANIES}
        value={row[column.id]?.name}
        valueToAddInPosX={8}
        entityId={row[column.id]?.id}
        valueToSubtractFromPosY={88}
        textWrapOrClip={this.getColumnTextStyle(column.id)}
      />;
    }

    if ([FieldTypes.PHONE, FieldTypes.EMAIL].includes(column.fieldType)) {
      const phoneFieldProps = column.fieldType === 'PHONE' ? { entity, entityId: row.id, entityName: row.name, marketplaceActions: marketplaceCallActions } : {};

      return <ReadOnlyMultiValue
        {...phoneFieldProps}
        itemType={column.fieldType}
        values={row[column.id]}
        textWrapOrClip={this.getColumnTextStyle(column.id)}
        fieldName={column.id}
      />;
    }

    if (column.multiValue) {
      return <ReadOnlyMultiValueLookup
              entity={entity}
              fieldName={column.id}
              values={(!column.isStandard && row.customFieldValues) ? row.customFieldValues[column.id] :row[column.id]}
              textWrapOrClip={this.getColumnTextStyle(column.id)}
            />;
    }

    if (['events', 'profiles'].includes(column.id)) {
      return column.formattedValue && column.formattedValue(row);
    }

    if((entity === entities.SHIFTS) && (column.id === 'assignedUsers')) {
      return (
        <a
          className="assigned-users link-primary"
          onClick={(e) => {
            e.stopPropagation();

            const shouldOpenInNewTab = e && (e.ctrlKey || e.metaKey);

            if(shouldOpenInNewTab){
              window.open(`/setup/users/list?shift=${row.id}`, '_blank');
              return;
            }

            history.push(`/setup/users/list?shift=${row.id}`);
          }}
        >
          {row[column.id]}
        </a>
      );
    }

    if(column.fieldType === FieldTypes.LATEST_NOTES) {
      return (
        <>
          {
            isActionAllowed([NOTE], row.recordActions)
              ?
              <a className="link-primary cursor-pointer" onClick={(e) => { this.showLatestNotes(this.props.entity, row.id, row.name, row.ownerId); e.stopPropagation(); }}> View notes </a>
              : <span> - </span>
          }
        </>
      );
    }

    if(entity === entities.WORKFLOW_ACTION_LOGS) {
      if(column.id === 'workflowId') {
        return (
          <a
            className="link-primary cursor-pointer"
            onClick={(e) => {
              e.stopPropagation();

              const shouldOpenInNewTab = e && (e.ctrlKey || e.metaKey);
              const url = getWorkflowRouteUrlBasedOnPermission(profilePermissions, row[column.id]);

              if(shouldOpenInNewTab){
                window.open(url, '_blank');
                return;
              }

              history.push(url);
            }}
          >
            {row[column.id]}
          </a>
        );
      }

      if((column.id === 'entityId') && (row.entityType !== 'EMAIL')) {
        return (
          <a
            className="link-primary cursor-pointer"
            onClick={(e) => {
              e.stopPropagation();

              const shouldOpenInNewTab = e && (e.ctrlKey || e.metaKey);
              const url = getEntityRouteUrl('workflow-action-log', profilePermissions, row.entityType, row[column.id]);

              if(shouldOpenInNewTab){
                window.open(url, '_blank');
                return;
              }

              history.push(url);
            }}
          >
            {row[column.id]}
          </a>
        );
      }

      if(column.id === 'status') {
        return (
          <span style={{ color: (row[column.id] === WorkflowActionStatus.SUCCESS) ? '#28A645' : (row[column.id] === WorkflowActionStatus.FAILED) ? '#EE0000' : '#2e384d' }}>
            {!isBlank(row[column.id]) ? capitalizeLabel(row[column.id]) : '-'}
          </span>
        );
      }

      if(column.id === 'workflowAction') {
        return <span>{column.picklist.picklistValues.find(f => f.id === row[column.id]).displayName}</span>;
      }
    }

    if(entitiesWithGPSCoordinateField.includes(entity) && Object.keys(gpsCoordinateRelationField).includes(column.id)) {
      if(!isBlank(row[column.id]) && !isBlank(row[gpsCoordinateRelationField[column.id]])) {
        return(
          <GPSAddressFieldValue
            showTooltipOnHover={true}
            tooltipContent={row[column.id]}
            textWrapOrClip={this.getColumnTextStyle(column.id)}
            gpsCoordinate={row[gpsCoordinateRelationField[column.id]]}
          >
            {row[column.id]}
          </GPSAddressFieldValue>
        );
      }
    }

    if((entity === entities.IMPORTS) && (column.id === 'entity')){
      return row[column.id] === 'PRODUCT' ? 'Product or Service' : getEntityLabel(entityLabelMap, row[column.id]);
    }

    if((entity === entities.CAMPAIGN_ACTIVITY_RECIPIENT_STATUS) && (column.id === 'entityId')) {
      return (
        <a
          className="link-primary cursor-pointer"
          onClick={(e) => {
            e.stopPropagation();

            const shouldOpenInNewTab = e && (e.ctrlKey || e.metaKey);
            const url = getEntityRouteUrl(entity, profilePermissions, row.entityType, row[column.id]);

            if(shouldOpenInNewTab){
              window.open(url, '_blank');
              return;
            }

            history.push(url);
          }}
        >
          {row[column.id]}
        </a>
      );
    }

    return (
      <div className={this.getColumnTextStyle(column.id)}>
        {
          column.formattedValue
            ? column.formattedValue(row)
            : <TableDataFormatter type={column.fieldType} row={row} column={column} metaData={this.props.data?.metaData} />
        }
      </div>
    );
  }

  componentWillUnmount(): void {
    this.props.removeRecordDetailsUrlAction();
    this.props.resetEntityDetails();
  }

  getColumnWidths = (column) => {
    const { entity, currentUserId, provisionalSmartlist, subEntity } = this.props;
    const storageLocation = !isBlank(provisionalSmartlist) ? `${currentUserId}.provisionalSmartlist.${entity}.${subEntity ? `${subEntity}.` : ''}${column.id}.width` : `${currentUserId}.${entity}.${subEntity ? `${subEntity}.` : ''}${column.id}.width`;
    const columnWidth = getDataFromLocalStorage(storageLocation, 'columnProperties');

    if(columnWidth){
      return columnWidth < 120 ? 120 : columnWidth;
    }

    if(entity === entities.CAMPAIGN_ACTIVITY_RECIPIENT_STATUS && column.id === 'status') {
      return 150;
    }

    return ((entity === entities.IP_CONFIGURATIONS && column.id === 'value')) ? 230 : 120;
  }

  shouldDisableFreezing = (columnId, freezedColumn) =>{
    const { provisionalSmartlist, entity, smartListRules } = this.props;
    const entityWithSmartlist = [entities.DEALS, entities.COMPANIES].includes(entity);

    if(entityWithSmartlist && isBlank(provisionalSmartlist) && columnId === smartListRules[entity]?.freezedColumn){
      return false;
    }

    if(!entityWithSmartlist && columnId === freezedColumn){
      return false;
    }

    if(!isBlank(provisionalSmartlist) && columnId === freezedColumn){
      return false;
    }

    return true;
  }

  getDataColumns = (dataSet) => {
    const { smartListRules, entity, currentUserId, provisionalSmartlist, subEntity } = this.props;
    const items = [];
    const minWidth = 95;
    const freezedColumn = getDataFromLocalStorage(`${currentUserId}.${entity}${subEntity ? `.${subEntity}` : ''}`, 'freezedColumn');
    let areColumnsFreezed = (isBlank(provisionalSmartlist) && [entities.DEALS, entities.COMPANIES].includes(entity)) ? !isBlank(smartListRules[entity]?.freezedColumn) : !isBlank(freezedColumn);

    dataSet.forEach((column, index) => {
      column.id !== API_KEY_COLUMN_ID ?
        items.push(
          {
            id: column.id,
            width: column.width,
            fixed: areColumnsFreezed ? 'left' : '',
            minWidth: this.getColumnWidths(column),
            Header: this.getHeaderWithDropDown(column, index),
            style: {
              minWidth,
              overflow: 'visible'
            },
            headerStyle: {
              minWidth
            },
            accessor: row => this.getDataColumnAccessor(row, column)
          })
        :
        items.push(
          {
            id: column.id,
            minWidth: 200,
            fixed: areColumnsFreezed ? 'left' : '',
            Header: this.getHeaderWithDropDown(column, index),
            style: {
              overflow: 'visible'
            },
            accessor: row => this.getDataColumnAccessor(row, column),
            Cell: (row) => {
              return this.props.apiKeyOptions(row);
            }
          });
      if(areColumnsFreezed){
        areColumnsFreezed = this.shouldDisableFreezing(column.id, freezedColumn);
      }
    });
    return items;
  }

  toggleSelectAll = () => {
    const { data } = this.props;
    const { selectAll } = this.state;

    let updatedSelectedEntities = [];

    if (!selectAll) {
      updatedSelectedEntities = data.content.map(item => item.id);
    }
    this.setState({ selectAll: !this.state.selectAll, selectedEntities: updatedSelectedEntities });
  }

  toggleSelect = (id: number) => {
    const { data } = this.props;
    const { selectedEntities } = this.state;

    if (selectedEntities.includes(id)) {
      const updatedSelectedEntities = selectedEntities.filter(item => item !== id);

      this.setState({ selectedEntities: updatedSelectedEntities, selectAll: false });
    } else {
      const updatedSelectedEntities = [...selectedEntities, id];
      this.setState({ selectedEntities: updatedSelectedEntities, selectAll: data.content.length === updatedSelectedEntities.length });
    }
  }

  handleSelectChange = (id: number) => {
    if (!id) {
      this.toggleSelectAll();
    } else {
      this.toggleSelect(id);
    }
  }

  getHeader = () => {
    const { allEntitiesSelected = false } = this.props;
    return (
      <div className="custom-control custom-checkbox">
        <input className="custom-control-input" id={'check_all'} type="checkbox" checked={this.state.selectAll || allEntitiesSelected}
          onChange={() => this.handleSelectChange(0)} disabled={allEntitiesSelected} />
        <label className="custom-control-label" htmlFor={'check_all'} />
      </div>
    );
  }

  getColumnsByLocalColumnOrder = (columns) => {
    const { currentUserId, entity, subEntity } = this.props;

    const localSmartlistColumnOrder = getDataFromLocalStorage(`${currentUserId}.${entity}${subEntity ? `.${subEntity}` : ''}.columnOrder`, 'provisionalSmartlist');

    if(isBlank(localSmartlistColumnOrder)) {
      return;
    }

    const columnsAccordingToOrder = localSmartlistColumnOrder.map(column => columns.find(col => typeof(column) === 'string' ? col.id === column : col.id === column.id));
    return [...columnsAccordingToOrder];
  }

  getAllColumns = () => {
    const { RowOptions, showRowSelectionCheckBox = false, allEntitiesSelected = false, provisionalSmartlist, entity, subEntity, dateFormat, timezone, tenantCurrency, currenciesPickList, entityLabelMap, showEventDetails, allTableColumns } = this.props;
    const { columns } = this.state;

    const localSmartlistColumnsInOrder = this.getColumnsByLocalColumnOrder(allTableColumns);
    const columnList = ((provisionalSmartlist || !isSmartListApplicable(routeToEntity(entity))) && !isBlank(localSmartlistColumnsInOrder))
                        ? this.getDataColumns(getColumnsWithFormattedValue(localSmartlistColumnsInOrder, entity, dateFormat, timezone, tenantCurrency, currenciesPickList, entityLabelMap, showEventDetails, subEntity))
                        : this.getDataColumns(columns);

    if (showRowSelectionCheckBox) {
      columnList.unshift({
        id: 'selectAll',
        fixed: 'left',
        Header: () => this.getHeader(),
        width: 50,
        resizable: false,
        isDefault: false,
        show: true,
        className: 'checkbox-header',
        Cell: ({ row }) => (
          <div className="custom-control custom-checkbox">
            <input className="custom-control-input" id={`check_${row._original.id}`}
              checked={allEntitiesSelected || this.state.selectedEntities.includes(row._original.id)} type="checkbox"
              disabled={allEntitiesSelected}
              onChange={() => this.handleSelectChange(row._original.id)} />
            <label className="custom-control-label" htmlFor={`check_${row._original.id}`} />
          </div>
        )
      });
    }

    return !RowOptions ? columnList :
      [
        ...columnList,
        {
          id: 'option',
          Header: '',
          width: 40,
          fixed: 'right',
          resizable: false,
          className: 'overflow-visible',
          accessor: p => (entity === entities.BULK_JOBS) && (p?.category === BulkJobCategory.CAMPAIGN_ACTION) ? null : (
            <ListRowsOptions
              Options={RowOptions ? <RowOptions {...p} /> : <></>}
              actions={p.recordActions} />
          )
        },
      ];
  }

  onBulkActionModalClose = (shouldRefreshList: boolean) => {
    const { updateCurrentPage } = this.props;

    if (shouldRefreshList) {
      this.setState({ selectAll: false, selectedEntities: [] });
      updateCurrentPage(1, true);
    }
  }

  toggleSelectAllEntities = () => {
    this.setState({ selectedEntities: [], selectAll: false });
    this.props.toggleSelectAllEntities();
  }

  onDragEnd = (result) => {
    const { provisionalSmartlist, currentUserId, entity, smartListRules, subEntity } = this.props;
    const entityWithSmartlist = [entities.DEALS, entities.COMPANIES].includes(entity);

    if (!result.destination) {
      return;
    }

    if (result.source.index === result.destination.index) {
      return;
    }

    const columns = this.reorder(
      this.state.columns,
      result.source.index,
      result.destination.index
    );

    this.setState({
      columns
    });

    if(provisionalSmartlist || !isSmartListApplicable(routeToEntity(entity))){
      // @ts-ignore
      setDataInLocalStorage(`${currentUserId}.${entity}${subEntity ? `.${subEntity}` : ''}.columnOrder`, 'provisionalSmartlist', columns.map(c => c.id));
    }else{
      // @ts-ignore
      this.props.updateColumnOrder(columns.map(col => col.id), smartListRules[entity]?.freezedColumn);
    }
  }

  reorder = (list, startIndex, endIndex) => {
    const result = Array.from(list);
    const [removed] = result.splice(startIndex, 1);
    result.splice(endIndex, 0, removed);

    return result;
  }

  shouldHighlightRow = (id) =>{
    const { entity } = this.props;
    const { isOpenInNewTabAction } = this.state;

    if(isOpenInNewTabAction){
      return this.props.selectedRecord === id;
    }

    let listingEntity = entity;

    if (entity.startsWith('scoring-rules')) {
      listingEntity = 'scoring-rules';
    } else if (entity === entities.WHATSAPP_TEMPLATES) {
      listingEntity = entities.WHATSAPP_BUSINESS;
    }

    listingEntity = listingEntity || 'lead-capture-forms';

    return (
      this.props.selectedRecord === id &&
      this.props.selectedRecordDetailsUrl.includes(listingEntity)
    );

  }


  openSendWhatsAppMessageModal = (entityId: number) =>{
    this.props.setShowSendWhatsAppMessageModalFor(entityId);
  }

  saveColumnWidth = (newResized) => {
    const { entity, currentUserId, provisionalSmartlist, subEntity } = this.props;

    newResized.forEach((val) => {
      const storageLocation = !isBlank(provisionalSmartlist) ? `${currentUserId}.provisionalSmartlist.${entity}.${subEntity ? `${subEntity}.` : ''}${val.id}.width` : `${currentUserId}.${entity}.${subEntity ? `${subEntity}.` : ''}${val.id}.width`;
      setDataInLocalStorage(storageLocation, 'columnProperties', val.value);
    });
  }

  debounceFunction = debounce(this.saveColumnWidth, 500);

  render(){
    const { selectedEntities, latestNoteParams } = this.state;
    const { entity, currentPage, data, updateCurrentPage, onClickRow, rowClickRequiredPermission, noDataComponent, entityLabelMap, timezone, dateFormat,
      setPageSize, sortByField, sortOrder, pageSize, allEntitiesSelected = false, openEditForm, deleteEntity, onClickAddFilters, standardPicklist, history } = this.props;
    const CustomTheadComponent = props => (
      <DragDropContext onDragEnd={this.onDragEnd}>
        <Droppable droppableId="droppable" direction="horizontal">
          {(provided, snapshot) => (
            <div {...props} {...provided.droppableProps} ref={provided.innerRef} className={`rt-thead -header ${snapshot.isDraggingOver ? 'table-header-dragging' : ''}`}>
              {props.children}
            </div>
          )}
        </Droppable>
      </DragDropContext>
    );
    const CustomNoDataComponent = props => (
    (this.props.error === SearchRowLimitExceeds) ?
      <div {...props} className="rt-noData error-in-fetching-records">
          <div className="error-add-filters">
            <img src={`${addFilters}`} />
          </div>
          <div className="error-content">
            <span className="bold-message">Nothing here!</span>
          </div>
          <div>
            <span className="error-message">You tried to access more than 10,000 records in one go. Add filters to fetch less than 10,000 records</span>
          </div>
            <button className="btn btn-outline-primary mt-1" onClick={onClickAddFilters}>Add Filters</button>
      </div>
      :
      noDataComponent ? <div className="rt-noData">{noDataComponent}</div> :
      <div className="rt-noData">No rows found</div>
    );

    const columnsList = this.getAllColumns();

    return(
      <>
        {bulkActionEntities.includes(entity) && <BulkActions
          entity={entity}
          sort={`${sortByField || 'updatedAt'},${sortOrder || SortOrder.DESC}`}
          pageSize={pageSize}
          allEntitiesSelected={allEntitiesSelected}
          toggleSelectAllEntities={this.toggleSelectAllEntities}
          totalEntities={data?.totalElements}
          currentPageNumber={currentPage}
          selectedEntityIds={selectedEntities}
          onClose={this.onBulkActionModalClose}
          sendSingleWhatsAppMessage={this.openSendWhatsAppMessageModal}
          selectedEntityOwnerId={selectedEntities.length === 1 ? data?.content.find(item => item.id === selectedEntities[0])?.ownerId : null}
          selectedEntityRecordActions={selectedEntities.length === 1 ? data?.content.find(item => item.id === selectedEntities[0])?.recordActions : null}
          updateSingleEntity={openEditForm}
          deleteSingleEntity={deleteEntity}
        />}
        <ReactTableWithFixedColumns
          getTableProps={() => ({ className: `listing-table ${bulkActionEntities.includes(entity) ? 'checkbox-header' : ''}` })}
          getTdProps={(state, rowInfo, column, instance) => {
            const isAllowedToPerformAction = isActionAllowed(rowClickRequiredPermission, rowInfo.original.recordActions);
            return {
              className: `${this.getCursorPointerType(isAllowedToPerformAction)} ${this.shouldHighlightRow(rowInfo.original.id) ? 'highlighted-row' : '' }`,
              onClick: (e) => {
                const isOpenInNewTabAction = e && (e.ctrlKey || e.metaKey);

                if (!['selectAll', 'option'].includes(column.id) && isAllowedToPerformAction) {
                  onClickRow && onClickRow(rowInfo.original, e);
                  this.setState({ isOpenInNewTabAction });
                }
              }
            };
          }}
          defaultPageSize={10}
          showPagination={false}
          minRows={0}
          loading={false}
          columns={columnsList}
          // @ts-ignore
          column={{ ...ReactTableDefaults.column, minResizeWidth: 120 }}
          manual
          data={this.props.error === SearchRowLimitExceeds ? undefined : data.content}
          TheadComponent={CustomTheadComponent}
          NoDataComponent={CustomNoDataComponent}
          onResizedChange={(newResized, e) => this.debounceFunction(newResized)}
        />
        <Pagination
          key={data?.totalPages}
          showPageSizeDropdown={!!setPageSize}
          setPageSize={setPageSize}
          currentPage={currentPage}
          itemList={data}
          changePage={updateCurrentPage}
          searchRowLimitExceeds={this.props.error === SearchRowLimitExceeds} />
          {
            !isBlank(latestNoteParams) &&
            <LatestNotesModal
              entityId={latestNoteParams.entityId}
              entityType={latestNoteParams.entityType}
              entityLabel={getEntityLabel(entityLabelMap, entity)}
              standardPicklist={standardPicklist}
              timezone={timezone}
              dateFormat={dateFormat}
              history={history}
              onClose={() => this.setState({ latestNoteParams: {} })}
              entityName={latestNoteParams.entityName}
              ownerId={latestNoteParams.ownerId}
            />
          }
          {
            !isBlank(this.props.showSendWhatsAppMessageModalFor) &&
            <WhatsAppMessageModal
              entityType={entity}
              history={history}
              parentEntity="deal"
              onClose={() => this.props.setShowSendWhatsAppMessageModalFor(null)}
              entityId={this.props.showSendWhatsAppMessageModalFor}
            />
          }
      </>
    );
  }
}

const mapStateToProps = (state: StateInterface) => ({
  smartListRules: state.smartListRules,
  selectedRecord: state.selectedRecord,
  selectedRecordDetailsUrl: state.selectedRecordDetailsUrl,
  currentUserId: state.loginForm.currentUserId,
  dateFormat: state.loginForm.userPreferences.dateFormat,
  timezone: state.loginForm.userPreferences.timezone,
  tenantCurrency: state.genSettings.payload.currency,
  standardPicklist: state.appData.standardPickList,
  currenciesPickList: state.appData.standardPickList.CURRENCY,
  entityLabelMap: state.loginForm.entityLabelMap,
  profilePermissions: state.appData.profilePermissions
});

export default connect(
  mapStateToProps,
  { setPreferredSmartListAction, removeRecordDetailsUrlAction, removeSelectedRecordAction, resetEntityDetails })(ListingTable);

import * as React from 'react';
import { shallow, mount } from 'enzyme';
import { MemoryRouter } from 'react-router-dom';

jest.mock('turndown', () => ({
  // tslint:disable-next-line
  default: function () {
    this.addRule = jest.fn();
  }
}));

import apiMiddleware from '../../middlewares/api';
import { dummyQuotation, dummyQuotationsList, mockQuotationsListLayout } from '../../components/page/Quotation/stub';
import { CONTACT_LIST, dealLayout } from '../../components/page/Deal/components/DealDetails/stubs';

const getAllDealsMock = jest.fn();
const getDealDetailsMock = jest.fn();
const getContactsSortedByStakeHolderMock = jest.fn();
jest.mock('../../components/page/Deal/service', () => ({
  getAllDeals: getAllDealsMock,
  getDealDetails: getDealDetailsMock,
  getContactsSortedByStakeHolder: getContactsSortedByStakeHolderMock
}));

const getAllCompaniesMock = jest.fn();
const getCompanyDetailsMock = jest.fn();
const getCompanyLogoMock = jest.fn();
const getDealsValueGroupedByForecastingStageMock = jest.fn();
jest.mock('../../components/page/Company/service', () => ({
  getAllCompanies: getAllCompaniesMock,
  getCompanyDetails: getCompanyDetailsMock,
  getCompanyLogo: getCompanyLogoMock,
  getDealsValueGroupedByForecastingStage: getDealsValueGroupedByForecastingStageMock,
  getJsonRule: jest.fn()
}));
const getAttachedPipelineMock = jest.fn();
jest.mock('../../components/page/pipeline/components/EntityPipeline/service', () => ({
  getAttachedPipeline: getAttachedPipelineMock
}));

const getEmailTemplateMock = jest.fn();
const listEmailTemplatesMock = jest.fn();
const getTemplateVariablesMock = jest.fn();
jest.mock('../../components/page/Communication/EmailTemplates/service', () => ({
  getEmailTemplate: getEmailTemplateMock,
  listEmailTemplates: listEmailTemplatesMock,
  getTemplateVariables: getTemplateVariablesMock
}));

const getWorkflowMock = jest.fn();
const getAllWorkflowsMock = jest.fn();
const getLayoutFieldsForEntityMock= jest.fn();
jest.mock('../../components/page/Workflows/service', () => ({
  getWorkflow: getWorkflowMock,
  getAllWorkflows: getAllWorkflowsMock,
  getLayoutFieldsForEntity: getLayoutFieldsForEntityMock
}));

const getWorkflowActionLogsListMock = jest.fn();
jest.mock('../../components/page/WorkflowActionLogs/service', () => ({
  getWorkflowActionLogsList: getWorkflowActionLogsListMock
}));

const getUniversalApiKeyMock = jest.fn();
jest.mock('../../components/page/ApiKeys/service', () => ({
  getUniversalApiKey: getUniversalApiKeyMock
}));

const getApiRequestDailyCountMock = jest.fn();
jest.mock('../../components/page/ApiUsage/service', () => ({
  getApiRequestDailyCount: getApiRequestDailyCountMock
}));

const getAllMeetingsMock = jest.fn();
const getMeetingListLayoutMock = jest.fn();
jest.mock('../../components/page/Meeting/service', () => ({
  getAllMeetings: getAllMeetingsMock,
  getMeetingListLayout: getMeetingListLayoutMock
}));

const getAllTasksMock = jest.fn();
jest.mock('../../components/page/TaskLayout/service', () => ({
  getAllTasks: getAllTasksMock
}));

const getSmartlistsValuesMock = jest.fn();
const getDashboardMock = jest.fn();
const getAllDashboardsMock = jest.fn();
const getReportDashletsDataMock = jest.fn();
const getAchievementForGoalMock = jest.fn();
jest.mock('../../components/page/Dashboard/service', () => ({
  getDashboard: getDashboardMock,
  getAllDashboards: getAllDashboardsMock,
  getSmartlistsValues: getSmartlistsValuesMock,
  getReportDashletsData: getReportDashletsDataMock,
  getAchievementForGoal: getAchievementForGoalMock
}));

const getReportListLayoutMock = jest.fn();
const getAllReportsMock = jest.fn();
const getReportConfigMock = jest.fn();
const getReportDetailsMock = jest.fn();
jest.mock('../../components/page/Reports/service', () => ({
  getAllReports: getAllReportsMock,
  getReportConfig: getReportConfigMock,
  getReportDetails: getReportDetailsMock,
  getReportListLayout: getReportListLayoutMock
}));

const getAllLeadCaptureFormsMock = jest.fn();
jest.mock('../../components/page/LeadCaptureForms/service', () => ({
  getAllLeadCaptureForms: getAllLeadCaptureFormsMock
}));

const fetchEntityLayoutMock = jest.fn();
jest.mock('../../services/service', () => ({
  fetchEntityLayout: fetchEntityLayoutMock
}));

const getSubscriptionDetailsMock = jest.fn();
const getTenantUsageStatisticsMock = jest.fn();
jest.mock('../../components/page/Billing/service', () => ({
  getSubscriptionDetails: getSubscriptionDetailsMock,
  getTenantUsageStatistics: getTenantUsageStatisticsMock
}));

const getAppOverviewMock = jest.fn();
const getAllAppsMock = jest.fn();
jest.mock('../../components/products/marketplace/service.ts', () => ({
  getAppOverview: getAppOverviewMock,
  getAllApps: getAllAppsMock
}));

const getAppDetailsMock = jest.fn();
const getAllPublicAppsMock = jest.fn();
jest.mock('../../components/products/marketplace/AllApps/service.ts', () => ({
  getAppDetails: getAppDetailsMock,
  getAllPublicApps: getAllPublicAppsMock
}));

const getInstalledAppsMock = jest.fn();
jest.mock('../../components/products/marketplace/InstalledApps/service.ts', () => ({
  getInstalledApps: getInstalledAppsMock
}));

const getDealLayoutMock = jest.fn();
const getAssignedDealLayoutMock = jest.fn();
jest.mock('../../components/page/Deal/components/DealDetails/DealInfo/service', () => ({
  getDealLayout: getDealLayoutMock,
  getAssignedDealLayout: getAssignedDealLayoutMock
}));

const getBulkJobsMock = jest.fn();
jest.mock('../../components/page/BulkJobs/service', () => ({
  getBulkJobs: getBulkJobsMock
}));

const getExportsListMock = jest.fn();
jest.mock('../../components/page/Export/service', () => ({
  getExportsList: getExportsListMock
}));

const getQuotationsListMock = jest.fn();
const getQuotationDetailsMock = jest.fn();
const getQuotationsListLayoutMock = jest.fn();
jest.mock('../../components/page/Quotation/service', () => ({
  getQuotationsList: getQuotationsListMock.mockReturnValue(Promise.resolve({ data: dummyQuotationsList })),
  getQuotationDetails: getQuotationDetailsMock.mockReturnValue(Promise.resolve({ data: dummyQuotation })),
  getQuotationsListLayout: getQuotationsListLayoutMock.mockReturnValue(Promise.resolve({ data: mockQuotationsListLayout }))
}));

const getCurrentIPMock = jest.fn();
jest.mock('../../components/page/IPConfigurations/service', () => ({
  getCurrentIP: getCurrentIPMock
}));

const LeadList = () => <div>mocked Lead List</div>;
jest.mock('../../components/page/Lead/components/LeadList/LeadList', () => ({
  __esModule: true,
  default: LeadList
}));

const LeadDetails = () => <div>mocked Lead Details</div>;
jest.mock('../../components/page/Lead/components/LeadDetails/LeadDetails', () => ({
  __esModule: true,
  default: LeadDetails
}));

const DealListLayout = () => <div>mocked Deal List</div>;
jest.mock('../../components/page/Deal/components/DealListLayout/DealListLayout', () => ({
  __esModule: true,
  default: DealListLayout
}));

const ContactList = () => <div>mocked Contact List</div>;
jest.mock('../../components/page/Contact/ContactList/ContactList', () => ({
  __esModule: true,
  default: ContactList
}));

const ContactDetails = () => <div>mocked Contact Details</div>;
jest.mock('../../components/page/Contact/ContactDetails/ContactDetails', () => ({
  __esModule: true,
  default: ContactDetails
}));

const CompanyDetails = () => <div>mocked Company Details</div>;
jest.mock('../../components/page/Company/components/CompanyDetails/CompanyDetails', () => ({
  __esModule: true,
  default: CompanyDetails
}));

const ReportCreate = () => <div>Mocked Report Builder for create page</div>;
jest.mock('../../components/page/Reports/components/ReportCreate/ReportCreate', () => ({
  __esModule: true,
  default: ReportCreate
}));

const ReportEdit = () => <div>Mocked Report Builder for edit page</div>;
jest.mock('../../components/page/Reports/components/ReportEdit/ReportEdit', () => ({
  __esModule: true,
  default: ReportEdit
}));

const ReportDetails = () => <div>Mocked Report Details</div>;
jest.mock('../../components/page/Reports/components/ReportDetails/ReportDetails', () => ({
  __esModule: true,
  default: ReportDetails
}));

const ReportList = () => <div>Mocked Report List</div>;
jest.mock('../../components/page/Reports/components/ReportList/ReportList', () => ({
  __esModule: true,
  default: ReportList
}));

const GoalCreate = () => <div>Mocked Goal Builder for create page</div>;
jest.mock('../../components/page/Goal/components/GoalCreate/GoalCreate', () => ({
  __esModule: true,
  default: GoalCreate
}));

const TaskListLayout = () => <div>mocked Lead List</div>;
jest.mock('../../components/page/TaskLayout/TaskListLayout', () => ({
  __esModule: true,
  default: TaskListLayout
}));

const MeetingListLayout = () => <div>mocked Meeting List</div>;
jest.mock('../../components/page/Meeting/components/MeetingList/MeetingDetails/MeetingListLayout', () => ({
  __esModule: true,
  default: MeetingListLayout
}));

const ProductsServices = () => <div>mocked Products Services</div>;
jest.mock('../../components/page/ProductsServices/Components/ProductsServices', () => ({
  __esModule: true,
  default: ProductsServices
}));

const CallListLayout = () => <div>mocked Meeting List</div>;
jest.mock('../../components/page/CallLayout/CallList/CallListLayout', () => ({
  __esModule: true,
  default: CallListLayout
}));

const LayoutsList = () => <div>mocked layouts List</div>;
jest.mock('../../components/page/Layouts/components/List/LayoutsList', () => ({
  __esModule: true,
  default: LayoutsList
}));

jest.mock('../../components/shared/TabbedPane/actions/tabbedPaneActions', () => ({
  getLayoutAction: () => jest.fn()
}));

jest.mock('../../components/shared/EntityLayout/actions/EntityLayoutActions', () => ({
  getEntityLayoutAction: () => jest.fn()
}));

const LayoutsCreate = () => <div>mocked layouts create form</div>;
jest.mock('../../components/page/Layouts/components/Form/Create/LayoutCreateForm', () => ({
  __esModule: true,
  default: LayoutsCreate
}));

const LayoutsEdit = () => <div>mocked layouts edit form</div>;
jest.mock('../../components/page/Layouts/components/Form/Edit/LayoutEditForm', () => ({
  __esModule: true,
  default: LayoutsEdit
}));

const CurrenciesList = () => <div>mocked currencies List</div>;
jest.mock('../../components/page/Currencies/components/CurrenciesList/CurrenciesList', () => ({
  __esModule: true,
  default: CurrenciesList
}));

const MyProfile = () => <div>mocked currencies List</div>;
jest.mock('../../components/page/user/profile/MyProfile', () => ({
  __esModule: true,
  default: MyProfile
}));

const ExchangeRateHistoryList = () => <div>mock exchange rate history page</div>;
jest.mock('../../components/page/Currencies/components/ExchangeRateHistoryList/ExchangeRateHistoryList', () => ({
  __esModule: true,
  default: ExchangeRateHistoryList
}));

const ExchangeRates = () => <div>mock exchange rate page</div>;
jest.mock('../../components/page/Currencies/components/ExchangeRates/ExchangeRates', () => ({
  __esModule: true,
  default: ExchangeRates
}));

const WorkflowEdit = () => <div>mock workflow edit</div>;
jest.mock('../../components/page/Workflows/components/Edit/WorkflowEdit', () => ({
  __esModule: true,
  default: WorkflowEdit
}));


const Dashboard = () => <div>mocked Component</div>;
jest.mock('../../components/page/Dashboard/DashboardLayout', () => ({
  __esModule: true,
  default: Dashboard
}));

const DealDetails = () => <div>mocked Component</div>;
jest.mock('../../components/page/Deal/components/DealDetails/DealDetails', () => ({
  __esModule: true,
  default: DealDetails
}));

const CompanyListLayout = () => <div>mocked Component</div>;
jest.mock('../../components/page/Company/components/CompanyList/CompanyListLayout/CompanyListLayout', () => ({
  __esModule: true,
  default: CompanyListLayout
}));

const EmailListLayout = () => <div>mocked Component</div>;
jest.mock('../../components/page/Email/EmailListLayout/EmailListLayout', () => ({
  __esModule: true,
  default: EmailListLayout
}));

const WhatsAppListLayout = () => <div>mocked Component</div>;
jest.mock('../../components/page/WhatsApp/WhatsAppListLayout/WhatsAppListLayout', () => ({
  __esModule: true,
  default: WhatsAppListLayout
}));

const DataManagement = () => <div>mocked Component</div>;
jest.mock('../../components/page/DataManagement/Components/DataManagement', () => ({
  __esModule: true,
  default: DataManagement
}));

const GoalEdit = () => <div>mocked Component</div>;
jest.mock('../../components/page/Goal/components/GoalEdit/GoalEdit', () => ({
  __esModule: true,
  default: GoalEdit
}));

const WorkflowsListLayout = () => <div>mocked Component</div>;
jest.mock('../../components/page/Workflows/components/List/WorkflowsListLayout', () => ({
  __esModule: true,
  default: WorkflowsListLayout
}));

const ReportListLayout = () => <div>mocked Component</div>;
jest.mock('../../components/page/Reports/components/ReportListLayout/ReportListLayout', () => ({
  __esModule: true,
  default: ReportListLayout
}));

const QuotationsListLayout = () => <div>mocked Component</div>;
jest.mock('../../components/page/Quotation/components/List/QuotationsListLayout', () => ({
  __esModule: true,
  default: QuotationsListLayout
}));

const QuotationDetails = () => <div>mocked Component</div>;
jest.mock('../../components/page/Quotation/components/Details/QuotationDetails', () => ({
  __esModule: true,
  default: QuotationDetails
}));

const FieldExecutivesList = () => <div>mock Component</div>;
jest.mock('../../components/page/FieldSales/FieldExecutivesList/FieldExecutivesList', () => ({
  __esModule: true,
  default: FieldExecutivesList
}));

const FieldSalesConfigurations = () => <div>mock Component</div>;
jest.mock('../../components/page/FieldSales/FieldSalesConfigurations/FieldSalesConfigurations', () => ({
  __esModule: true,
  default: FieldSalesConfigurations
}));

const UserShiftList = () => <div>mock Component</div>;
jest.mock('../../components/page/UserShifts/UserShiftList/UserShiftList', () => ({
  __esModule: true,
  default: UserShiftList
}));

const WebhooksList = () => <div>mock Component</div>;
jest.mock('../../components/page/Webhooks/components/List/WebhooksList', () => ({
  __esModule: true,
  default: WebhooksList
}));

const WebhookHistoryList = () => <div>mock Component</div>;
jest.mock('../../components/page/WebhookHistory/WebhookHistoryList', () => ({
  __esModule: true,
  default: WebhookHistoryList
}));

const CalendarSettings = () => <div>mock Component</div>;
jest.mock('../../components/page/Communication/CalendarSettings/Components/CalendarSettings/CalendarSettings', () => ({
  __esModule: true,
  default: CalendarSettings
}));

const IPConfigurationsList = () => <div>mock Component</div>;
jest.mock('../../components/page/IPConfigurations/components/IPConfigurationsList/IPConfigurationsList', () => ({
  __esModule: true,
  default: IPConfigurationsList
}));

const CreateIPConfiguration = () => <div>mock Component</div>;
jest.mock('../../components/page/IPConfigurations/components/CreateIPconfiguration/CreateIPConfiguration', () => ({
  __esModule: true,
  default: CreateIPConfiguration
}));

const EditIPConfiguration = () => <div>mock Component</div>;
jest.mock('../../components/page/IPConfigurations/components/EditIPConfiguration/EditIPConfiguration', () => ({
  __esModule: true,
  default: EditIPConfiguration
}));

const TwoFactorAuthentication = () => <div>mock Component</div>;
jest.mock('../../components/page/TwoFactorAuthentication/TwoFactorAuthentication', () => ({
  __esModule: true,
  default: TwoFactorAuthentication
}));

const LiveLocationTracking = () => <div>mock Component</div>;
jest.mock('../../components/page/FieldSales/LiveLocationTracking/LiveLocationTracking', () => ({
  __esModule: true,
  default: LiveLocationTracking
}));

const UserShiftEdit = () => <div>mock Component</div>;
jest.mock('../../components/page/UserShifts/UserShiftEdit/UserShiftEdit', () => ({
  __esModule: true,
  default: UserShiftEdit
}));

const AddAgents = () => <div>mock Component</div>;
jest.mock('../../components/page/Communication/WhatsAppSettings/Agents/AddAgents', () => ({
  __esModule: true,
  default: AddAgents
}));

const WhatsAppSettingsLayout = () => <div>mock Component</div>;
jest.mock('../../components/page/Communication/WhatsAppSettings/WhatsAppSettingsLayout', () => ({
  __esModule: true,
  default: WhatsAppSettingsLayout
}));

const WhatsAppTemplatesList = () => <div>mock Component</div>;
jest.mock('../../components/page/Communication/WhatsAppTemplates/List/WhatsAppTemplatesList', () => ({
  __esModule: true,
  default: WhatsAppTemplatesList
}));

const WhatsAppBillingInformation = () => <div>mock Component</div>;
jest.mock('../../components/page/Communication/WhatsAppSettings/WhatsAppBillingInformation/WhatsAppBillingInformation', () => ({
  __esModule: true,
  default: WhatsAppBillingInformation
}));

const CreateWhatsAppTemplate = () => <div>mock Component</div>;
jest.mock('../../components/page/Communication/WhatsAppTemplates/CreateEditTemplate/CreateWhatsAppTemplate', () => ({
  __esModule: true,
  default: CreateWhatsAppTemplate
}));

const EmailAssistant = () => <div>mock Component</div>;
jest.mock('../../components/shared/AIFeatures/EmailAssistant/EmailAssistant', () => ({
  __esModule: true,
  default: EmailAssistant
}));

const CreateBulkJob = () => <div>mock Component</div>;
jest.mock('../../components/page/BulkJobs/components/Create/CreateBulkJob', () => ({
  __esModule: true,
  default: CreateBulkJob
}));

const WorkflowActionLogsListLayout = () => <div>mock Component</div>;
jest.mock('../../components/page/WorkflowActionLogs/WorkflowActionLogsListLayout/WorkflowActionLogsListLayout', () => ({
  __esModule: true,
  default: WorkflowActionLogsListLayout
}));

const NotificationSettings = () => <div>mock Component</div>;
jest.mock('../../components/page/NotificationSettings/NotificationSettings', () => ({
  __esModule: true,
  default: NotificationSettings
}));

const CampaignList = () => <div>mock Component</div>;
jest.mock('../../components/page/Campaign/CampaignList/CampaignList', () => ({
  __esModule: true,
  default: CampaignList
}));

const CampaignCreate = () => <div>mock Component</div>;
jest.mock('../../components/page/Campaign/CampaignCreate/CampaignCreate', () => ({
  __esModule: true,
  default: CampaignCreate
}));

const CampaignEdit = () => <div>mock Component</div>;
jest.mock('../../components/page/Campaign/CampaignEdit/CampaignEdit', () => ({
  __esModule: true,
  default: CampaignEdit
}));

const CampaignView = () => <div>mock Component</div>;
jest.mock('../../components/page/Campaign/CampaignView/CampaignView', () => ({
  __esModule: true,
  default: CampaignView
}));

const CampaignDetails = () => <div>mock Component</div>;
jest.mock('../../components/page/Campaign/CampaignDetails/CampaignDetails', () => ({
  __esModule: true,
  default: CampaignDetails
}));

const CampaignActivityList = () => <div>mock Component</div>;
jest.mock('../../components/page/Campaign/CampaignActivityList/CampaignActivityList', () => ({
  __esModule: true,
  default: CampaignActivityList
}));

const CampaignActivityCreate = () => <div>mock Component</div>;
jest.mock('../../components/page/Campaign/CampaignActivityCreate/CampaignActivityCreate', () => ({
  __esModule: true,
  default: CampaignActivityCreate
}));

const CampaignActivityEdit = () => <div>mock Component</div>;
jest.mock('../../components/page/Campaign/CampaignActivityEdit/CampaignActivityEdit', () => ({
  __esModule: true,
  default: CampaignActivityEdit
}));

const CampaignActivityRecipientStatusList = () => <div>mock Component</div>;
jest.mock('../../components/page/Campaign/CampaignActivityRecipientStatusList/CampaignActivityRecipientStatusList', () => ({
  __esModule: true,
  default: CampaignActivityRecipientStatusList
}));

import Routes, { isAuthenticated } from './Routes';
import { Provider } from 'react-redux';
import configureStore from 'redux-mock-store';
import thunk from 'redux-thunk';
import { storeData as mockedStore } from '../../store/mockStore';
import { formInitialState } from '../../higherOrderComponents/Form/reducer/FormReducer';
import EmailTemplatesLayout from '../../components/page/Communication/EmailTemplates/EmailTemplatesLayout';
import { EditEmailTemplate } from '../../components/page/Communication/EmailTemplates/FormLayout/EditEmailTemplate';
import { CreateEmailTemplate } from '../../components/page/Communication/EmailTemplates/FormLayout/CreateEmailTemplate';
import { templateInitialData } from '../../components/page/Communication/EmailTemplates/reducers/EmailTemplateReducer';
import { dummyEmailTemplates } from '../../components/page/Communication/EmailTemplates/stub';
import { mockLeadCreateLayout } from '../../components/page/Workflows/stubs';
import { dummyMeetingList, mockMeetingEditLayout, mockMeetingListLayout } from '../../components/page/Meeting/stubs';
import { reportConfig } from '../../components/page/Reports/components/ReportBuilder/stubs';
import { leadCaptureFormsListData } from '../../components/page/LeadCaptureForms/components/list/stub';
import { tenantSubscriptionDetails, tenantUsageStatistics } from '../../components/page/Billing/stubs';
import { appListResponse, InstalledAppsListResponse, stepOneResponse } from '../../components/products/marketplace/stub';
import { appDetailsResponse } from '../../components/products/marketplace/AllApps/stub';
import { getAllDashboardsResponse, getDashboardResponse, getGoalAchievementResponse } from '../../components/page/Dashboard/stub';
import { bulkJobList } from '../../components/page/BulkJobs/stubs';
import BulkJobsListComponent from '../../components/page/BulkJobs/components/List/BulkJobsList';
import ExportListComponent from '../../components/page/Export/components/List/ExportList';
import { exportsList } from '../../components/page/Export/stubs';
import CreateExportJob from '../../components/page/Export/components/Create/CreateExportJob';
import FieldExecutiveTimeline from '../../components/page/FieldSales/FieldExecutiveTimeline/FieldExecutiveTimeline';
import WorkflowView from '../../components/page/Workflows/components/View/WorkflowView';
import UserShiftCreate from '../../components/page/UserShifts/UserShiftCreate/UserShiftCreate';
import WorkflowTemplates from '../../components/page/Workflows/components/Template/WorkflowTemplates';
import { mockGetWorkflowActionLogsListResponse, mockWorkflowActionLogsListLayout } from '../../components/page/WorkflowActionLogs/stubs';
import ApiUsage from '../../components/page/ApiUsage/ApiUsage';
import ScoringRulesListLayout from '../../components/page/ScoringRules/ScoringRulesListLayout/ScoringRulesListLayout';

describe('Route components', () => {

  let wrapper;

  const props = {
    Component: '/'
  };
  let store;
  const profilePermissions = [{
    id: 22,
    name: 'products-services',
    description: 'has access to Products and Services',
    limits: 5,
    units: 'count',
    action: {
      read: true,
      write: true,
      update: true,
      delete: false,
      email: false,
      call: false,
      sms: false,
      task: false,
      note: false,
      readAll: true,
      updateAll: true
    }
  }, {
    id: 12,
    name: 'deal',
    description: 'has permission to deal resource',
    limits: 50,
    units: 'count',
    action: {
      read: true,
      write: true,
      update: true,
      delete: true,
      email: false,
      call: false,
      sms: false,
      task: true,
      note: true,
      readAll: true,
      updateAll: true
    }
  }, {
    id: 14,
    name: 'export',
    description: 'has permission to export',
    limits: 50,
    units: 'count',
    action: {
      read: false,
      write: true,
      update: false,
      delete: false,
      email: false,
      call: false,
      sms: false,
      task: false,
      note: false,
      readAll: true,
      updateAll: false
    }
  }];
  const tenantRosourcePermission = {
    id: 13,
    name: 'tenant',
    description: 'has permission to tenant resource',
    limits: 50,
    units: 'count',
    action: {
      read: true,
      write: false,
      update: true,
      delete: false,
      email: false,
      call: false,
      sms: false,
      task: false,
      note: false,
      readAll: true,
      updateAll: true
    }
  };

  const storeData = {
    ...mockedStore,
    appData: {
      profilePermissions,
      error: undefined,
      loading: false,
      standardPickList: {
        CURRENCY: [
          { id:400, name:'USD', displayName:'United States Dollars' },
          { id:401, name:'EUR', displayName:'Euro' },
          { id:431, name:'INR', displayName:'India Rupees' }
        ]
      }
    },
    entityLayout: {
      layout: {
        EDIT: { tasks: {} }
      }
    },
    header: {
      headerList: {},
      app: 'Setup',
      profile: {
        id: 123,
        firstName: 'MS',
        lastName: 'D'
      }
    },
    formData: {},
    slide: {
      slider: true
    },
    app: { slider: true },
    listLayout: { appliedFilters: {}, page: {} },
    entityEditModal: {
      entityEditSuccessfulFor: {
        deals: false
      }
    },
    leadConversion: {
      leadConversionSuccessful: false
    },
    entityShare: {
      showEntityShareModal: false,
      headerSection: []
    },
    form: {
      shareEntityForm: {}
    },
    tabPane: { layout: [] },
    marketplace: {
      actions: []
    },
    forms: {

    }
  };

  const mockWorkflowListLayout = {
    leftNav: false,
    pageConfig: {
      actionConfig: {},
      tableConfig: {
        fetchURL: '/search/workflows',
        searchService: 'search',
        recordClickAction: 'VIEW',
        clickActionUrl: null,
        columns: [
          {
            id: 'name',
            header: 'Name',
            isStandard: true,
            isFilterable: true,
            isSortable: true,
            isInternal: false,
            fieldType: 'TEXT_FIELD',
            picklist: null,
            lookup: null,
            values: null
          },
          {
            id: 'ownedBy',
            header: 'Owner',
            isStandard: true,
            isFilterable: true,
            isSortable: true,
            isInternal: false,
            fieldType: 'LOOK_UP',
            picklist: null,
            lookup: {
              entity: 'USER',
              lookupUrl: '/users/lookup?q=firstName:'
            },
            values: null
          }
        ]
      }
    },
    defaultConfig: {
      fields: ['name', 'ownedBy']
    }
  };

  const taskList = {
    content:[{
      assignedTo: 793,
      completed: false,
      createdBy: 793,
      customFieldValues: null,
      deleted: false,
      description: null,
      id: 5636,
      metaData: null,
      name: 'Test1',
      ownerId: 793,
      priority: null
    }],
    totalElements: 2,
    totalPages: 1
  };

  const getTemplateVariablesPromise = Promise.resolve({ data: { variables: [{ id: 4070, displayName: 'First Name', internalName: 'firstName', standard: true }] } });
  getTemplateVariablesMock.mockReturnValue(getTemplateVariablesPromise);

  const getLayoutFieldsForEntityPromise = Promise.resolve({ data: mockLeadCreateLayout });
  getLayoutFieldsForEntityMock.mockReturnValue(getLayoutFieldsForEntityPromise);

  const getWorkflowsListLayoutPromise = Promise.resolve({ data: mockWorkflowListLayout });
  fetchEntityLayoutMock.mockReturnValue(getWorkflowsListLayoutPromise);

  const getAllWorkflowsPromise = Promise.resolve({ data: { content: [], totalElements: 0, totalPages: 0 } });
  getAllWorkflowsMock.mockReturnValue(getAllWorkflowsPromise);

  const getWorkflowPromise = Promise.resolve({ data: { id: 1 } });
  getWorkflowMock.mockReturnValue(getWorkflowPromise);

  const getWorkflowActionLogsListLayoutPromise = Promise.resolve({ data: mockWorkflowActionLogsListLayout });
  fetchEntityLayoutMock.mockReturnValue(getWorkflowActionLogsListLayoutPromise);

  const getWorkflowActionLogsListPromise = Promise.resolve({ data: mockGetWorkflowActionLogsListResponse });
  getWorkflowActionLogsListMock.mockReturnValue(getWorkflowActionLogsListPromise);

  const listEmailTemplatesPromise = Promise.resolve({ data: dummyEmailTemplates });
  listEmailTemplatesMock.mockReturnValue(listEmailTemplatesPromise);

  const getEmailTemplatePromise = Promise.resolve({ data: dummyEmailTemplates.content[0] });
  getEmailTemplateMock.mockReturnValue(getEmailTemplatePromise);

  const getUniversalApiKeyPromise = Promise.resolve({ data: { apiKey: '41d68800-faa5-4cff-921a-514cac542e7c' } });
  getUniversalApiKeyMock.mockReturnValue(getUniversalApiKeyPromise);

  const getApiRequestDailyCountPromise = Promise.resolve({ data: [{ usagesDate: '2024-10-14T18:29:00.000Z', event: 'UI', count: 5 }] });
  getApiRequestDailyCountMock.mockReturnValue(getApiRequestDailyCountPromise);

  const getAllMeetingsPromise = Promise.resolve({ data: dummyMeetingList });
  getAllMeetingsMock.mockReturnValue(getAllMeetingsPromise);

  const getMeetingListLayoutPromise = Promise.resolve({ data: mockMeetingListLayout });
  getMeetingListLayoutMock.mockReturnValue(getMeetingListLayoutPromise);

  const getMeetingLayoutPromise = Promise.resolve({ data: mockMeetingEditLayout });
  fetchEntityLayoutMock.mockReturnValue(getMeetingLayoutPromise);

  const getAllTasksPromise = Promise.resolve({ data: taskList });
  getAllTasksMock.mockReturnValue(getAllTasksPromise);

  const entitiesPromise = Promise.resolve({ data: [] });
  getSmartlistsValuesMock.mockReturnValue(entitiesPromise);

  const reportDataPromise = Promise.resolve({ data: [] });
  getReportDashletsDataMock.mockReturnValue(reportDataPromise);

  const getAllReportsPromise = Promise.resolve({ data: { content: [], totalElements: 0, totalPages: 0 } });
  getAllReportsMock.mockReturnValue(getAllReportsPromise);

  const getReportListLayoutPromise = Promise.resolve({ data: { content: [], totalElements: 0, totalPages: 0 } });
  getReportListLayoutMock.mockReturnValue(getReportListLayoutPromise);

  const getReportConfigPromise = Promise.resolve({ data: reportConfig });
  getReportConfigMock.mockReturnValue(getReportConfigPromise);

  const getReportDetailsPromise = Promise.resolve({ data: { id: 1, name: 'Test Report' } });
  getReportDetailsMock.mockReturnValue(getReportDetailsPromise);

  const getAllLeadCaptureFormsPromise = Promise.resolve({ data: leadCaptureFormsListData });
  getAllLeadCaptureFormsMock.mockReturnValue(getAllLeadCaptureFormsPromise);

  const fetchEntityLayoutPromise = Promise.resolve({ data: mockLeadCreateLayout });
  fetchEntityLayoutMock.mockReturnValue(fetchEntityLayoutPromise);

  const getTenantUsageStatisticsPromise = Promise.resolve({ data: tenantUsageStatistics });
  getTenantUsageStatisticsMock.mockReturnValue(getTenantUsageStatisticsPromise);

  const getSubscriptionDetailsPromise = Promise.resolve({ data: tenantSubscriptionDetails });
  getSubscriptionDetailsMock.mockReturnValue(getSubscriptionDetailsPromise);

  const getAppDetailsPromise = Promise.resolve({ data: appDetailsResponse });
  getAppDetailsMock.mockReturnValue(getAppDetailsPromise);
  const getAllPublicAppsPromise = Promise.resolve({ data: appListResponse });
  getAllPublicAppsMock.mockReturnValue(getAllPublicAppsPromise);

  const getInstalledAppsPromise = Promise.resolve({ data: InstalledAppsListResponse });
  getInstalledAppsMock.mockReturnValue(getInstalledAppsPromise);

  const getAppOverviewPromise = Promise.resolve({ data: stepOneResponse });
  getAppOverviewMock.mockReturnValue(getAppOverviewPromise);
  const getAllAppsPromise = Promise.resolve({ data: appListResponse });
  getAllAppsMock.mockReturnValue(getAllAppsPromise);

  const getAllDashboardsPromise = Promise.resolve({ data: getAllDashboardsResponse });
  getAllDashboardsMock.mockReturnValue(getAllDashboardsPromise);

  const getDashboardPromise = Promise.resolve({ data: getDashboardResponse });
  getDashboardMock.mockReturnValue(getDashboardPromise);

  const getDealLayoutProimse = Promise.resolve({ data: dealLayout });
  getDealLayoutMock.mockReturnValue(getDealLayoutProimse);

  const getBulkJobsPromise = Promise.resolve({ data: bulkJobList });
  getBulkJobsMock.mockReturnValue(getBulkJobsPromise);

  const getExportsListPromise = Promise.resolve({ data: exportsList });
  getExportsListMock.mockReturnValue(getExportsListPromise);

  getAssignedDealLayoutMock.mockReturnValue(getDealLayoutProimse);

  const getAchievementForGoalPromise = Promise.resolve({ data: getGoalAchievementResponse });
  getAchievementForGoalMock.mockReturnValue(getAchievementForGoalPromise);

  const getCurrentIPPromise = Promise.resolve({ data: { ip: '114.334.223.233' } });
  getCurrentIPMock.mockReturnValue(getCurrentIPPromise);

  const mockmiddlewares = [thunk, apiMiddleware];
  const mockStore = configureStore(mockmiddlewares);
  beforeEach(()=>{
    jest.spyOn(Storage.prototype, 'getItem').mockReturnValue('DummyToken');
    store = mockStore(storeData);
  });


  afterEach(()=>{
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  it('should redirect to the sales app if local storage is set to sales', ()=> {
    jest.spyOn(Storage.prototype, 'getItem').mockReturnValue('Sales');
    wrapper = shallow(<Routes {...props} />);
    const mockComponent = jest.fn();
    isAuthenticated(mockComponent);
    const element = wrapper.find('Route').at(0);
    const authWrapper = element.props().render();
    expect(authWrapper.props).toEqual({ push: false, to: '/sales/home' });
  });

  it('should redirect to the setup app if local storage is not set to setup', ()=> {
    jest.spyOn(Storage.prototype, 'getItem').mockReturnValue('Setup');
    wrapper = shallow(<Routes {...props} />);
    const mockComponent = jest.fn();
    isAuthenticated(mockComponent);
    const element = wrapper.find('Route').at(0);
    const authWrapper = element.props().render();
    expect(authWrapper.props).toEqual({ push: false, to: '/setup' });
  });

  describe('Test cases for Product services component', ()=>{
    it('should render ProductsServices component when url is "setup/products-services/list"', ()=>{
      const newWrapper = mount(
        <MemoryRouter initialEntries={['/setup/products-services/list']}>
          <Provider store={store}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );
      expect(newWrapper.find(ProductsServices).length).toBe(1);
    });
  });

  it('should redirect to Dashboard when the url is "/sales/home"', () => {
    getAllDealsMock.mockReturnValue(Promise.resolve({ data: {} }));
    fetchEntityLayoutMock.mockReturnValue(Promise.resolve(
        { data: { pageConfig: { tableConfig: { columns: [] } }, leftNav: {}, defaultConfig: {} } }
                                          )
    );
    const newWrapper = mount(
      <MemoryRouter initialEntries={['/sales/home']}>
        <Provider store={store}>
          <Routes/>
        </Provider>
      </MemoryRouter>
    );

    expect(newWrapper.find(Dashboard).length).toBe(1);
  });

  it('should render DealDetails component when url is "/sales/deals/details/1"', () => {
    getDealDetailsMock.mockReturnValue(Promise.resolve(
      {
        data: {
          id: 12,
          name: 'My Deal',
          ownedBy: {
            id: 111,
            name: 'MSD'
          },
          estimatedValue: {
            currencyId: 431,
            value: 1000
          },
          estimatedClosureOn: '2020-03-10T17:25:40.355+0000'
        }
      }
    ));
    getAttachedPipelineMock.mockReturnValue(Promise.resolve({ data: { stages: [] } }));
    const contactDetailsPromise = Promise.resolve(
      {
        data: CONTACT_LIST
      }
    );
    getContactsSortedByStakeHolderMock.mockReturnValue(contactDetailsPromise);

    const newWrapper = mount(
      <MemoryRouter initialEntries={['/sales/deals/details/1']}>
        <Provider store={store}>
          <Routes/>
        </Provider>
      </MemoryRouter>
    );

    expect(newWrapper.find(DealDetails).length).toBe(1);
  });

  describe('Deal List', () => {
    it('should not render DealList component when user without permission route to "/deals/list"', () => {
      const storeWithoutDealPermissions = mockStore(
        {
          ...storeData,
          appData: {
            ...storeData.appData,
            profilePermissions: [
              profilePermissions[0],
              {
                id: 12,
                name: 'deal',
                description: 'has permission to deal resource',
                limits: 50,
                units: 'count',
                action: {
                  read: false,
                  write: false,
                  update: false,
                  delete: false,
                  email: false,
                  call: false,
                  sms: false,
                  task: false,
                  note: false,
                  readAll: false,
                  updateAll: false
                }
              }
            ]
          }
        }
      );
      const newWrapper = mount(
        <MemoryRouter initialEntries={['/sales/deals/list']}>
          <Provider store={storeWithoutDealPermissions}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find('DealListLayout').length).toBe(0);
    });

    it('should render DealList component when user with permission route to "/deals/list"', () => {
      getAllDealsMock.mockReturnValue(Promise.resolve({ data: {} }));
      fetchEntityLayoutMock.mockReturnValue(Promise.resolve(
        { data: { pageConfig: { tableConfig: { columns: [] } }, leftNav: {}, defaultConfig: {} } }
        )
      );
      const newWrapper = mount(
        <MemoryRouter initialEntries={['/sales/deals/list']}>
          <Provider store={store}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find('DealListLayout').length).toBe(1);
    });
  });

  it('should render CompanyDetails component when url is "/companies/details/1"', () => {
    const storeWithCompanyReadPermission = mockStore(
      {
        ...storeData,
        appData: {
          ...storeData.appData,
          profilePermissions: [
            ...profilePermissions,
            {
              id: 12,
              name: 'company',
              description: 'has permission to company resource',
              limits: 50,
              units: 'count',
              action: {
                read: true,
                write: true,
                update: true,
                delete: true,
                email: false,
                call: false,
                sms: false,
                task: false,
                note: false,
                readAll: false,
                updateAll: false
              }
            }
          ]
        },
        smartListRules: {
          preferredSmartlistLoading: false
        }
      }
    );

    getCompanyDetailsMock.mockReturnValue(Promise.resolve(
      {
        data: {
          name: 'My Company',
          industry: { id: 2, name: 'Farma' },
          businessType: { id: 2, name: 'product' },
          annualRevenue: {
            currencyId: 431,
            value: 1000
          },
          numberOfEmployees: { id: 2, name: '50-100' },
          phoneNumbers: [{
            type: 'MOBILE',
            code: 'IN',
            dialCode: '+91',
            value: '9999999999',
            primary: true
          }],
          emails: null,
          website: 'facebook.com',
          address: 'Pune, Maharashtra',
          timezone: null,
          dnd: null,
          city: null,
          country: null,
          state: null,
          zipcode: null,
          facebook: null,
          twitter: null,
          linkedIn: null
        }
      }
    ));
    getCompanyLogoMock.mockReturnValue(Promise.resolve({ data: {} }));
    getDealsValueGroupedByForecastingStageMock.mockReturnValue(Promise.resolve({ data: {} }));
    const newWrapper = mount(
      <MemoryRouter initialEntries={['/sales/companies/details/1']}>
        <Provider store={storeWithCompanyReadPermission}>
          <Routes/>
        </Provider>
      </MemoryRouter>
    );

    expect(newWrapper.find('CompanyDetails').length).toBe(1);
  });

  describe('Company List', () => {
    it('should not render CompanyList component when user without permission route to "/companies/list"', () => {
      const newWrapper = mount(
        <MemoryRouter initialEntries={['/sales/companies/list']}>
          <Provider store={store}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find('CompanyListLayout').length).toBe(0);
    });

    it('should render CompanyList component when user with permission route to "/companies/list"', () => {
      getAllCompaniesMock.mockReturnValue(Promise.resolve({ data: {} }));
      fetchEntityLayoutMock.mockReturnValue(Promise.resolve(
        { data: { pageConfig: { tableConfig: { columns: [] } }, leftNav: {}, defaultConfig: { fields: ['name'] } } }
        )
      );
      const storeWithCompanyPermissions = mockStore(
        {
          ...storeData,
          appData: {
            ...storeData.appData,
            profilePermissions: [
              ...profilePermissions,
              {
                id: 12,
                name: 'company',
                description: 'has permission to company resource',
                limits: 50,
                units: 'count',
                action: {
                  read: true,
                  write: true,
                  update: true,
                  delete: true,
                  email: false,
                  call: false,
                  sms: false,
                  task: true,
                  note: true,
                  readAll: true,
                  updateAll: true
                }
              }
            ]
          },
          smartListRules: {
            preferredSmartlistLoading: false
          }
        }
      );

      const newWrapper = mount(
        <MemoryRouter initialEntries={['/sales/companies/list']}>
          <Provider store={storeWithCompanyPermissions}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find('CompanyListLayout').length).toBe(1);
    });
  });

  describe('Task List', () => {
    const storeWithTaskReadPermission = mockStore(
      {
        ...storeData,
        appData: {
          ...storeData.appData,
          profilePermissions: [
            ...profilePermissions,
            {
              id: 12,
              name: 'task',
              description: 'has permission to task resource',
              limits: 50,
              units: 'count',
              action: {
                read: true,
                write: true,
                update: true,
                delete: true,
                email: false,
                call: false,
                sms: false,
                task: true,
                note: true,
                readAll: true,
                updateAll: true
              }
            }
          ]
        },
        smartListRules: {
          preferredSmartlistLoading: false
        }
      }
    );
    it('should render TaskListLayout component when user routes to "/tasks/list"', () => {
      const newWrapper = mount(
        <MemoryRouter initialEntries={['/sales/tasks/list']}>
          <Provider store={storeWithTaskReadPermission}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find('TaskListLayout').length).toBe(1);
    });
  });

  describe('Call List', () => {
    const storeWithCallReadPermission = mockStore(
      {
        ...storeData,
        appData: {
          ...storeData.appData,
          profilePermissions: [
            ...profilePermissions,
            {
              id: 12,
              name: 'call',
              description: 'has permission to call resource',
              limits: 50,
              units: 'count',
              action: {
                read: true,
                write: true,
                update: true,
                delete: true,
                email: false,
                call: false,
                sms: false,
                task: false,
                note: false,
                readAll: false,
                updateAll: false
              }
            }
          ]
        },
        smartListRules: {
          preferredSmartlistLoading: false
        }
      }
    );

    it('should render CallListLayout component when user routes to "sales/calls/list"', () => {
      const newWrapper = mount(
        <MemoryRouter initialEntries={['/sales/calls/list']}>
          <Provider store={storeWithCallReadPermission}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find('CallListLayout').length).toBe(1);
    });
  });

  describe('Report List', () => {
    it('should not render ReportList component when user without report read permission route to "/sales/reports/list"', () => {
      const newWrapper = mount(
        <MemoryRouter initialEntries={['/sales/reports/list']}>
          <Provider store={store}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find(ReportList).length).toBe(0);
    });

    it('should render ReportList component when user with report read permission route to "/sales/reports/list"', () => {
      const storeWithReportReadPermissions = mockStore(
        {
          ...storeData,
          appData: {
            ...storeData.appData,
            profilePermissions: [
              ...profilePermissions,
              {
                id: 12,
                name: 'report',
                description: 'has permission to company resource',
                limits: 50,
                units: 'count',
                action: {
                  read: true,
                  write: false,
                  update: false,
                  delete: false,
                  email: false,
                  call: false,
                  sms: false,
                  task: false,
                  note: false,
                  readAll: false,
                  updateAll: false
                }
              }
            ]
          }
        }
      );

      const newWrapper = mount(
        <MemoryRouter initialEntries={['/sales/reports/list']}>
          <Provider store={storeWithReportReadPermissions}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find(ReportListLayout).length).toBe(1);
    });
  });

  describe('Report Create', () => {
    it('should not render ReportCreate component when user without report write permission route to "/sales/reports/create"', () => {
      const newWrapper = mount(
        <MemoryRouter initialEntries={['/sales/reports/create']}>
          <Provider store={store}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find(ReportCreate).length).toBe(0);
    });

    it('should render ReportCreate component when user with report create permission route to "/sales/reports/create"', () => {
      const storeWithReportCreatePermissions = mockStore(
        {
          ...storeData,
          appData: {
            ...storeData.appData,
            profilePermissions: [
              ...profilePermissions,
              {
                id: 12,
                name: 'report',
                description: 'has permission to company resource',
                limits: 50,
                units: 'count',
                action: {
                  read: true,
                  write: true,
                  update: false,
                  delete: false,
                  email: false,
                  call: false,
                  sms: false,
                  task: false,
                  note: false,
                  readAll: false,
                  updateAll: false
                }
              }
            ]
          }
        }
      );

      const newWrapper = mount(
        <MemoryRouter initialEntries={['/sales/reports/create']}>
          <Provider store={storeWithReportCreatePermissions}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find(ReportCreate).length).toBe(1);
    });
  });

  describe('Report Edit', () => {
    it('should not render ReportEdit component when user without report update permission route to "/sales/reports/create"', () => {
      const newWrapper = mount(
        <MemoryRouter initialEntries={['/sales/reports/create']}>
          <Provider store={store}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find(ReportEdit).length).toBe(0);
    });

    it('should render ReportEdit component when user with report update permission route to "/sales/reports/edit/:reportId"', () => {
      const storeWithReportCreatePermissions = mockStore(
        {
          ...storeData,
          appData: {
            ...storeData.appData,
            profilePermissions: [
              ...profilePermissions,
              {
                id: 12,
                name: 'report',
                description: 'has permission to company resource',
                limits: 50,
                units: 'count',
                action: {
                  read: true,
                  write: true,
                  update: true,
                  delete: false,
                  email: false,
                  call: false,
                  sms: false,
                  task: false,
                  note: false,
                  readAll: false,
                  updateAll: false
                }
              }
            ]
          }
        }
      );

      const newWrapper = mount(
        <MemoryRouter initialEntries={['/sales/reports/edit/4']}>
          <Provider store={storeWithReportCreatePermissions}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find(ReportEdit).length).toBe(1);
    });
  });

  describe('Report Details', () => {
    const storeWithReportReadPermission = mockStore(
      {
        ...storeData,
        appData: {
          ...storeData.appData,
          profilePermissions: [
            ...profilePermissions,
            {
              id: 12,
              name: 'report',
              description: 'has permission to report resource',
              limits: 50,
              units: 'count',
              action: {
                read: true,
                write: true,
                update: true,
                delete: false,
                email: false,
                call: false,
                sms: false,
                task: false,
                note: false,
                readAll: false,
                updateAll: false
              }
            }
          ]
        }
      }
    );

    it('should render ReportDetails component when user route to "/sales/reports/details/:reportId"', () => {
      const newWrapper = mount(
        <MemoryRouter initialEntries={['/sales/reports/details/1']}>
          <Provider store={storeWithReportReadPermission}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find(ReportDetails).length).toBe(1);
    });
  });

  describe('Lead', () => {
    it('should not render LeadList component when user without permission route to "/leads/list"', () => {
      const newWrapper = mount(
        <MemoryRouter initialEntries={['/sales/leads/list']}>
          <Provider store={store}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find('LeadList').length).toBe(0);
    });

    it('should render LeadList component when user with permission route to "/leads/list"', () => {

      const storeWithLeadPermissions = mockStore(
        {
          ...storeData,
          appData: {
            ...storeData.appData,
            profilePermissions: [
              ...profilePermissions,
              {
                id: 12,
                name: 'lead',
                description: 'has permission to lead resource',
                limits: 50,
                units: 'count',
                action: {
                  read: true,
                  write: true,
                  update: true,
                  delete: true,
                  email: false,
                  call: false,
                  sms: false,
                  task: true,
                  note: true,
                  readAll: true,
                  updateAll: true
                }
              }
            ]
          }
        }
      );

      const newWrapper = mount(
        <MemoryRouter initialEntries={['/sales/leads/list']}>
          <Provider store={storeWithLeadPermissions}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find('LeadList').length).toBe(1);
    });

    it('should render Lead Details component when user with permission route to "/leads/details/5"', () => {

      const storeWithLeadPermissions = mockStore(
        {
          ...storeData,
          appData: {
            ...storeData.appData,
            profilePermissions: [
              ...profilePermissions,
              {
                id: 12,
                name: 'lead',
                description: 'has permission to lead resource',
                limits: 50,
                units: 'count',
                action: {
                  read: true,
                  write: true,
                  update: true,
                  delete: true,
                  email: false,
                  call: false,
                  sms: false,
                  task: true,
                  note: true,
                  readAll: true,
                  updateAll: true
                }
              }
            ]
          }
        }
      );

      const newWrapper = mount(
        <MemoryRouter initialEntries={['/sales/leads/details/5']}>
          <Provider store={storeWithLeadPermissions}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find('LeadDetails').length).toBe(1);
    });
  });

  describe('Goal', () => {
    describe('Goal Create', () => {
      it('should not render Goal Create component when user without goal write permission route to "/setup/goals/create"', () => {
        const newWrapper = mount(
          <MemoryRouter initialEntries={['/setup/goals/create']}>
            <Provider store={store}>
              <Routes/>
            </Provider>
          </MemoryRouter>
        );

        expect(newWrapper.find(GoalCreate).length).toBe(0);
      });

      it('should render Goal create component when user with goal create permission route to "/setup/goals/create"', () => {
        const storeWithGoalCreatePermissions = mockStore(
          {
            ...storeData,
            appData: {
              ...storeData.appData,
              profilePermissions: [
                ...profilePermissions,
                {
                  id: 12,
                  name: 'goal',
                  description: 'has permission to company resource',
                  limits: 50,
                  units: 'count',
                  action: {
                    read: true,
                    write: true,
                    update: false,
                    delete: false,
                    email: false,
                    call: false,
                    sms: false,
                    task: false,
                    note: false,
                    readAll: false,
                    updateAll: false
                  }
                }
              ]
            }
          }
        );

        const newWrapper = mount(
          <MemoryRouter initialEntries={['/setup/goals/create']}>
            <Provider store={storeWithGoalCreatePermissions}>
              <Routes/>
            </Provider>
          </MemoryRouter>
        );

        expect(newWrapper.find(GoalCreate).length).toBe(1);
      });
    });

    describe('Goal Edit', () => {
      it('should not render Goal Edit component when user without goal update permission route to "/setup/goals/edit/123"', () => {
        const newWrapper = mount(
          <MemoryRouter initialEntries={['/setup/goals/edit/123']}>
            <Provider store={store}>
              <Routes/>
            </Provider>
          </MemoryRouter>
        );

        expect(newWrapper.find(GoalEdit).length).toBe(0);
      });

      it('should render Goal Edit component when user with goal edit permission route to "/setup/goals/edit/123"', () => {
        const storeWithGoalEditPermissions = mockStore(
          {
            ...storeData,
            appData: {
              ...storeData.appData,
              profilePermissions: [
                ...profilePermissions,
                {
                  id: 12,
                  name: 'goal',
                  description: 'has permission to company resource',
                  limits: 50,
                  units: 'count',
                  action: {
                    read: true,
                    write: true,
                    update: true,
                    delete: false,
                    email: false,
                    call: false,
                    sms: false,
                    task: false,
                    note: false,
                    readAll: false,
                    updateAll: false
                  }
                }
              ]
            }
          }
        );

        const newWrapper = mount(
          <MemoryRouter initialEntries={['/setup/goals/edit/123']}>
            <Provider store={storeWithGoalEditPermissions}>
              <Routes/>
            </Provider>
          </MemoryRouter>
        );

        expect(newWrapper.find(GoalEdit).length).toBe(1);
      });
    });
  });

  describe('Contact', () => {
    it('should not render ContactList component when user without permission route to "/contacts/list"', () => {
      const newWrapper = mount(
        <MemoryRouter initialEntries={['/sales/contacts/list']}>
          <Provider store={store}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find('ContactList').length).toBe(0);
    });

    it('should render ContactList component when user with permission route to "/contacts/list"', () => {

      const storeWithContactPermissions = mockStore(
        {
          ...storeData,
          appData: {
            ...storeData.appData,
            profilePermissions: [
              ...profilePermissions,
              {
                id: 12,
                name: 'contact',
                description: 'has permission to contact resource',
                limits: 50,
                units: 'count',
                action: {
                  read: true,
                  write: true,
                  update: true,
                  delete: true,
                  email: false,
                  call: false,
                  sms: false,
                  task: true,
                  note: true,
                  readAll: true,
                  updateAll: true
                }
              }
            ]
          }
        }
      );

      const newWrapper = mount(
        <MemoryRouter initialEntries={['/sales/contacts/list']}>
          <Provider store={storeWithContactPermissions}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find('ContactList').length).toBe(1);
    });

    it('should render Contact Details component when user with permission route to "/contacts/details/5"', () => {

      const storeWithContactPermissions = mockStore(
        {
          ...storeData,
          appData: {
            ...storeData.appData,
            profilePermissions: [
              ...profilePermissions,
              {
                id: 12,
                name: 'contact',
                description: 'has permission to contact resource',
                limits: 50,
                units: 'count',
                action: {
                  read: true,
                  write: true,
                  update: true,
                  delete: true,
                  email: false,
                  call: false,
                  sms: false,
                  task: true,
                  note: true,
                  readAll: true,
                  updateAll: true
                }
              }
            ]
          }
        }
      );

      const newWrapper = mount(
        <MemoryRouter initialEntries={['/sales/contacts/details/5']}>
          <Provider store={storeWithContactPermissions}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find('ContactDetails').length).toBe(1);
    });
  });

  describe('Lead Capture Forms', () => {
    it('should not render LeadCaptureFormsListLayout component when user does not have permission for route "/setup/integrations/lead-capture-forms/list', () => {
      const newWrapper = mount(
        <MemoryRouter initialEntries={['/setup/integrations/lead-capture-forms/list']}>
          <Provider store={store}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find('LeadCaptureFormsListLayout').length).toBe(0);
    });

    it('should render LeadCaptureFormsListLayout component when user has permission for route "/setup/integrations/lead-capture-forms/list', () => {
      jest.spyOn(Storage.prototype, 'getItem').mockReturnValue('{"7239.lead-capture-forms.columnOrder": ""}');
      const storeWithLeadCaptureReadPermissions = mockStore(
        {
          ...storeData,
          appData: {
            ...storeData.appData,
            profilePermissions: [
              ...profilePermissions,
              {
                id: 24,
                name: 'lead-capture-forms',
                description: 'has access to Lead Capture Forms',
                limits: 50,
                units: 'count',
                action: {
                  read: true,
                  write: false,
                  update: false,
                  delete: false,
                  email: false,
                  call: false,
                  sms: false,
                  task: false,
                  note: false,
                  readAll: false,
                  updateAll: false
                }
              }
            ]
          }
        }
      );

      const newWrapper = mount(
        <MemoryRouter initialEntries={['/setup/integrations/lead-capture-forms/list']}>
          <Provider store={storeWithLeadCaptureReadPermissions}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find('LeadCaptureFormsListLayout').length).toBe(1);
    });

    it('should not render LeadCaptureFormBuilderLayout component when user does not have permission for route "/setup/integrations/lead-capture-forms/create', () => {
      const newWrapper = mount(
        <MemoryRouter initialEntries={['/setup/integrations/lead-capture-forms/create']}>
          <Provider store={store}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find('LeadCaptureFormCreate').length).toBe(0);
    });

    it('should render LeadCaptureFormBuilderLayout component when user has permission for route "/setup/integrations/lead-capture-forms/create', () => {
      jest.spyOn(Storage.prototype, 'getItem').mockReturnValue('{\"leads\":{\"create\":{\"standard\":{\"layoutItems\":[]}}}}');
      const storeWithLeadCaptureReadPermissions = mockStore(
        {
          ...storeData,
          appData: {
            ...storeData.appData,
            profilePermissions: [
              ...profilePermissions,
              {
                id: 24,
                name: 'lead-capture-forms',
                description: 'has access to Lead Capture Forms',
                limits: 50,
                units: 'count',
                action: {
                  read: true,
                  write: true,
                  update: false,
                  delete: false,
                  email: false,
                  call: false,
                  sms: false,
                  task: false,
                  note: false,
                  readAll: false,
                  updateAll: false
                }
              }
            ]
          }
        }
      );

      const newWrapper = mount(
        <MemoryRouter initialEntries={['/setup/integrations/lead-capture-forms/create']}>
          <Provider store={storeWithLeadCaptureReadPermissions}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find('LeadCaptureFormCreate').length).toBe(1);
    });
  });

  describe('Meeting List', () => {
    it('should render Meeting List component when user route to "/sales/meetings/list"', () => {
      const newWrapper = mount(
        <MemoryRouter initialEntries={['/sales/meetings/list']}>
          <Provider store={store}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find('MeetingListLayout').length).toBe(1);
    });
  });

  describe('Email List', () => {
    it('should render Email List component when user route to "/sales/emails/list"', () => {
      const storeWithEmailReadPermissions = mockStore(
        {
          ...storeData,
          appData: {
            ...storeData.appData,
            profilePermissions: [
              ...profilePermissions,
              {
                id: 24,
                name: 'email',
                description: 'has access to Email',
                limits: 50,
                units: 'count',
                action: {
                  read: true,
                  write: true,
                  update: false,
                  delete: false,
                  email: false,
                  call: false,
                  sms: false,
                  task: false,
                  note: false,
                  readAll: false,
                  updateAll: false
                }
              }
            ]
          }
        }
      );

      const newWrapper = mount(
        <MemoryRouter initialEntries={['/sales/emails/list']}>
          <Provider store={storeWithEmailReadPermissions}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find('EmailListLayout').length).toBe(1);
    });
  });

  describe('WhatsApp List', () => {
    it('should render WhatsApp List component when user route to "/sales/whatsapp/list"', () => {
      const storeWithWhatsAppReadAllPermission = mockStore(
        {
          ...storeData,
          appData: {
            ...storeData.appData,
            profilePermissions: [
              ...profilePermissions,
              {
                id: 13,
                name: 'sms',
                description: 'has permission to security resource',
                limits: 50,
                units: 'count',
                action: {
                  read: true,
                  write: false,
                  update: false,
                  delete: false,
                  readAll: true,
                  updateAll: true
                }
              }
            ]
          }
        }
      );

      const newWrapper = mount(
        <MemoryRouter initialEntries={['/sales/whatsapp/list']}>
          <Provider store={storeWithWhatsAppReadAllPermission}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find('WhatsAppListLayout').length).toBe(1);
    });
  });

  describe('Workflow', () => {
    const storeWithoutworkflowPermissions = mockStore(
      {
        ...storeData,
        appData: {
          ...storeData.appData,
          profilePermissions: [
            profilePermissions[0],
            {
              id: 12,
              name: 'workflow',
              description: 'has permission to workflow resource',
              limits: 50,
              units: 'count',
              action: {
                read: false,
                write: false,
                update: false,
                delete: false,
                email: false,
                call: false,
                sms: false,
                task: false,
                note: false,
                readAll: false,
                updateAll: false
              }
            }
          ]
        }
      }
    );
    const storeWithworkflowPermissions = mockStore(
      {
        ...storeData,
        appData: {
          ...storeData.appData,
          profilePermissions: [
            profilePermissions[1],
            {
              id: 12,
              name: 'workflow',
              description: 'has permission to workflow resource',
              limits: 50,
              units: 'count',
              action: {
                read: true,
                write: true,
                update: true,
                delete: false,
                email: false,
                call: false,
                sms: false,
                task: false,
                note: false,
                readAll: true,
                updateAll: false
              }
            }
          ]
        },
        forms: formInitialState
      }
    );

    describe('List', () => {
      it('should not render WorkflowsList component when user without permission route to "/setup/workflow-automation/workflows/list"', () => {
        const newWrapper = mount(
          <MemoryRouter initialEntries={['/setup/workflow-automation/workflows/list']}>
            <Provider store={storeWithoutworkflowPermissions}>
              <Routes/>
            </Provider>
          </MemoryRouter>
        );

        expect(newWrapper.find('WorkflowsListLayout').length).toBe(0);
      });

      it('should render WorkflowsList component when user with permission route to "/setup/workflow-automation/workflows/list"', () => {
        const newWrapper = mount(
          <MemoryRouter initialEntries={['/setup/workflow-automation/workflows/list']}>
            <Provider store={storeWithworkflowPermissions}>
              <Routes/>
            </Provider>
          </MemoryRouter>
        );

        expect(newWrapper.find('WorkflowsListLayout').length).toBe(1);
      });
    });

    describe('Create', () => {
      it('should not render WorkflowCreate component when user without permission route to "/setup/workflow-automation/workflows/create"', () => {
        const newWrapper = mount(
          <MemoryRouter initialEntries={['/setup/workflow-automation/workflows/create']}>
            <Provider store={storeWithoutworkflowPermissions}>
              <Routes/>
            </Provider>
          </MemoryRouter>
        );

        expect(newWrapper.find('WorkflowCreate').length).toBe(0);
      });

      it('should render WorkflowsCreate component when user with permission route to "/setup/workflow-automation/workflows/create"', () => {
        const newWrapper = mount(
          <MemoryRouter initialEntries={['/setup/workflow-automation/workflows/create']}>
            <Provider store={storeWithworkflowPermissions}>
              <Routes/>
            </Provider>
          </MemoryRouter>
        );

        expect(newWrapper.find('WorkflowCreate').length).toBe(1);
      });
    });

    describe('View', () => {
      it('should not render WorkflowView component when user without permission route to "/setup/workflow-automation/workflows/details/1"', () => {
        const newWrapper = mount(
          <MemoryRouter initialEntries={['/setup/workflow-automation/workflows/details/1']}>
            <Provider store={storeWithoutworkflowPermissions}>
              <Routes/>
            </Provider>
          </MemoryRouter>
        );

        expect(newWrapper.find('WorkflowView').length).toBe(0);
      });

      it('should render WorkflowView component when user with permission route to "/setup/workflow-automation/workflows/details/1"', () => {
        const newWrapper = mount(
          <MemoryRouter initialEntries={['/setup/workflow-automation/workflows/details/1']}>
            <Provider store={storeWithworkflowPermissions}>
              <Routes/>
            </Provider>
          </MemoryRouter>
        );

        expect(newWrapper.find(WorkflowView).length).toBe(1);
      });
    });

    describe('Edit', () => {
      it('should not render WorkflowEdit component when user without permission route to "/setup/workflow-automation/workflows/edit/1"', () => {
        const newWrapper = mount(
          <MemoryRouter initialEntries={['/setup/workflow-automation/workflows/edit/1']}>
            <Provider store={storeWithoutworkflowPermissions}>
              <Routes/>
            </Provider>
          </MemoryRouter>
        );

        expect(newWrapper.find('WorkflowEdit').length).toBe(0);
      });

      it('should render WorkflowEdit component when user with permission route to "/setup/workflow-automation/workflows/edit/1"', () => {
        const newWrapper = mount(
          <MemoryRouter initialEntries={['/setup/workflow-automation/workflows/edit/1']}>
            <Provider store={storeWithworkflowPermissions}>
              <Routes/>
            </Provider>
          </MemoryRouter>
        );

        expect(newWrapper.find('WorkflowEdit').length).toBe(1);
      });
    });

    describe('Template', () => {
      it('should not render WorkflowTemplates component when user without permission route to "/setup/workflow-automation/workflows/templates/all"', () => {
        const newWrapper = mount(
          <MemoryRouter initialEntries={['/setup/workflow-automation/workflows/templates/all']}>
            <Provider store={storeWithoutworkflowPermissions}>
              <Routes/>
            </Provider>
          </MemoryRouter>
        );

        expect(newWrapper.find(WorkflowTemplates).length).toBe(0);
      });

      it('should render WorkflowTemplates component when user with permission route to "/setup/workflow-automation/workflows/templates/deals"', () => {
        const newWrapper = mount(
          <MemoryRouter initialEntries={['/setup/workflow-automation/workflows/templates/deals']}>
            <Provider store={storeWithworkflowPermissions}>
              <Routes/>
            </Provider>
          </MemoryRouter>
        );

        expect(newWrapper.find(WorkflowTemplates).length).toBe(1);
      });
    });

    describe('Action Logs', () => {
      it('should not render WorkflowActionLogsList component when user without permission route to "/setup/workflow-automation/execution-logs/list"', () => {
        const newWrapper = mount(
          <MemoryRouter initialEntries={['/setup/workflow-automation/workflows/execution-logs/list']}>
            <Provider store={storeWithoutworkflowPermissions}>
              <Routes/>
            </Provider>
          </MemoryRouter>
        );

        expect(newWrapper.find(WorkflowActionLogsListLayout).length).toBe(0);
      });

      it('should render WorkflowActionLogsListLayout component when user with permission route to "/setup/workflow-automation/execution-logs/list"', () => {
        const newWrapper = mount(
          <MemoryRouter initialEntries={['/setup/workflow-automation/execution-logs/list']}>
            <Provider store={storeWithworkflowPermissions}>
              <Routes/>
            </Provider>
          </MemoryRouter>
        );

        expect(newWrapper.find(WorkflowActionLogsListLayout).length).toBe(1);
      });
    });
  });

  describe('Scoring Rules', () => {
    const storeWithoutScoringRulesPermissions = mockStore(
      {
        ...storeData,
        appData: {
          ...storeData.appData,
          profilePermissions: [
            profilePermissions[0],
            {
              id: 12,
              name: 'scoringRules',
              description: 'has permission to scoringRules resource',
              limits: 50,
              units: 'count',
              action: {
                read: false,
                write: false,
                update: false,
                delete: false,
                email: false,
                call: false,
                sms: false,
                task: false,
                note: false,
                readAll: false,
                updateAll: false
              }
            }
          ]
        }
      }
    );
    const storeWithScoringRulesPermissions = mockStore(
      {
        ...storeData,
        appData: {
          ...storeData.appData,
          profilePermissions: [
            profilePermissions[1],
            {
              id: 12,
              name: 'scoringRules',
              description: 'has permission to scoringRules resource',
              limits: 50,
              units: 'count',
              action: {
                read: true,
                write: true,
                update: true,
                delete: false,
                email: false,
                call: false,
                sms: false,
                task: false,
                note: false,
                readAll: true,
                updateAll: false
              }
            }
          ]
        },
        forms: formInitialState
      }
    );

    describe('List', () => {
      it('should not render ScoringRulesListLayout component when user without permission route to "/setup/automation/scoring-rules/leads/list"', () => {
        const newWrapper = mount(
          <MemoryRouter initialEntries={['/setup/automation/scoring-rules/leads/list']}>
            <Provider store={storeWithoutScoringRulesPermissions}>
              <Routes/>
            </Provider>
          </MemoryRouter>
        );

        expect(newWrapper.find(ScoringRulesListLayout).length).toBe(0);
      });

      it('should render ScoringRulesListLayout component when user with permission route to "/setup/automation/scoring-rules/leads/list"', () => {
        const newWrapper = mount(
          <MemoryRouter initialEntries={['/setup/automation/scoring-rules/leads/list']}>
            <Provider store={storeWithScoringRulesPermissions}>
              <Routes/>
            </Provider>
          </MemoryRouter>
        );

        expect(newWrapper.find(ScoringRulesListLayout).length).toBe(1);
      });
    });

    describe('Create', () => {
      it('should not render ScoringRuleStepTwo component when user without permission route to "/setup/automation/scoring-rules/leads/create"', () => {
        const newWrapper = mount(
          <MemoryRouter initialEntries={['/setup/automation/scoring-rules/leads/create']}>
            <Provider store={storeWithoutScoringRulesPermissions}>
              <Routes/>
            </Provider>
          </MemoryRouter>
        );

        expect(newWrapper.find('ScoringRuleStepTwo').length).toBe(0);
      });

      it('should render ScoringRuleStepTwo component when user with permission route to "/setup/automation/scoring-rules/leads/create"', () => {
        const newWrapper = mount(
          <MemoryRouter initialEntries={['/setup/automation/scoring-rules/leads/create']}>
            <Provider store={storeWithScoringRulesPermissions}>
              <Routes/>
            </Provider>
          </MemoryRouter>
        );

        expect(newWrapper.find('ScoringRuleStepTwo').length).toBe(1);
      });
    });

    describe('Edit', () => {
      it('should not render ScoringRuleStepTwo component when user without permission route to "/setup/automation/scoring-rules/leads/edit/123"', () => {
        const newWrapper = mount(
          <MemoryRouter initialEntries={['/setup/automation/scoring-rules/leads/edit/123']}>
            <Provider store={storeWithoutScoringRulesPermissions}>
              <Routes/>
            </Provider>
          </MemoryRouter>
        );

        expect(newWrapper.find('ScoringRuleStepTwo').length).toBe(0);
      });

      it('should render ScoringRuleStepTwo component when user with permission route to "/setup/automation/scoring-rules/leads/edit/123"', () => {
        const newWrapper = mount(
          <MemoryRouter initialEntries={['/setup/automation/scoring-rules/leads/edit/123']}>
            <Provider store={storeWithScoringRulesPermissions}>
              <Routes/>
            </Provider>
          </MemoryRouter>
        );

        expect(newWrapper.find('ScoringRuleStepTwo').length).toBe(1);
      });
    });
  });

  describe('Billing', () => {
    describe('Usage Statistics component', () => {
      const storeWithTenantPermission = mockStore(
        {
          ...storeData,
          appData: {
            ...storeData.appData,
            profilePermissions: [
              ...storeData.appData.profilePermissions,
              tenantRosourcePermission
            ]
          },
          forms: formInitialState
        }
      );

      it('should render, when user route to "/setup/billing/usage-statistics"', () => {
        const newWrapper = mount(
          <MemoryRouter initialEntries={['/setup/billing/usage-statistics']}>
            <Provider store={storeWithTenantPermission}>
              <Routes/>
            </Provider>
          </MemoryRouter>
        );

        expect(newWrapper.find('UsageStatistics').length).toBe(1);
      });
    });

    describe('Subscription Details component', () => {
      const storeWithTenantPermission = mockStore(
        {
          ...storeData,
          appData: {
            ...storeData.appData,
            profilePermissions: [
              ...storeData.appData.profilePermissions,
              tenantRosourcePermission
            ]
          },
          forms: formInitialState
        }
      );

      it('should render, when user route to "/setup/billing/subscription-details"', () => {
        const newWrapper = mount(
          <MemoryRouter initialEntries={['/setup/billing/subscription-details']}>
            <Provider store={storeWithTenantPermission}>
              <Routes/>
            </Provider>
          </MemoryRouter>
        );

        expect(newWrapper.find('SubscriptionDetails').length).toBe(1);
      });
    });
  });

  describe('Email template', () => {
    it('should render email template list layout component when user route to "/setup/communication/email-templates"', () => {
      const updatedStore = mockStore(
        {
          ...storeData,
          emailTemplates: templateInitialData,
          forms: formInitialState,
          appData: {
            ...storeData.appData,
            profilePermissions: [
              ...profilePermissions,
              {
                id: 13,
                name: 'email_template',
                description: 'has permission to email templates resource',
                limits: 50,
                units: 'count',
                action: {
                  read: true,
                  write: false,
                  update: false,
                  delete: false,
                  email: false,
                  call: false,
                  sms: false,
                  task: false,
                  note: false,
                  readAll: false,
                  updateAll: false
                }
              }
            ]
          }
        }
      );

      const newWrapper = mount(
        <MemoryRouter initialEntries={['/setup/communication/email-templates/list']}>
          <Provider store={updatedStore}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find(EmailTemplatesLayout).length).toBe(1);
    });

    it('should render email template create layout component when user route to "/setup/communication/email-templates/create"', () => {
      const updatedStore = mockStore(
        {
          ...storeData,
          forms: formInitialState,
          appData: {
            ...storeData.appData,
            profilePermissions: [
              ...profilePermissions,
              {
                id: 13,
                name: 'email_template',
                description: 'has permission to email templates resource',
                limits: 50,
                units: 'count',
                action: {
                  read: true,
                  write: true,
                  update: false,
                  delete: false,
                  email: false,
                  call: false,
                  sms: false,
                  task: false,
                  note: false,
                  readAll: false,
                  updateAll: false
                }
              }
            ]
          }
        }
      );
      const newWrapper = mount(
        <MemoryRouter initialEntries={['/setup/communication/email-templates/create']}>
          <Provider store={updatedStore}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find(CreateEmailTemplate).length).toBe(1);
    });

    it('should render email template edit layout component when user route to "/setup/communication/email-templates/edit/:templateId"', () => {
      const updatedStore = mockStore(
        {
          ...storeData,
          forms: formInitialState,
          appData: {
            ...storeData.appData,
            profilePermissions: [
              ...profilePermissions,
              {
                id: 13,
                name: 'email_template',
                description: 'has permission to email templates resource',
                limits: 50,
                units: 'count',
                action: {
                  read: true,
                  write: false,
                  update: true,
                  delete: false,
                  email: false,
                  call: false,
                  sms: false,
                  task: false,
                  note: false,
                  readAll: false,
                  updateAll: false
                }
              }
            ]
          }
        }
      );
      const newWrapper = mount(
        <MemoryRouter initialEntries={[`/setup/communication/email-templates/edit/${dummyEmailTemplates.content[0]}`]}>
          <Provider store={updatedStore}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find(EditEmailTemplate).length).toBe(1);
    });
  });

  describe('WhatsApp Business', () => {
    const storeWithWhatsAppPermission = mockStore(
      {
        ...storeData,
        appData: {
          ...storeData.appData,
          profilePermissions: [
            ...profilePermissions,
            {
              id: 13,
              name: 'whatsappBusiness',
              description: 'has permission to security resource',
              limits: 50,
              units: 'count',
              action: {
                read: false,
                write: false,
                update: false,
                delete: false,
                email: false,
                call: false,
                sms: false,
                task: false,
                note: false,
                readAll: true,
                updateAll: true
              }
            },
            {
              id: 14,
              name: 'whatsappTemplate',
              description: 'has permission to whatsapp templates resource',
              limits: 50,
              units: 'count',
              action: {
                read: true,
                write: true,
                update: true,
                delete: false,
                email: false,
                call: false,
                sms: false,
                task: false,
                note: false,
                readAll: true,
                updateAll: true
              }
            }
          ]
        }
      }
    );

    it('should render whatsapp settings layout component when user route to "/setup/whatsapp-business/account-details"', () => {
      const newWrapper = mount(
        <MemoryRouter initialEntries={['/setup/whatsapp-business/account-details']}>
          <Provider store={storeWithWhatsAppPermission}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find(WhatsAppSettingsLayout).length).toBe(1);
    });

    it('should not render whatsapp settings layout component when user dont have readAll permission on whatsappBusiness route to "/setup/whatsapp-business/account-details"', () => {

      const newWrapper = mount(
        <MemoryRouter initialEntries={['/setup/whatsapp-business/account-details']}>
          <Provider store={store}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find(WhatsAppSettingsLayout).length).toBe(0);
    });

    it('should render whatsapp add agents component when user route to "/setup/whatsapp-business/account-details/1/add-agents/lead"', () => {
      const newWrapper = mount(
        <MemoryRouter initialEntries={['/setup/whatsapp-business/account-details/1/add-agents/lead']}>
          <Provider store={storeWithWhatsAppPermission}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find(AddAgents).length).toBe(1);
    });

    it('should render whatsapp templates listing page when user route to "/setup/whatsapp-business/templates/list"', () => {
      const newWrapper = mount(
        <MemoryRouter initialEntries={['/setup/whatsapp-business/templates/list']}>
          <Provider store={storeWithWhatsAppPermission}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find(WhatsAppTemplatesList).length).toBe(1);
    });

    it('should not render whatsapp templates listing component when user does not have read permission on whatsappTemplates route to "/setup/whatsapp-business/templates/list"', () => {

      const newWrapper = mount(
        <MemoryRouter initialEntries={['/setup/whatsapp-business/templates/list']}>
          <Provider store={store}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find(WhatsAppTemplatesList).length).toBe(0);
    });

    it('should render create whatsapp template page when user route to "/setup/whatsapp-business/templates/create"', () => {
      const newWrapper = mount(
        <MemoryRouter initialEntries={['/setup/whatsapp-business/templates/create']}>
          <Provider store={storeWithWhatsAppPermission}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find(CreateWhatsAppTemplate).length).toBe(1);
    });

    it('should route to /setup/whatsapp-business/track-usage, if user has required whatsapp business permission', () => {
      const newWrapper = mount(
        <MemoryRouter initialEntries={['/setup/whatsapp-business/track-usage']}>
          <Provider store={storeWithWhatsAppPermission}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find(WhatsAppBillingInformation).length).toBe(1);
    });
  });

  describe('ApiKeys', () => {
    const storeWithTenantPermission = mockStore(
      {
        ...storeData,
        appData: {
          ...storeData.appData,
          profilePermissions: [
            ...profilePermissions,
            tenantRosourcePermission
          ]
        }
      }
    );

    it('should render, when user routes to "/setup/integrations/api-keys/list"', () => {
      const newWrapper = mount(
        <MemoryRouter initialEntries={['/setup/integrations/api-keys/list']}>
          <Provider store={storeWithTenantPermission}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find('ApiKeysListLayout').length).toBe(1);
    });

    it('should render, when user routes to "/setup/integrations/api-usage"', () => {
      const newWrapper = mount(
        <MemoryRouter initialEntries={['/setup/integrations/api-usage']}>
          <Provider store={storeWithTenantPermission}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find(ApiUsage).length).toBe(1);
    });
  });
  describe('Email Oauth', () => {
    it('should render EmailLoginPopUp, when user routes to "/setup/communication/gmail-oauth-callback"', () => {
      const newWrapper = mount(
          <MemoryRouter initialEntries={['/setup/communication/gmail-oauth-callback']}>
            <Provider store={store}>
              <Routes/>
            </Provider>
          </MemoryRouter>
      );

      expect(newWrapper.find('EmailLoginPopUp').length).toBe(1);
    });

    it('should render EmailLoginPopUp, when user routes to "/setup/communication/outlook-oauth-callback"', () => {
      const newWrapper = mount(
          <MemoryRouter initialEntries={['/setup/communication/outlook-oauth-callback']}>
            <Provider store={store}>
              <Routes/>
            </Provider>
          </MemoryRouter>
      );

      expect(newWrapper.find('EmailLoginPopUp').length).toBe(1);
    });
  });

  describe('Marketplace', () => {
    it('should render all apps component, when user routes to "/marketplace"', () => {
      const newWrapper = mount(
        <MemoryRouter initialEntries={['/marketplace']}>
          <Provider store={store}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find('AllApps').length).toBe(1);
    });

    it('should render app details component, when user routes to "/marketplace/all-apps/app-details/123"', () => {
      const newWrapper = mount(
        <MemoryRouter initialEntries={['/marketplace/all-apps/app-details/123']}>
          <Provider store={store}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );
      expect(newWrapper.find('AppDetailsComponent').length).toBe(1);
    });

    it('should render installed apps component, when user routes to "/marketplace/installed-apps/list"', () => {
      const newWrapper = mount(
        <MemoryRouter initialEntries={['/marketplace/installed-apps/list']}>
          <Provider store={store}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );
      expect(newWrapper.find('InstalledAppsListComponent').length).toBe(1);
    });

    it('should render installed apps component, when user routes to "/marketplace/installed-apps/:appId/page"', () => {
      const newWrapper = mount(
        <MemoryRouter initialEntries={['/marketplace/installed-apps/d172b5a4-9372-49a3-888b-d3b78327cde4/customers/edit']}>
          <Provider store={store}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );
      expect(newWrapper.find('InstalledAppsListComponent').length).toBe(1);
    });

    it('should render manage apps component, when user routes to "/marketplace/manage-my-apps/list"', () => {
      const storeWithTenantPermission = mockStore(
        {
          ...storeData,
          appData: {
            ...storeData.appData,
            profilePermissions: [
              ...profilePermissions,
              tenantRosourcePermission
            ]
          }
        }
      );

      const newWrapper = mount(
        <MemoryRouter initialEntries={['/marketplace/manage-my-apps/list']}>
          <Provider store={storeWithTenantPermission}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find('ManageAppsList').length).toBe(1);
    });

    it('should render Create app component, when user routes to "/marketplace/manage-my-apps/create"', () => {
      const updatedStore = mockStore(
        {
          ...storeData,
          forms: formInitialState,
          appData: {
            ...storeData.appData,
            profilePermissions: [
              ...profilePermissions,
              tenantRosourcePermission
            ]
          }
        }
      );

      const newWrapper = mount(
        <MemoryRouter initialEntries={['/marketplace/manage-my-apps/create']}>
          <Provider store={updatedStore}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find('CreateApp').length).toBe(1);
    });

    it('should render Edit app component, when user routes to "/marketplace/manage-my-apps/edit/1323"', () => {
      const updatedStore = mockStore(
        {
          ...storeData,
          forms: formInitialState,
          appData: {
            ...storeData.appData,
            profilePermissions: [
              ...profilePermissions,
              tenantRosourcePermission
            ]
          }
        }
      );

      const newWrapper = mount(
        <MemoryRouter initialEntries={['/marketplace/manage-my-apps/edit/1323']}>
          <Provider store={updatedStore}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find('EditApp').length).toBe(1);
    });
  });

  describe('Data Management', () => {
    it('should render Bulk Jobs component, when user routes to "/setup/data-management/bulk-jobs/list"', () => {
      jest.spyOn(Storage.prototype, 'getItem').mockReturnValue('{"7239.bulk-jobs.columnOrder": ""}');
      const newWrapper = mount(
        <MemoryRouter initialEntries={['/setup/data-management/bulk-jobs/list']}>
          <Provider store={store}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find(BulkJobsListComponent).length).toBe(1);
    });

    it('should render CreateBulkJob component, when user routes to "/setup/data-management/bulk-jobs/create" with note delete permission', () => {
      const storeWithNotePermission = mockStore(
        {
          ...storeData,
          appData: {
            ...storeData.appData,
            profilePermissions: [
              ...profilePermissions,
              {
                id: 13,
                name: 'note',
                description: 'has permission to note resource',
                limits: 50,
                units: 'count',
                action: {
                  read: true,
                  write: true,
                  update: true,
                  delete: true,
                  email: false,
                  call: false,
                  sms: false,
                  task: false,
                  note: false,
                  readAll: false,
                  updateAll: false
                }
              }
            ]
          }
        }
      );

      const newWrapper = mount(
        <MemoryRouter initialEntries={['/setup/data-management/bulk-jobs/create']}>
          <Provider store={storeWithNotePermission}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find(CreateBulkJob).length).toBe(1);
    });

    it('should render Export jobs listing component, when user routes to "/setup/data-management/exports/list"', () => {
      jest.spyOn(Storage.prototype, 'getItem').mockReturnValue('{"7239.exports.columnOrder": ""}');

      const newWrapper = mount(
        <MemoryRouter initialEntries={['/setup/data-management/exports/list']}>
          <Provider store={store}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find(ExportListComponent).length).toBe(1);
    });

    it('should render Create Export job component, when user routes to "/setup/data-management/exports/create"', () => {
      const newWrapper = mount(
        <MemoryRouter initialEntries={['/setup/data-management/exports/create']}>
          <Provider store={store}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find(CreateExportJob).length).toBe(1);
    });

    describe('imports', () => {
      it('should render import listing if user routes to "/setup/data-management/imports/list" and has contact create permission', () => {
        const storeWithContactCreatePermission = mockStore(
          {
            ...storeData,
            appData: {
              ...storeData.appData,
              profilePermissions: [
                profilePermissions[0],
                {
                  id: 12,
                  name: 'contact',
                  description: 'has permission to contact resource',
                  limits: 50,
                  units: 'count',
                  action: {
                    read: true,
                    write: true,
                    update: true,
                    delete: false,
                    email: false,
                    call: false,
                    sms: false,
                    task: false,
                    note: false,
                    readAll: false,
                    updateAll: false
                  }
                }
              ]
            },
            forms: formInitialState
          }
        );

        const newWrapper = mount(
          <MemoryRouter initialEntries={['/setup/data-management/imports/list']}>
            <Provider store={storeWithContactCreatePermission}>
              <Routes/>
            </Provider>
          </MemoryRouter>
        );

        expect(newWrapper.find(DataManagement).length).toEqual(1);
      });

      it('should NOT render import listing if user routes to "/setup/data-management/imports/list" and has contact create or lead create permission', () => {
        const newWrapper = mount(
          <MemoryRouter initialEntries={['/setup/data-management/imports/list']}>
            <Provider store={store}>
              <Routes/>
            </Provider>
          </MemoryRouter>
        );

        expect(newWrapper.find(DataManagement).length).toEqual(0);
      });

      it('should render import listing if user routes to "/setup/data-management/imports/list" and has company create permission', () => {
        const storeWithContactCreatePermission = mockStore(
          {
            ...storeData,
            appData: {
              ...storeData.appData,
              profilePermissions: [
                profilePermissions[0],
                {
                  id: 12,
                  name: 'company',
                  description: 'has permission to company resource',
                  limits: 50,
                  units: 'count',
                  action: {
                    read: true,
                    write: true,
                    update: true,
                    delete: false,
                    email: false,
                    call: false,
                    sms: false,
                    task: false,
                    note: false,
                    readAll: false,
                    updateAll: false
                  }
                }
              ]
            },
            forms: formInitialState
          }
      );

        const newWrapper = mount(
        <MemoryRouter initialEntries={['/setup/data-management/imports/list']}>
          <Provider store={storeWithContactCreatePermission}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

        expect(newWrapper.find(DataManagement).length).toEqual(1);
      });

      it('should NOT render import listing if user routes to "/setup/data-management/imports/list" and does not have company create or lead create permission', () => {
        const newWrapper = mount(
        <MemoryRouter initialEntries={['/setup/data-management/imports/list']}>
          <Provider store={store}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

        expect(newWrapper.find(DataManagement).length).toEqual(0);
      });
    });

  });

  describe('Webhooks', () => {
    const storeWithoutWebhookPermissions = mockStore(
      {
        ...storeData,
        appData: {
          ...storeData.appData,
          profilePermissions: [
            profilePermissions[0],
            {
              id: 12,
              name: 'webhook',
              description: 'has permission to webhook resource',
              limits: 50,
              units: 'count',
              action: {
                read: false,
                write: false,
                update: false,
                delete: false,
                email: false,
                call: false,
                sms: false,
                task: false,
                note: false,
                readAll: false,
                updateAll: false
              }
            }
          ]
        }
      }
    );
    const storeWithWebhookPermissions = mockStore(
      {
        ...storeData,
        appData: {
          ...storeData.appData,
          profilePermissions: [
            profilePermissions[0],
            {
              id: 12,
              name: 'webhook',
              description: 'has permission to webhook resource',
              limits: 50,
              units: 'count',
              action: {
                read: true,
                write: true,
                update: true,
                delete: false,
                email: false,
                call: false,
                sms: false,
                task: false,
                note: false,
                readAll: false,
                updateAll: false
              }
            }
          ]
        },
        forms: formInitialState
      }
    );

    describe('List', () => {
      it('should not render WebhooksList component when user without permission route to "/setup/automation/webhooks/list"', () => {
        const newWrapper = mount(
          <MemoryRouter initialEntries={['/setup/integrations/webhooks/list']}>
            <Provider store={storeWithoutWebhookPermissions}>
              <Routes/>
            </Provider>
          </MemoryRouter>
        );

        expect(newWrapper.find(WebhooksList).length).toBe(0);
      });

      it('should render WebhooksList component when user with permission route to "/setup/automation/webhooks/list"', () => {
        jest.spyOn(Storage.prototype, 'getItem').mockReturnValue('{"7239.webhook-history.columnOrder": ""}');

        const newWrapper = mount(
          <MemoryRouter initialEntries={['/setup/integrations/webhooks/list']}>
            <Provider store={storeWithWebhookPermissions}>
              <Routes/>
            </Provider>
          </MemoryRouter>
        );

        expect(newWrapper.find(WebhooksList).length).toBe(1);
      });
    });

    it('should render WebhookHistoryList component if user has read on webhooks', () => {
      jest.spyOn(Storage.prototype, 'getItem').mockReturnValue('{"7239.webhook-history.columnOrder": ""}');

      const newWrapper = mount(
        <MemoryRouter initialEntries={['/setup/integrations/webhook-history/list']}>
          <Provider store={storeWithWebhookPermissions}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find(WebhookHistoryList).length).toBe(1);
    });

    it('should NOT render WebhookHistoryList component if user does NOT have read on webhooks', () => {
      const newWrapper = mount(
        <MemoryRouter initialEntries={['/setup/integrations/webhook-history/list']}>
          <Provider store={storeWithoutWebhookPermissions}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find(WebhookHistoryList).length).toBe(0);
    });
  });

  describe('Quotations', () => {
    const storeWithoutQuotationPermissions = mockStore(
      {
        ...storeData,
        appData: {
          ...storeData.appData,
          profilePermissions: [
            profilePermissions[0],
            {
              id: 12,
              name: 'quotation',
              description: 'has permission to quotation resource',
              limits: 50,
              units: 'count',
              action: {
                read: false,
                write: false,
                update: false,
                delete: false,
                email: false,
                call: false,
                sms: false,
                task: false,
                note: false,
                readAll: false,
                updateAll: false
              }
            }
          ]
        }
      }
    );
    const storeWithQuotationPermissions = mockStore(
      {
        ...storeData,
        appData: {
          ...storeData.appData,
          profilePermissions: [
            profilePermissions[0],
            {
              id: 12,
              name: 'quotation',
              description: 'has permission to quotation resource',
              limits: 50,
              units: 'count',
              action: {
                read: true,
                write: true,
                update: true,
                delete: false,
                email: false,
                call: false,
                sms: false,
                task: false,
                note: false,
                readAll: false,
                updateAll: false
              }
            }
          ]
        },
        forms: formInitialState
      }
    );

    describe('List', () => {
      it('should not render QuotationList component when user without permission route to "/sales/quotations/list"', () => {
        const newWrapper = mount(
          <MemoryRouter initialEntries={['/sales/quotations/list']}>
            <Provider store={storeWithoutQuotationPermissions}>
              <Routes/>
            </Provider>
          </MemoryRouter>
        );

        expect(newWrapper.find(QuotationsListLayout).length).toBe(0);
      });

      it('should render QuotationList component when user with permission route to "/setup/quotations/list"', () => {
        const newWrapper = mount(
          <MemoryRouter initialEntries={['/sales/quotations/list']}>
            <Provider store={storeWithQuotationPermissions}>
              <Routes/>
            </Provider>
          </MemoryRouter>
        );

        expect(newWrapper.find(QuotationsListLayout).length).toBe(1);
      });
    });

    describe('details', () => {
      it('should render Quotation Details component', () => {
        const newWrapper = mount(
          <MemoryRouter initialEntries={['/sales/quotations/details/123']}>
            <Provider store={storeWithQuotationPermissions}>
              <Routes/>
            </Provider>
          </MemoryRouter>
        );

        expect(newWrapper.find(QuotationDetails).length).toBe(1);
      });
    });
  });

  describe('Calendar Settings', () => {
    const storeWithMeetingPermissions = mockStore(
      {
        ...storeData,
        appData: {
          ...storeData.appData,
          profilePermissions: [
            profilePermissions[0],
            {
              id: 12,
              name: 'deal',
              description: 'has permission to deal resource',
              limits: 50,
              units: 'count',
              action: {
                read: true,
                write: true,
                update: true,
                delete: true,
                email: false,
                call: false,
                sms: false,
                task: true,
                note: true,
                readAll: true,
                updateAll: true,
                meeting: true
              }
            }
          ]
        },
        forms: formInitialState
      }
    );

    it('should NOT render Calendar settings when meeting is NOT allowed on any entity', () => {
      const newWrapper = mount(
        <MemoryRouter initialEntries={['/setup/communication/calendar-settings']}>
          <Provider store={store}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find(CalendarSettings).length).toBe(0);
    });

    it('should render Calendar settings when meeting is allowed on any one entity', () => {
      const newWrapper = mount(
        <MemoryRouter initialEntries={['/setup/communication/calendar-settings']}>
          <Provider store={storeWithMeetingPermissions}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find(CalendarSettings).length).toBe(1);
    });

    it('should render EmailLoginPopUp, when user routes to "/setup/communication/google-calendar-oauth-callback"', () => {
      const newWrapper = mount(
          <MemoryRouter initialEntries={['/setup/communication/google-calendar-oauth-callback']}>
            <Provider store={store}>
              <Routes/>
            </Provider>
          </MemoryRouter>
      );

      expect(newWrapper.find('EmailLoginPopUp').length).toBe(1);
    });
  });

  describe('Layouts', () => {
    const storeWithoutLayoutPermissions = mockStore(
      {
        ...storeData,
        appData: {
          ...storeData.appData,
          profilePermissions: [
            profilePermissions[0],
            {
              id: 12,
              name: 'layout',
              description: 'has permission to layout resource',
              limits: 50,
              units: 'count',
              action: {
                read: false,
                write: false,
                update: false,
                delete: false,
                email: false,
                call: false,
                sms: false,
                task: false,
                note: false,
                readAll: false,
                updateAll: false
              }
            }
          ]
        }
      }
    );
    const storeWithLayoutPermissions = mockStore(
      {
        ...storeData,
        appData: {
          ...storeData.appData,
          profilePermissions: [
            profilePermissions[0],
            {
              id: 12,
              name: 'layout',
              description: 'has permission to layout resource',
              limits: 50,
              units: 'count',
              action: {
                read: true,
                write: true,
                update: false,
                delete: false,
                email: false,
                call: false,
                sms: false,
                task: false,
                note: false,
                readAll: true,
                updateAll: true
              }
            }
          ]
        },
        forms: formInitialState
      }
    );

    describe('List', () => {
      it('should not render layouts listing component when user without permission route to "/setup/layouts/leads/list"', () => {
        const newWrapper = mount(
          <MemoryRouter initialEntries={['/setup/layouts/leads/list']}>
            <Provider store={storeWithoutLayoutPermissions}>
              <Routes/>
            </Provider>
          </MemoryRouter>
        );

        expect(newWrapper.find(LayoutsList).length).toBe(0);
      });

      it('should render LayoutsList component when user with permission route to "/setup/layouts/leads/list"', () => {
        const newWrapper = mount(
          <MemoryRouter initialEntries={['/setup/layouts/leads/list']}>
            <Provider store={storeWithLayoutPermissions}>
              <Routes/>
            </Provider>
          </MemoryRouter>
        );

        expect(newWrapper.find(LayoutsList).length).toBe(1);
      });
    });

    describe('create', () => {
      it('should not render layouts create component when user without permission route to "/setup/layouts/leads/create"', () => {
        const newWrapper = mount(
          <MemoryRouter initialEntries={['/setup/layouts/leads/create']}>
            <Provider store={storeWithoutLayoutPermissions}>
              <Routes/>
            </Provider>
          </MemoryRouter>
        );

        expect(newWrapper.find(LayoutsCreate).length).toBe(0);
      });

      it('should render layouts create component when user with permission route to "/setup/layouts/leads/create"', () => {
        const newWrapper = mount(
          <MemoryRouter initialEntries={['/setup/layouts/leads/create']}>
            <Provider store={storeWithLayoutPermissions}>
              <Routes/>
            </Provider>
          </MemoryRouter>
        );

        expect(newWrapper.find(LayoutsCreate).length).toBe(1);
      });
    });

    describe('Edit', () => {
      it('should not render layouts edit component when user without permission route to "/setup/layouts/:entity/edit/:layoutId"', () => {
        const newWrapper = mount(
          <MemoryRouter initialEntries={['/setup/layouts/leads/edit/123']}>
            <Provider store={storeWithoutLayoutPermissions}>
              <Routes/>
            </Provider>
          </MemoryRouter>
        );

        expect(newWrapper.find(LayoutsEdit).length).toBe(0);
      });

      it('should render layouts edit component when user with permission route to "/setup/layouts/:entity/edit/:layoutId"', () => {
        const newWrapper = mount(
          <MemoryRouter initialEntries={['/setup/layouts/leads/edit/123']}>
            <Provider store={storeWithLayoutPermissions}>
              <Routes/>
            </Provider>
          </MemoryRouter>
        );

        expect(newWrapper.find(LayoutsEdit).length).toBe(1);
      });
    });
  });

  describe('IP Configurations', () => {
    it('should render ip configurations listing component when user with security permission routes to "/setup/account-management/security/ip-configurations/list"', () => {
      const storeWithSecurityPermission = mockStore(
        {
          ...storeData,
          appData: {
            ...storeData.appData,
            profilePermissions: [
              ...profilePermissions,
              {
                id: 13,
                name: 'security',
                description: 'has permission to security resource',
                limits: 50,
                units: 'count',
                action: {
                  read: true,
                  write: true,
                  update: true,
                  delete: true,
                  email: false,
                  call: false,
                  sms: false,
                  task: true,
                  note: true,
                  readAll: true,
                  updateAll: true
                }
              }
            ]
          }
        }
      );

      const newWrapper = mount(
        <MemoryRouter initialEntries={['/setup/account-management/security/ip-configurations/list']}>
         <Provider store={storeWithSecurityPermission}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find(IPConfigurationsList).length).toBe(1);
    });

    it('should NOT render ip configurations listing component when user does not have security permission routes to "/setup/account-management/security/ip-configurations/list"', () => {
      const newWrapper = mount(
        <MemoryRouter initialEntries={['/setup/account-management/security/ip-configurations/list']}>
         <Provider store={store}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find(IPConfigurationsList).length).toBe(0);
    });

    it('should render ip configurations create component when user with security permission routes to "/setup/account-management/security/ip-configurations/create"', () => {
      const storeWithSecurityPermission = mockStore(
        {
          ...storeData,
          appData: {
            ...storeData.appData,
            profilePermissions: [
              ...profilePermissions,
              {
                id: 13,
                name: 'security',
                description: 'has permission to security resource',
                limits: 50,
                units: 'count',
                action: {
                  read: true,
                  write: true,
                  update: true,
                  delete: true,
                  email: false,
                  call: false,
                  sms: false,
                  task: true,
                  note: true,
                  readAll: true,
                  updateAll: true
                }
              }
            ]
          }
        }
      );

      const newWrapper = mount(
        <MemoryRouter initialEntries={['/setup/account-management/security/ip-configurations/create']}>
         <Provider store={storeWithSecurityPermission}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find(CreateIPConfiguration).length).toBe(1);
    });

    it('should NOT render ip configurations create component when user does not have security permission routes to "/setup/account-management/security/ip-configurations/create"', () => {
      const newWrapper = mount(
        <MemoryRouter initialEntries={['/setup/account-management/security/ip-configurations/create']}>
         <Provider store={store}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find(CreateIPConfiguration).length).toBe(0);
    });

    it('should render ip configurations edit component when user with security permission routes to "/setup/account-management/security/ip-configurations/edit/:ipConfigurationId"', () => {
      const storeWithSecurityPermission = mockStore(
        {
          ...storeData,
          appData: {
            ...storeData.appData,
            profilePermissions: [
              ...profilePermissions,
              {
                id: 13,
                name: 'security',
                description: 'has permission to security resource',
                limits: 50,
                units: 'count',
                action: {
                  read: true,
                  write: true,
                  update: true,
                  delete: true,
                  email: false,
                  call: false,
                  sms: false,
                  task: true,
                  note: true,
                  readAll: true,
                  updateAll: true
                }
              }
            ]
          }
        }
      );

      const newWrapper = mount(
        <MemoryRouter initialEntries={['/setup/account-management/security/ip-configurations/edit/24']}>
         <Provider store={storeWithSecurityPermission}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find(EditIPConfiguration).length).toBe(1);
    });

    it('should NOT render ip configurations edit component when user does not have security permission routes to "/setup/account-management/security/ip-configurations/edit/:ipConfigurationId"', () => {
      const newWrapper = mount(
        <MemoryRouter initialEntries={['/setup/account-management/security/ip-configurations/edit/23']}>
         <Provider store={store}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find(EditIPConfiguration).length).toBe(0);
    });
  });

  describe('Two Factor Authentication', () => {
    it('should render two factor authentication component when user with security permission routes to "/setup/account-management/security/two-factor-authentication"', () => {
      const storeWithSecurityPermission = mockStore(
        {
          ...storeData,
          appData: {
            ...storeData.appData,
            profilePermissions: [
              ...profilePermissions,
              {
                id: 13,
                name: 'security',
                description: 'has permission to security resource',
                limits: 50,
                units: 'count',
                action: {
                  read: true,
                  write: true,
                  update: true,
                  delete: true,
                  email: false,
                  call: false,
                  sms: false,
                  task: true,
                  note: true,
                  readAll: true,
                  updateAll: true
                }
              }
            ]
          }
        }
      );

      const newWrapper = mount(
        <MemoryRouter initialEntries={['/setup/account-management/security/two-factor-authentication']}>
         <Provider store={storeWithSecurityPermission}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find(TwoFactorAuthentication).length).toBe(1);
    });

    it('should NOT render two factor authentication component when user does not have security permission routes to "/setup/account-management/security/two-factor-authentication"', () => {
      const newWrapper = mount(
        <MemoryRouter initialEntries={['/setup/account-management/security/two-factor-authentication']}>
         <Provider store={store}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find(TwoFactorAuthentication).length).toBe(0);
    });
  });

  describe('Currencies Listing', () => {
    it('should render currencies listing component when user with currency permission routes to "/setup/account-management/currencies/list"', () => {
      const storeWithCurrencyPermission = mockStore(
        {
          ...storeData,
          appData: {
            ...storeData.appData,
            profilePermissions: [
              ...profilePermissions,
              {
                id: 13,
                name: 'currency',
                description: 'has permission to currency resource',
                limits: 50,
                units: 'count',
                action: {
                  read: true,
                  write: true,
                  update: true,
                  delete: true,
                  email: false,
                  call: false,
                  sms: false,
                  task: true,
                  note: true,
                  readAll: true,
                  updateAll: true
                }
              }
            ]
          }
        }
      );

      const newWrapper = mount(
        <MemoryRouter initialEntries={['/setup/account-management/currencies/list']}>
         <Provider store={storeWithCurrencyPermission}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find(CurrenciesList).length).toBe(1);
    });

    it('should NOT render currencies listing component when user does not have currency permission routes to "/setup/account-management/currencies/list"', () => {
      const newWrapper = mount(
        <MemoryRouter initialEntries={['/setup/account-management/currencies/list']}>
         <Provider store={store}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find(CurrenciesList).length).toBe(0);
    });
  });

  describe('My Profile', () => {
    it('should render my profile component when routes to "/setup/account-management/me"', () => {
      const newWrapper = mount(
        <MemoryRouter initialEntries={['/setup/account-management/me']}>
         <Provider store={mockStore(storeData)}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find(MyProfile).length).toBe(1);
    });
  });

  describe('Exchange Rate History', () => {
    it('should render Exchange Rate History page when user with currency permission routes to "/setup/account-management/exchange-rates-history/list"', () => {
      const storeWithCurrencyPermission = mockStore(
        {
          ...storeData,
          appData: {
            ...storeData.appData,
            profilePermissions: [
              ...profilePermissions,
              {
                id: 13,
                name: 'currency',
                description: 'has permission to currency resource',
                limits: 50,
                units: 'count',
                action: {
                  read: true,
                  write: true,
                  update: true,
                  delete: true,
                  email: false,
                  call: false,
                  sms: false,
                  task: true,
                  note: true,
                  readAll: true,
                  updateAll: true
                }
              }
            ]
          }
        }
      );

      const newWrapper = mount(
        <MemoryRouter initialEntries={['/setup/account-management/exchange-rates-history/list']}>
          <Provider store={storeWithCurrencyPermission}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find(ExchangeRateHistoryList).length).toBe(1);
    });
  });

  describe('Exchange Rate List', () => {
    it('should render Exchange Rate Listing page when user with currency routes to "/setup/account-management/exchange-rates/list"', () => {
      const storeWithCurrencyPermission = mockStore(
        {
          ...storeData,
          appData: {
            ...storeData.appData,
            profilePermissions: [
              ...profilePermissions,
              {
                id: 13,
                name: 'currency',
                description: 'has permission to currency resource',
                limits: 50,
                units: 'count',
                action: {
                  read: true,
                  write: true,
                  update: true,
                  delete: true,
                  email: false,
                  call: false,
                  sms: false,
                  task: true,
                  note: true,
                  readAll: true,
                  updateAll: true
                }
              }
            ]
          }
        }
      );

      const newWrapper = mount(
        <MemoryRouter initialEntries={['/setup/account-management/exchange-rates/list']}>
          <Provider store={storeWithCurrencyPermission}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find(ExchangeRates).length).toBe(1);
    });
  });

  describe('Field Sales Executives List', () => {
    it('should render Field Sales list page, when user with Field Sales permission routes to "/setup/field-sales/executives/list"', () => {
      const storeWithFieldSalesPermission = mockStore(
        {
          ...storeData,
          appData: {
            ...storeData.appData,
            profilePermissions: [
              ...profilePermissions,
              {
                id: 13,
                name: 'fieldSales',
                description: 'has permission to field sales resource',
                limits: 50,
                units: 'count',
                action: {
                  read: true,
                  write: true,
                  update: true,
                  delete: true,
                  email: false,
                  call: false,
                  sms: false,
                  task: false,
                  note: false,
                  readAll: false,
                  updateAll: false
                }
              }
            ]
          }
        }
      );

      const newWrapper = mount(
        <MemoryRouter initialEntries={['/setup/field-sales/executives/list']}>
          <Provider store={storeWithFieldSalesPermission}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find(FieldExecutivesList).length).toBe(1);
    });
  });

  describe('Field Executives Live Location Tracking', () => {
    it('should render live location tracking page, when user with Field Sales permission routes to "/setup/field-sales/track/live-location"', () => {
      jest.spyOn(Storage.prototype, 'getItem').mockReturnValue('{"123": "03/05/2020"}');

      const storeWithFieldSalesPermission = mockStore(
        {
          ...storeData,
          appData: {
            ...storeData.appData,
            profilePermissions: [
              ...profilePermissions,
              {
                id: 13,
                name: 'fieldSales',
                description: 'has permission to field sales resource',
                limits: 50,
                units: 'count',
                action: {
                  read: true,
                  write: true,
                  update: true,
                  delete: true,
                  email: false,
                  call: false,
                  sms: false,
                  task: false,
                  note: false,
                  readAll: false,
                  updateAll: false
                }
              }
            ]
          }
        }
      );

      const newWrapper = mount(
        <MemoryRouter initialEntries={['/setup/field-sales/track/live-location']}>
          <Provider store={storeWithFieldSalesPermission}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find(LiveLocationTracking).length).toBe(1);
    });
  });

  describe('Field Executive Timeline', () => {
    it('should render field executive timeline page, when user with Field Sales permission routes to "/setup/field-sales/track/timeline"', () => {
      jest.spyOn(Storage.prototype, 'getItem').mockReturnValue('{"123": "03/05/2020"}');

      const storeWithFieldSalesPermission = mockStore(
        {
          ...storeData,
          appData: {
            ...storeData.appData,
            profilePermissions: [
              ...profilePermissions,
              {
                id: 13,
                name: 'fieldSales',
                description: 'has permission to field sales resource',
                limits: 50,
                units: 'count',
                action: {
                  read: true,
                  write: true,
                  update: true,
                  delete: true,
                  email: false,
                  call: false,
                  sms: false,
                  task: false,
                  note: false,
                  readAll: false,
                  updateAll: false
                }
              }
            ]
          }
        }
      );

      const newWrapper = mount(
        <MemoryRouter initialEntries={['/setup/field-sales/track/timeline']}>
          <Provider store={storeWithFieldSalesPermission}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find(FieldExecutiveTimeline).length).toBe(1);
    });
  });

  describe('Field Sales Configurations', () => {
    it('should render field sales configurations page, when user with Field Sales UPDATE_ALL permission routes to "/setup/field-sales/configurations"', () => {
      const storeWithFieldSalesPermission = mockStore(
        {
          ...storeData,
          appData: {
            ...storeData.appData,
            profilePermissions: [
              ...profilePermissions,
              {
                id: 13,
                name: 'fieldSales',
                description: 'has permission to field sales resource',
                limits: 50,
                units: 'count',
                action: {
                  read: true,
                  write: true,
                  update: true,
                  delete: true,
                  email: false,
                  call: false,
                  sms: false,
                  task: false,
                  note: false,
                  readAll: false,
                  updateAll: true
                }
              }
            ]
          }
        }
      );

      const newWrapper = mount(
        <MemoryRouter initialEntries={['/setup/field-sales/configurations']}>
          <Provider store={storeWithFieldSalesPermission}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find(FieldSalesConfigurations).length).toBe(1);
    });
  });

  describe('User Shift List', () => {
    it('should render User Shift list page, when user with shift permission routes to "/setup/shifts/list"', () => {
      const storeWithShiftPermission = mockStore(
        {
          ...storeData,
          appData: {
            ...storeData.appData,
            profilePermissions: [
              ...profilePermissions,
              {
                id: 13,
                name: 'shift',
                description: 'has permission to shift resource',
                limits: 50,
                units: 'count',
                action: {
                  read: false,
                  write: false,
                  update: false,
                  delete: true,
                  email: false,
                  call: false,
                  sms: false,
                  task: false,
                  note: false,
                  readAll: true,
                  updateAll: true
                }
              }
            ]
          }
        }
      );

      const newWrapper = mount(
        <MemoryRouter initialEntries={['/setup/shifts/list']}>
          <Provider store={storeWithShiftPermission}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find(UserShiftList).length).toBe(1);
    });
  });

  describe('User Shift Create', () => {
    it('should render User Shift create page, when user with shift permission routes to "/setup/shifts/create"', () => {
      const storeWithShiftPermission = mockStore(
        {
          ...storeData,
          appData: {
            ...storeData.appData,
            profilePermissions: [
              ...profilePermissions,
              {
                id: 13,
                name: 'shift',
                description: 'has permission to shift resource',
                limits: 50,
                units: 'count',
                action: {
                  read: false,
                  write: true,
                  update: false,
                  delete: true,
                  email: false,
                  call: false,
                  sms: false,
                  task: false,
                  note: false,
                  readAll: true,
                  updateAll: true
                }
              }
            ]
          }
        }
      );

      const newWrapper = mount(
        <MemoryRouter initialEntries={['/setup/shifts/create']}>
          <Provider store={storeWithShiftPermission}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find(UserShiftCreate).length).toBe(1);
    });
  });

  describe('User Shift Edit', () => {
    it('should render User Shift edit page, when user with shift permission routes to "/setup/shifts/edit/123"', () => {
      const storeWithShiftPermission = mockStore(
        {
          ...storeData,
          appData: {
            ...storeData.appData,
            profilePermissions: [
              ...profilePermissions,
              {
                id: 13,
                name: 'shift',
                description: 'has permission to shift resource',
                limits: 50,
                units: 'count',
                action: {
                  read: false,
                  write: false,
                  update: false,
                  delete: true,
                  email: false,
                  call: false,
                  sms: false,
                  task: false,
                  note: false,
                  readAll: true,
                  updateAll: false
                }
              }
            ]
          }
        }
      );

      const newWrapper = mount(
        <MemoryRouter initialEntries={['/setup/shifts/edit/123']}>
          <Provider store={storeWithShiftPermission}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find(UserShiftEdit).length).toBe(1);
    });
  });

  describe('Kylas AI', () => {
    it('should render AI For Email when user with kylas ai permission routes to "/setup/ai/email-assistant"', () => {
      const storeWithAIPermission = mockStore(
        {
          ...storeData,
          appData: {
            ...storeData.appData,
            profilePermissions: [
              ...profilePermissions,
              {
                id: 16,
                name: 'ai',
                description: 'has permission to kylas ai resource',
                limits: 50,
                units: 'count',
                action: {
                  read: false,
                  write: false,
                  update: false,
                  delete: true,
                  email: false,
                  call: false,
                  sms: false,
                  task: false,
                  note: false,
                  readAll: true,
                  updateAll: true
                }
              }
            ]
          }
        }
      );

      const newWrapper = mount(
        <MemoryRouter initialEntries={['/setup/ai/email-assistant']}>
          <Provider store={storeWithAIPermission}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find(EmailAssistant).length).toBe(1);
    });
  });

  describe('Notifications Settings', () => {
    it('should render Notification Settings when user with notification permission routes to "/setup/account-management/notifications/system"', () => {
      const storeWithNotificationPermission = mockStore(
        {
          ...storeData
        }
      );

      const newWrapper = mount(
        <MemoryRouter initialEntries={['/setup/account-management/notifications/system']}>
          <Provider store={storeWithNotificationPermission}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find(NotificationSettings).length).toBe(1);
    });
  });

  describe('Campaigns', () => {
    it('should render Campaigns when user with campaign permission routes to "/sales/campaigns/list"', () => {
      const storeWithCampaignPermission = mockStore(
        {
          ...storeData,
          appData: {
            ...storeData.appData,
            profilePermissions: [
              ...profilePermissions,
              {
                id: 14,
                name: 'campaign',
                description: 'has permission to campaign resource',
                limits: 10,
                units: 'count',
                action: {
                  read: true,
                  write: true,
                  update: true,
                  delete: true,
                  email: false,
                  call: false,
                  sms: false,
                  task: false,
                  note: false,
                  readAll: true,
                  updateAll: true
                }
              }
            ]
          }
        }
      );

      const newWrapper = mount(
        <MemoryRouter initialEntries={['/sales/campaigns/list']}>
          <Provider store={storeWithCampaignPermission}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find(CampaignList).length).toBe(1);
    });

    it('should render campaigns create page when user with campaign permission routes to "/sales/campaigns/create"', () => {
      const storeWithCampaignPermission = mockStore(
        {
          ...storeData,
          appData: {
            ...storeData.appData,
            profilePermissions: [
              ...profilePermissions,
              {
                id: 14,
                name: 'campaign',
                description: 'has permission to campaign resource',
                limits: 10,
                units: 'count',
                action: {
                  read: true,
                  write: true,
                  update: true,
                  delete: true,
                  email: false,
                  call: false,
                  sms: false,
                  task: false,
                  note: false,
                  readAll: true,
                  updateAll: true
                }
              }
            ]
          }
        }
      );

      const newWrapper = mount(
        <MemoryRouter initialEntries={['/sales/campaigns/create']}>
          <Provider store={storeWithCampaignPermission}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find(CampaignCreate).length).toBe(1);
    });

    it('should render campaigns edit page when user with campaign permission routes to "/sales/campaigns/edit/:campaignId"', () => {
      const storeWithCampaignPermission = mockStore(
        {
          ...storeData,
          appData: {
            ...storeData.appData,
            profilePermissions: [
              ...profilePermissions,
              {
                id: 14,
                name: 'campaign',
                description: 'has permission to campaign resource',
                limits: 10,
                units: 'count',
                action: {
                  read: true,
                  write: true,
                  update: true,
                  delete: true,
                  email: false,
                  call: false,
                  sms: false,
                  task: false,
                  note: false,
                  readAll: true,
                  updateAll: true
                }
              }
            ]
          }
        }
      );

      const newWrapper = mount(
        <MemoryRouter initialEntries={['/sales/campaigns/edit/1']}>
          <Provider store={storeWithCampaignPermission}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find(CampaignEdit).length).toBe(1);
    });

    it('should render campaigns view page when user with campaign permission routes to "/sales/campaigns/view/:campaignId"', () => {
      const storeWithCampaignPermission = mockStore(
        {
          ...storeData,
          appData: {
            ...storeData.appData,
            profilePermissions: [
              ...profilePermissions,
              {
                id: 14,
                name: 'campaign',
                description: 'has permission to campaign resource',
                limits: 10,
                units: 'count',
                action: {
                  read: true,
                  write: true,
                  update: true,
                  delete: true,
                  email: false,
                  call: false,
                  sms: false,
                  task: false,
                  note: false,
                  readAll: true,
                  updateAll: true
                }
              }
            ]
          }
        }
      );

      const newWrapper = mount(
        <MemoryRouter initialEntries={['/sales/campaigns/view/1']}>
          <Provider store={storeWithCampaignPermission}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find(CampaignView).length).toBe(1);
    });

    it('should render campaigns details page when user with campaign permission routes to "/sales/campaigns/details/:campaignId"', () => {
      const storeWithCampaignPermission = mockStore(
        {
          ...storeData,
          appData: {
            ...storeData.appData,
            profilePermissions: [
              ...profilePermissions,
              {
                id: 14,
                name: 'campaign',
                description: 'has permission to campaign resource',
                limits: 10,
                units: 'count',
                action: {
                  read: true,
                  write: true,
                  update: true,
                  delete: true,
                  email: false,
                  call: false,
                  sms: false,
                  task: false,
                  note: false,
                  readAll: true,
                  updateAll: true
                }
              }
            ]
          }
        }
      );

      const newWrapper = mount(
        <MemoryRouter initialEntries={['/sales/campaigns/details/1']}>
          <Provider store={storeWithCampaignPermission}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find(CampaignDetails).length).toBe(1);
    });

    it('should render campaign activity listing page when user with campaign permission routes to "/sales/campaigns/activities/list"', () => {
      const storeWithCampaignPermission = mockStore(
        {
          ...storeData,
          appData: {
            ...storeData.appData,
            profilePermissions: [
              ...profilePermissions,
              {
                id: 14,
                name: 'campaign',
                description: 'has permission to campaign resource',
                limits: 10,
                units: 'count',
                action: {
                  read: true,
                  write: true,
                  update: true,
                  delete: true,
                  email: false,
                  call: false,
                  sms: false,
                  task: false,
                  note: false,
                  readAll: true,
                  updateAll: true
                }
              }
            ]
          }
        }
      );

      const newWrapper = mount(
        <MemoryRouter initialEntries={['/sales/campaigns/activities/list']}>
          <Provider store={storeWithCampaignPermission}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find(CampaignActivityList).length).toBe(1);
    });

    it('should render campaign activity create page when user with campaign permission routes to "/sales/campaigns/activities/create"', () => {
      const storeWithCampaignPermission = mockStore(
        {
          ...storeData,
          appData: {
            ...storeData.appData,
            profilePermissions: [
              ...profilePermissions,
              {
                id: 14,
                name: 'campaign',
                description: 'has permission to campaign resource',
                limits: 10,
                units: 'count',
                action: {
                  read: true,
                  write: true,
                  update: true,
                  delete: true,
                  email: false,
                  call: false,
                  sms: false,
                  task: false,
                  note: false,
                  readAll: true,
                  updateAll: true
                }
              }
            ]
          }
        }
      );

      const newWrapper = mount(
        <MemoryRouter initialEntries={['/sales/campaigns/activities/create']}>
          <Provider store={storeWithCampaignPermission}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find(CampaignActivityCreate).length).toBe(1);
    });

    it('should render campaign activity edit page when user with campaign permission routes to "/sales/campaigns/activities/edit/1"', () => {
      const storeWithCampaignPermission = mockStore(
        {
          ...storeData,
          appData: {
            ...storeData.appData,
            profilePermissions: [
              ...profilePermissions,
              {
                id: 14,
                name: 'campaign',
                description: 'has permission to campaign resource',
                limits: 10,
                units: 'count',
                action: {
                  read: true,
                  write: true,
                  update: true,
                  delete: true,
                  email: false,
                  call: false,
                  sms: false,
                  task: false,
                  note: false,
                  readAll: true,
                  updateAll: true
                }
              }
            ]
          }
        }
      );

      const newWrapper = mount(
        <MemoryRouter initialEntries={['/sales/campaigns/activities/edit/1']}>
          <Provider store={storeWithCampaignPermission}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find(CampaignActivityEdit).length).toBe(1);
    });

    it('should render campaign activity recipient status page when user with campaign permission routes to "/sales/campaigns/activities/1/recipient-status/list"', () => {
      const storeWithCampaignPermission = mockStore(
        {
          ...storeData,
          appData: {
            ...storeData.appData,
            profilePermissions: [
              ...profilePermissions,
              {
                id: 14,
                name: 'campaign',
                description: 'has permission to campaign resource',
                limits: 10,
                units: 'count',
                action: {
                  read: true,
                  write: true,
                  update: true,
                  delete: true,
                  email: false,
                  call: false,
                  sms: false,
                  task: false,
                  note: false,
                  readAll: true,
                  updateAll: true
                }
              }
            ]
          }
        }
      );

      const newWrapper = mount(
        <MemoryRouter initialEntries={['/sales/campaigns/activities/1/recipient-status/list']}>
          <Provider store={storeWithCampaignPermission}>
            <Routes/>
          </Provider>
        </MemoryRouter>
      );

      expect(newWrapper.find(CampaignActivityRecipientStatusList).length).toBe(1);
    });
  });
});

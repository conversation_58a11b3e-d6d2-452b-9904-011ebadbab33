import * as React from 'react';
import { Route, Redirect, Switch } from 'react-router-dom';

export const loginUrl = '/signIn';
export const homeUrl = '/sales/home';
export const setupUrl = '/setup';
import Dashboard from '../../components/page/Dashboard/DashboardLayout';
import LoginForm from '../../components/page/login/components/LoginForm';
import signUpForm from '../../components/page/signUp/componets/signUpForm';
import ActivateUser from '../../components/page/user/components/ActivateUser';
import UserSetPasswordForm from '../../components/page/user/components/UserSetPassword';
import UserResitrationExpiredForm from '../../components/page/user/components/UserRegistrationExpired';
import ForgotPassword from '../../components/page/user/components/ForgotPassword';
import UserVerifyEmail from '../../components/page/user/components/UserVerifyEmail';
import AssignUserComponent from './../../components/page/AssignUsers/AssignUser';
import PipelineListComponent from '../../components/page/pipeline/components/PipelineList';

import SetupHomeComponent from '../../components/page/Setup/Setup';
import TenentSettingComponent from '../../components/page/generalSettings/TenentSettings';
import CreateProfileForm from '../../components/page/profile/components/CreateProfile';
import ProfileList from '../../components/page/profile/components/ProfileList';
import ModuleCustomizationComponent from '../../components/page/generalSettings/ModuleCustomization';
import FieldsListingComponent from '../../components/page/FieldSettings/FieldList';
import FieldWizardFormComponent from '../../components/page/FieldSettings/FieldCreate';
import FieldWizardEditFormComponent from '../../components/page/FieldSettings/FieldEdit';
import LeadPipelineLayoutComponent from '../../components/page/pipeline/components/leads/LeadPipelineComponent';
import { routePermissions } from '../../utils/routePermissions';
import { withApiErrorHandlingAndAuthorization } from '../../services/withApiErrorHandlingAndAuthorization';
import ErrorPage from '../../components/shared/Error/ErrorPage';
import { ErrorType } from '../../utils/ErrorUtil';
import { Products } from '../../utils/constants';
import DataManagement from '../../components/page/DataManagement/Components/DataManagement';
import ProductsServices from '../../components/page/ProductsServices/Components/ProductsServices';
import DealDetails from '../../components/page/Deal/components/DealDetails/DealDetails';
import CompanyDetails from '../../components/page/Company/components/CompanyDetails/CompanyDetails';
import CompanyListLayout from '../../components/page/Company/components/CompanyList/CompanyListLayout/CompanyListLayout';
import DealListLayout from '../../components/page/Deal/components/DealListLayout/DealListLayout';
import LeadList from '../../components/page/Lead/components/LeadList/LeadList';
import ContactList from '../../components/page/Contact/ContactList/ContactList';
import ReportDetails from '../../components/page/Reports/components/ReportDetails/ReportDetails';
import ReportCreate from '../../components/page/Reports/components/ReportCreate/ReportCreate';
import ReportEdit from '../../components/page/Reports/components/ReportEdit/ReportEdit';
import LeadCaptureFormsListLayout from '../../components/page/LeadCaptureForms/components/list/LeadCaptureFormsListLayout';
import LeadCaptureFormCreate from '../../components/page/LeadCaptureForms/components/Create/LeadCaptureFormCreate';
import LeadCaptureFormEdit from '../../components/page/LeadCaptureForms/components/Edit/LeadCaptureFormEdit';
import MeetingRsvpWrapper from '../../components/page/Meeting/components/ExternalRsvp/MeetingRsvpWrapper';
import WorkflowsListLayout from '../../components/page/Workflows/components/List/WorkflowsListLayout';
import WorkflowCreate from '../../components/page/Workflows/components/Create/WorkflowCreate';
import EmailSettingsLayout from '../../components/page/Communication/EmailSettings/EmailSettingsLayout';
import WhatsAppSettingsLayout from '../../components/page/Communication/WhatsAppSettings/WhatsAppSettingsLayout';
import WorkflowView from '../../components/page/Workflows/components/View/WorkflowView';
import WorkflowEdit from '../../components/page/Workflows/components/Edit/WorkflowEdit';
import EmailLoginPopUp from '../../components/page/Communication/EmailSettings/ConnectEmail/EmailLoginPopUp';
import UsageStatistics from '../../components/page/Billing/UsageStatistics/UsageStatistics';
import EmailTemplatesLayout from '../../components/page/Communication/EmailTemplates/EmailTemplatesLayout';
import CreateEmailTemplate from '../../components/page/Communication/EmailTemplates/FormLayout/CreateEmailTemplate';
import EditEmailTemplate from '../../components/page/Communication/EmailTemplates/FormLayout/EditEmailTemplate';
import SubscriptionDetails from '../../components/page/Billing/SubscriptionDetails/SubscriptionDetails';
import TaskListLayout from '../../components/page/TaskLayout/TaskListLayout';
import LeadDetails from '../../components/page/Lead/components/LeadDetails/LeadDetails';
import ContactDetails from '../../components/page/Contact/ContactDetails/ContactDetails';
import SharingComponent from '../../components/page/sharing/SharingComponent';
import SharingList from '../../components/page/sharing/SharingList';
import UserList from '../../components/page/user/UserList';
import TeamList from '../../components/page/Team/TeamList';
import UserCreate from '../../components/page/user/UserCreate';
import UserEdit from '../../components/page/user/UserEdit';
import TeamCreate from '../../components/page/Team/TeamCreate';
import TeamEdit from '../../components/page/Team/TeamEdit';
import ApiKeysListLayout from '../../components/page/ApiKeys/components/list/ApiKeysListLayout';
import AllApps from '../../components/products/marketplace/AllApps/List/AllApps';
import ManageAppsList from '../../components/products/marketplace/ManageApps/List/ManageAppsList';
import CreateApp from '../../components/products/marketplace/ManageApps/Create/CreateApp';
import EditApp from '../../components/products/marketplace/ManageApps/Edit/EditApp';
import AppDetailsComponent  from '../../components/products/marketplace/AllApps/Details/AppDetails';
import InstalledAppsListComponent from '../../components/products/marketplace/InstalledApps/List/InstalledAppsList';
import BulkJobsListComponent from '../../components/page/BulkJobs/components/List/BulkJobsList';
import ExportListComponent from '../../components/page/Export/components/List/ExportList';
import CreateExportJob from '../../components/page/Export/components/Create/CreateExportJob';
import WebhooksList from '../../components/page/Webhooks/components/List/WebhooksList';
import WebhookHistoryList from '../../components/page/WebhookHistory/WebhookHistoryList';
import ScheduledJobsListLayout from '../../components/page/ScheduledJobs/components/List/ScheduledJobsListLayout';
import ReportListLayout from '../../components/page/Reports/components/ReportListLayout/ReportListLayout';
import QuotationsListLayout from '../../components/page/Quotation/components/List/QuotationsListLayout';
import QuotationDetails from '../../components/page/Quotation/components/Details/QuotationDetails';
import MeetingListLayout from '../../components/page/Meeting/components/MeetingList/MeetingDetails/MeetingListLayout';
import CalendarSettings from '../../components/page/Communication/CalendarSettings/Components/CalendarSettings/CalendarSettings';
import CallListLayout from '../../components/page/CallLayout/CallList/CallListLayout';
import LayoutsList from '../../components/page/Layouts/components/List/LayoutsList';
import LayoutCreateForm from '../../components/page/Layouts/components/Form/Create/LayoutCreateForm';
import LayoutEditForm from '../../components/page/Layouts/components/Form/Edit/LayoutEditForm';
import ConversionMappingLayout from '../../components/page/ConversionMapping/components/ConversionMappingLayout';
import GoalCreate from '../../components/page/Goal/components/GoalCreate/GoalCreate';
import GoalListLayout from '../../components/page/Goal/components/GoalsListLayout/GoalsListLayout';
import GoalEdit from '../../components/page/Goal/components/GoalEdit/GoalEdit';
import { withBaseCurrency } from '../../services/withBaseCurrency';
import CurrenciesList from '../../components/page/Currencies/components/CurrenciesList/CurrenciesList';
import EmailListLayout from '../../components/page/Email/EmailListLayout/EmailListLayout';
import AccountSetup from '../../components/shared/AccountSetup/AccountSetup';
import ExchangeRateHistoryList from '../../components/page/Currencies/components/ExchangeRateHistoryList/ExchangeRateHistoryList';
import ExchangeRates from '../../components/page/Currencies/components/ExchangeRates/ExchangeRates';
import MyProfileSettings from '../../components/page/user/profile/MyProfile';
import IPConfigurationsList from '../../components/page/IPConfigurations/components/IPConfigurationsList/IPConfigurationsList';
import EditIPConfiguration from '../../components/page/IPConfigurations/components/EditIPConfiguration/EditIPConfiguration';
import CreateIPConfiguration from '../../components/page/IPConfigurations/components/CreateIPconfiguration/CreateIPConfiguration';
import TwoFactorAuthentication from '../../components/page/TwoFactorAuthentication/TwoFactorAuthentication';
import VerifyOTP from '../../components/page/login/VerifyOTP/VerifyOTP';
import withAbortController from '../../components/shared/AbortController/withAbortController';
import FieldExecutivesList from '../../components/page/FieldSales/FieldExecutivesList/FieldExecutivesList';
import LiveLocationTracking from '../../components/page/FieldSales/LiveLocationTracking/LiveLocationTracking';
import FieldExecutiveTimeline from '../../components/page/FieldSales/FieldExecutiveTimeline/FieldExecutiveTimeline';
import WhatsAppFieldMapping from '../../components/page/Communication/WhatsAppSettings/FieldMapping/WhatsAppFieldMapping';
import UserShiftList from '../../components/page/UserShifts/UserShiftList/UserShiftList';
import UserShiftCreate from '../../components/page/UserShifts/UserShiftCreate/UserShiftCreate';
import UserShiftEdit from '../../components/page/UserShifts/UserShiftEdit/UserShiftEdit';
import FieldSalesConfigurations from '../../components/page/FieldSales/FieldSalesConfigurations/FieldSalesConfigurations';
import WhatsAppTemplatesList from '../../components/page/Communication/WhatsAppTemplates/List/WhatsAppTemplatesList';
import CreateWhatsAppTemplate from '../../components/page/Communication/WhatsAppTemplates/CreateEditTemplate/CreateWhatsAppTemplate';
import EditWhatsAppTemplate from '../../components/page/Communication/WhatsAppTemplates/CreateEditTemplate/EditWhatsAppTemplate';
import AddAgents from '../../components/page/Communication/WhatsAppSettings/Agents/AddAgents';
import CreateBulkJob from '../../components/page/BulkJobs/components/Create/CreateBulkJob';
import WorkflowTemplates from '../../components/page/Workflows/components/Template/WorkflowTemplates';
import WhatsAppBillingInformation from '../../components/page/Communication/WhatsAppSettings/WhatsAppBillingInformation/WhatsAppBillingInformation';
import WorkflowActionLogsListLayout from '../../components/page/WorkflowActionLogs/WorkflowActionLogsListLayout/WorkflowActionLogsListLayout';
import ApiUsage from '../../components/page/ApiUsage/ApiUsage';
import ScoringRulesListLayout from '../../components/page/ScoringRules/ScoringRulesListLayout/ScoringRulesListLayout';
import ScoringRuleStepTwo from '../../components/page/ScoringRules/ScoringRuleStepTwo/ScoringRuleStepTwo';
import EmailAssistant from '../../components/shared/AIFeatures/EmailAssistant/EmailAssistant';
import NotificationSettings from '../../components/page/NotificationSettings/NotificationSettings';
import WhatsAppListLayout from '../../components/page/WhatsApp/WhatsAppListLayout/WhatsAppListLayout';
import CampaignList from '../../components/page/Campaign/CampaignList/CampaignList';
import CampaignCreate from '../../components/page/Campaign/CampaignCreate/CampaignCreate';
import CampaignEdit from '../../components/page/Campaign/CampaignEdit/CampaignEdit';
import CampaignView from '../../components/page/Campaign/CampaignView/CampaignView';
import { isProd } from '../../config/apiKeys';
import CallTranscriptSummary from '../../components/shared/AIFeatures/CallIntelligence/CallTranscriptSummary/CallTranscriptSummary';
import CampaignDetails from '../../components/page/Campaign/CampaignDetails/CampaignDetails';
import CampaignActivityList from '../../components/page/Campaign/CampaignActivityList/CampaignActivityList';
import CampaignActivityCreate from '../../components/page/Campaign/CampaignActivityCreate/CampaignActivityCreate';
import CampaignActivityEdit from '../../components/page/Campaign/CampaignActivityEdit/CampaignActivityEdit';
import SmartTaskCreator from '../../components/shared/AIFeatures/SmartTaskCreator/SmartTaskCreator';
import CampaignActivityRecipientStatusList from '../../components/page/Campaign/CampaignActivityRecipientStatusList/CampaignActivityRecipientStatusList';

export interface Props {
  logoutUser?: (e:any)=> void;
  isLoggedIn: boolean;
  isAuth: boolean;
  selected: string[];
  depId: number;
  depName: string;
  showDrop?: boolean;
  optionId?: number;
  globalUser?: boolean;
  open: () => void;
  close: () => void;
  openClose: () => void;
  updateDepId: () => void;
  showDropAction: () => void;
  history: any;
}

export const isAuthenticated = (Component: any) => {
  // checks weather the user have a token stored in the cache
  // determines if is a loggedin  user
  if (localStorage.getItem('token')) {
    if (localStorage.getItem('slingApp') !== Products.SALES) {
      return <Redirect to={setupUrl} />;
    }
    return <Redirect to={homeUrl} />;
  }
  // @ts-ignore
  return <Component {...this.props} />;
};

class Routes extends React.Component<any, any> {

  render() {
    const AccountSetupRoute = withApiErrorHandlingAndAuthorization(AccountSetup);
    const DashboardRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(withAbortController(Dashboard)));
    const ProfileListRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(ProfileList), routePermissions.ProfileListRoute);
    const UserListRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(UserList), routePermissions.UserListRoute);
    const UserCreateRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(UserCreate), routePermissions.UserCreateRoute);
    const UserEditRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(UserEdit), routePermissions.UserEditRoute);
    const TeamListRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(TeamList), routePermissions.TeamListRoute);
    const TeamCreateRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(TeamCreate), routePermissions.TeamCreateRoute);
    const TeamEditRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(TeamEdit), routePermissions.TeamEditRoute);
    const TaskLayoutRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(TaskListLayout), routePermissions.TaskListRoute);
    const CallLayoutRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(CallListLayout), routePermissions.CallsListRoute);
    const LeadListRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(LeadList), routePermissions.LeadListRoute);
    const ContactListRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(ContactList), routePermissions.ContactListRoute);
    const LeadDetailsRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(LeadDetails), routePermissions.LeadDetailsRoute);
    const ContactDetailsRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(ContactDetails), routePermissions.ContactDetailsRoute);
    const SharingListRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(SharingList), routePermissions.SharingListLayoutRoute);
    const SharingCreateLayoutRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(SharingComponent), routePermissions.SharingCreateLayoutRoute);
    const SharingEditLayoutRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(SharingComponent), routePermissions.SharingEditLayoutRoute);
    const LeadPipelineLayoutRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(LeadPipelineLayoutComponent), routePermissions.PipelineLayoutRoute);
    const SetupRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(SetupHomeComponent), routePermissions.SetupRoute);
    const PipelineRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(PipelineListComponent), routePermissions.PipelineListRoute);
    const AssignUserRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(AssignUserComponent), routePermissions.AssignUserRoute);
    const TenantSettingRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(TenentSettingComponent), routePermissions.TenantUpdatePermission);
    const MyProfileSettingRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(MyProfileSettings));
    const ModuleCustomizationRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(ModuleCustomizationComponent), routePermissions.ModuleCustomizationRoute);
    const FieldsListingRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(FieldsListingComponent), routePermissions.FieldsListingRoute);
    const FieldWizardFormRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(FieldWizardFormComponent), routePermissions.FieldWizardFormRoute);
    const FieldsEditFormRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(FieldWizardEditFormComponent), routePermissions.FieldsEditFormRoute);
    const CreateProfileRoute=withApiErrorHandlingAndAuthorization(withBaseCurrency(CreateProfileForm), routePermissions.CreateProfileRoute);
    const EditProfileRoute=withApiErrorHandlingAndAuthorization(withBaseCurrency(CreateProfileForm), routePermissions.EditProfileRoute);
    const DataManagementRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(DataManagement), routePermissions.DataImportRoute);
    const ProductsServicesRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(ProductsServices), routePermissions.ProductsServicesRoute);
    const CreateProductsServicesRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(ProductsServices), routePermissions.CreateProductsServicesRoute);
    const EditProductsServicesRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(ProductsServices), routePermissions.EditProductsServicesRoute);
    const DealDetailsRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(DealDetails), routePermissions.DealDetailsRoute);
    const DealListRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(DealListLayout), routePermissions.DealListRoute);
    const CompanyDetailsRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(CompanyDetails), routePermissions.CompanyDetailsRoute);
    const CompanyListRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(CompanyListLayout), routePermissions.CompanyListRoute);
    const ReportListRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(ReportListLayout), routePermissions.ReportListRoute);
    const ReportCreateRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(ReportCreate), routePermissions.ReportCreateRoute);
    const ReportEditRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(ReportEdit), routePermissions.ReportEditRoute);
    const ReportDetailsRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(ReportDetails), routePermissions.ReportDetailsRoute);

    const CampaignListRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(CampaignList), routePermissions.CampaignListRoute);
    const CampaignCreateRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(CampaignCreate), routePermissions.CampaignCreateRoute);
    const CampaignEditRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(CampaignEdit), routePermissions.CampaignEditRoute);
    const CampaignViewRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(CampaignView), routePermissions.CampaignEditRoute);
    const CampaignDetailsRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(CampaignDetails), routePermissions.CampaignDetailsRoute);
    const CampaignActivityListRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(CampaignActivityList), routePermissions.CampaignActivityListRoute);
    const CampaignActivityCreateRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(CampaignActivityCreate), routePermissions.CampaignActivityCreateRoute);
    const CampaignActivityEditRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(CampaignActivityEdit), routePermissions.CampaignActivityEditRoute);
    const CampaignActivityRecipientStatusListRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(CampaignActivityRecipientStatusList), routePermissions.CampaignActivityRecipientStatusList);

    const GoalListRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(GoalListLayout), routePermissions.GoalListRoute);
    const GoalCreateRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(GoalCreate), routePermissions.GoalCreateRoute);
    const GoalEditRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(GoalEdit), routePermissions.GoalEditRoute);

    const LeadCaptureFormsListRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(LeadCaptureFormsListLayout), routePermissions.LeadCaptureFormsListRoute);
    const LeadCaptureFormsCreateRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(LeadCaptureFormCreate), routePermissions.LeadCaptureFormsCreateRoute);
    const LeadCaptureFormsEditRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(LeadCaptureFormEdit), routePermissions.LeadCaptureFormsEditRoute);

    const ApiKeysListRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(ApiKeysListLayout), routePermissions.TenantReadPermission);

    const ApiUsageRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(ApiUsage), routePermissions.TenantReadPermission);

    const MeetingListRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(MeetingListLayout));

    const EmailListRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(EmailListLayout), routePermissions.EmailListRoute);

    const WhatsAppMessageListRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(WhatsAppListLayout), routePermissions.WhatsAppListRoute);

    const WorkflowsListRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(WorkflowsListLayout), routePermissions.WorkflowListRoute);
    const WorkflowTemplateRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(WorkflowTemplates), routePermissions.WorkflowTemplateRoute);
    const WorkflowsCreateRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(WorkflowCreate), routePermissions.WorkflowCreateRoute);
    const WorkflowViewRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(WorkflowView), routePermissions.WorkflowViewRoute);
    const WorkflowEditRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(WorkflowEdit), routePermissions.WorkflowEditRoute);
    const WorkflowActionLogsListRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(WorkflowActionLogsListLayout), routePermissions.WorkflowActionLogsListRoute);
    const ScheduledJobsListRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(ScheduledJobsListLayout), routePermissions.ScheduledJobsListRoute);
    const ScoringRulesListRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(ScoringRulesListLayout), routePermissions.ScoringRulesListRoute);
    const ScoringRulesCreateRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(ScoringRuleStepTwo), routePermissions.ScoringRulesCreateRoute);
    const ScoringRulesEditRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(ScoringRuleStepTwo), routePermissions.ScoringRulesEditRoute);

    const BillingUsageRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(UsageStatistics), routePermissions.TenantReadPermission);
    const BillingSubscriptionDetailsRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(SubscriptionDetails), routePermissions.TenantReadPermission);

    const EmailSettingsRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(EmailSettingsLayout));
    const EmailTemplatesRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(EmailTemplatesLayout), routePermissions.EmailTemplatesListRoute);
    const CreateEmailTemplateRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(CreateEmailTemplate), routePermissions.EmailTemplatesCreateRoute);
    const EditEmailTemplateRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(EditEmailTemplate), routePermissions.EmailTemplatesEditRoute);

    const emailLoginPopUpRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(EmailLoginPopUp));

    const WhatsAppSettingsRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(WhatsAppSettingsLayout), routePermissions.WhatsAppSettingsRoute);
    const WhatsAppFieldMappingRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(WhatsAppFieldMapping), routePermissions.WhatsAppSettingsRoute);
    const WhatsAppAddAgentsRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(AddAgents), routePermissions.WhatsAppSettingsRoute);
    const WhatsAppBillingInformationRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(WhatsAppBillingInformation), routePermissions.WhatsAppSettingsRoute);

    const CreateWhatsAppTemplateRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(CreateWhatsAppTemplate), routePermissions.CreateWhatsAppTemplateRoute);
    const EditWhatsAppTemplateRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(EditWhatsAppTemplate), routePermissions.EditWhatsAppTemplateRoute);
    const VariableMappingRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(EditWhatsAppTemplate), routePermissions.EditWhatsAppTemplateRoute);
    const WhatsAppTemplatesListRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(WhatsAppTemplatesList), routePermissions.WhatsAppTemplatesListRoute);

    const MarketplaceAllAppsRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(AllApps));
    const MarketplaceAppDetailsRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(AppDetailsComponent));

    const MarketplaceInstalledAppsRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(InstalledAppsListComponent));

    const MarketplaceManageAppsRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(ManageAppsList), routePermissions.TenantReadPermission);
    const MarketplaceCreateAppRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(CreateApp), routePermissions.TenantUpdatePermission);
    const MarketplaceEditAppRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(EditApp), routePermissions.TenantUpdatePermission);

    const BulkJobsListRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(BulkJobsListComponent));
    const CreateBulkJobRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(CreateBulkJob), routePermissions.CreateBulkJobRoute);

    const ExportListRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(ExportListComponent), routePermissions.ExportListRoute);
    const CreateExportJobRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(CreateExportJob), routePermissions.ExportCreateRoute);

    const WebhooksListRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(WebhooksList), routePermissions.WebhookListRoute);
    const WebhookHistoryListRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(WebhookHistoryList), routePermissions.WebhookListRoute);

    const QuotationsListRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(QuotationsListLayout), routePermissions.QuotationsListRoute);
    const QuotationDetailsRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(QuotationDetails), routePermissions.QuotationsDetailsRoute);

    const CalendarSettingsRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(CalendarSettings), routePermissions.CalendarSettingsRoute);

    const LayoutsListRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(LayoutsList), routePermissions.LayoutsListRoute);
    const LayoutsCreateRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(LayoutCreateForm), routePermissions.LayoutsCreateRoute);
    const LayoutsEditRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(LayoutEditForm), routePermissions.LayoutsEditRoute);

    const ConversionMappingRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(ConversionMappingLayout), routePermissions.ConversionMappingRoute);

    const CurrenciesListRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(CurrenciesList), routePermissions.CurrenciesListRoute);
    const ExchangeRateHistoryListRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(ExchangeRateHistoryList), routePermissions.ExchangeRateHistoryListRoute);
    const ExchangeRatesRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(ExchangeRates), routePermissions.ExchangeRatesRoute);

    const IPConfigurationsListRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(IPConfigurationsList), routePermissions.IPConfigurationsListRoute);
    const IPConfigurationsCreateRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(CreateIPConfiguration), routePermissions.IPConfigurationsCreateRoute);
    const IPConfigurationsEditRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(EditIPConfiguration), routePermissions.IPConfigurationsEditRoute);

    const TwoFactorAuthenticationRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(TwoFactorAuthentication), routePermissions.TwoFactorAuthenticationRoute);

    const FieldExecutivesListRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(FieldExecutivesList), routePermissions.FieldExecutivesListRoute);
    const LiveLocationTrackingRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(LiveLocationTracking), routePermissions.LiveLocationTrackingRoute);
    const FieldExecutiveTimelineRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(FieldExecutiveTimeline), routePermissions.FieldExecutiveTimelineRoute);
    const FieldSalesConfigurationsRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(FieldSalesConfigurations), routePermissions.FieldSalesConfigurationsRoute);

    const UserShiftListRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(UserShiftList), routePermissions.UserShiftListRoute);
    const UserShiftCreateRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(UserShiftCreate), routePermissions.UserShiftCreateRoute);
    const UserShiftEditRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(UserShiftEdit), routePermissions.UserShiftEditRoute);

    const EmailAssistantSettingsRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(EmailAssistant), routePermissions.AIPermission);
    const CallTranscriptSummaryRoute = withApiErrorHandlingAndAuthorization(withBaseCurrency(CallTranscriptSummary), routePermissions.AIPermission);
    const SmartTaskCreatorRoute= withApiErrorHandlingAndAuthorization(withBaseCurrency(SmartTaskCreator), routePermissions.AIPermission);

    const NotificationSettingsRoute= withApiErrorHandlingAndAuthorization(withBaseCurrency(NotificationSettings));
    // @ts-ignore
    return (
      <Switch>
        <Route
          exact={true}
          path="/"
          render={() => isAuthenticated(LoginForm)}
        />
        <Route path="/signIn" render={() => isAuthenticated(LoginForm)} />
        <Route path="/signUp" render={() => isAuthenticated(signUpForm)} />
        <Route exact={true} path="/verify-otp"  component={VerifyOTP}/>
        <Route exact={true} path="/meetings/rsvp" component={MeetingRsvpWrapper} />

        <Route path="/user/forgotPassword" component={ForgotPassword} />
        <Route path="/user/activate/:token" component={ActivateUser} />
        <Route
          path="/user/setPassword/:token"
          component={UserSetPasswordForm}
        />
        <Route path="/user/verifyEmail/:token" component={UserVerifyEmail} />
        <Route path="/user/expired" component={UserResitrationExpiredForm} />
        {/* pre listing pages */}

        <Route exact={true} path="/setup/account" component={AccountSetupRoute} />
        <Route path="/sales/home" component={DashboardRoute} />
        <Route exact={true} path="/sales/deals/list" component={DealListRoute} />
        <Route exact={true} path="/sales/deals/details/:dealId" component={DealDetailsRoute} />

        <Route exact={true} path="/sales/companies/list" component={CompanyListRoute} />
        <Route exact={true} path="/sales/companies/details/:companyId" component={CompanyDetailsRoute} />

        <Route exact={true} path="/sales/leads/list" component={LeadListRoute} />
        <Route exact={true} path="/sales/leads/details/:entityId" render={() => {
          // @ts-ignore
          return <LeadDetailsRoute entity="leads" entityAction="details"/>;
        }} />

        <Route exact={true} path="/sales/contacts/list" component={ContactListRoute} />
        <Route exact={true} path="/sales/contacts/details/:entityId" render={() => {
          // @ts-ignore
          return <ContactDetailsRoute entity="contacts" entityAction="details"/>;
        }} />

        <Route exact={true} path="/sales/tasks/list" component={TaskLayoutRoute} />

        { !isProd() && <Route exact={true} path="/sales/campaigns/list" component={CampaignListRoute} /> }
        { !isProd() && <Route exact={true} path="/sales/campaigns/create" component={CampaignCreateRoute} /> }
        { !isProd() && <Route exact={true} path="/sales/campaigns/edit/:campaignId" component={CampaignEditRoute} /> }
        { !isProd() && <Route exact={true} path="/sales/campaigns/view/:campaignId" component={CampaignViewRoute} /> }
        { !isProd() && <Route exact={true} path="/sales/campaigns/details/:campaignId" component={CampaignDetailsRoute} /> }
        { !isProd() && <Route exact={true} path="/sales/campaigns/activities/list" component={CampaignActivityListRoute} /> }
        { !isProd() && <Route exact={true} path="/sales/campaigns/activities/create" component={CampaignActivityCreateRoute} /> }
        { !isProd() && <Route exact={true} path="/sales/campaigns/activities/edit/:activityId" component={CampaignActivityEditRoute} /> }
        { !isProd() && <Route exact={true} path="/sales/campaigns/activities/:id/recipient-status/list" component={CampaignActivityRecipientStatusListRoute} /> }

        <Route exact={true} path="/sales/reports/list" component={ReportListRoute} />
        <Route exact={true} path="/sales/reports/create" component={ReportCreateRoute} />
        <Route exact={true} path="/sales/reports/details/:reportId" component={ReportDetailsRoute} />
        <Route exact={true} path="/sales/reports/edit/:reportId" component={ReportEditRoute} />

        <Route exact={true} path="/sales/meetings/list" component={MeetingListRoute} />
        <Route exact={true} path="/sales/emails/list" component={EmailListRoute} />
        <Route exact={true} path="/sales/calls/list" component={CallLayoutRoute} />
        <Route exact={true} path="/sales/whatsapp/list" component={WhatsAppMessageListRoute} />

        <Route exact={true} path="/sales/quotations/list" component={QuotationsListRoute} />
        <Route exact={true} path="/sales/quotations/details/:quotationId" component={QuotationDetailsRoute} />

        <Route exact={true} path="/setup/" component={SetupRoute} />

        <Route exact={true} path="/setup/data-management/bulk-jobs/list" component={BulkJobsListRoute} />
        <Route exact={true} path="/setup/data-management/bulk-jobs/create" component={CreateBulkJobRoute} />

        <Route exact={true} path="/setup/data-management/exports/list" component={ExportListRoute} />
        <Route exact={true} path="/setup/data-management/exports/create" component={CreateExportJobRoute} />

        <Route exact={true} path="/setup/data-management/sharing/:entity/list" component={SharingListRoute}/>
        <Route exact={true} path="/setup/data-management/sharing/:entity/edit/:entityId" render={() => {
          // @ts-ignore
          return <SharingEditLayoutRoute entity="sharing" entityAction="edit"/>;
        }}/>
        <Route exact={true} path="/setup/data-management/sharing/:entity/create" render={() => {
          // @ts-ignore
          return <SharingCreateLayoutRoute entity="sharing" entityAction="create"/>;
        }} />


        <Route exact={true} path="/setup/data-management/:entity/:actionParam/:importEntity?" component={DataManagementRoute} />

        <Route exact={true} path="/setup/:entityFor/pipelines/list" component={PipelineRoute} />

        <Route exact={true} path="/setup/:entityFor/pipelines/:entityAction" component={LeadPipelineLayoutRoute} />

        <Route exact={true} path="/setup/:entityFor/pipelines/:entityAction/:entityId" component={LeadPipelineLayoutRoute} />

        {/* <Route exact={true} path="/setup/deals/pipelines/:entityAction" component={DealPipelineLayoutRoute} />

        <Route exact={true} path="/setup/deals/pipelines/:entityAction/:entityId" component={DealPipelineLayoutRoute} /> */}

        <Route exact={true} path="/setup/users/create" component={UserCreateRoute} />
        <Route exact={true} path="/setup/users/edit/:entityId" component={UserEditRoute} />
        <Route exact={true} path="/setup/users/:view(list|hierarchy)" component={UserListRoute} />
        {/*<Route exact={true} path="/setup/users/edit/:entityId/availability" render={() => {*/}
        {/*  // @ts-ignore*/}
        {/*  return <EditLayoutRoute entity="users"/>;*/}
        {/*}} />*/}

        <Route exact={true} path="/setup/teams/list" component={TeamListRoute} />
        <Route exact={true} path="/setup/teams/create" component={TeamCreateRoute} />
        <Route exact={true} path="/setup/teams/edit/:entityId" component={TeamEditRoute} />
        <Route  exact={true} path="/setup/teams/assign/:entityId" component={AssignUserRoute} />


        <Route exact={true} path="/setup/account-management/general" component={TenantSettingRoute} />
        <Route exact={true} path="/setup/account-management/me" component={MyProfileSettingRoute} />
        <Route exact={true} path="/setup/account-management/notifications/:category" component={NotificationSettingsRoute}/>

        <Route exact={true} path="/setup/:entity/settings" component={ModuleCustomizationRoute} />
        <Route exact={true} path="/setup/fields/:entity/list" component={FieldsListingRoute} />
        <Route exact={true} path="/setup/fields/:entity/create" component={FieldWizardFormRoute} />
        <Route exact={true} path="/setup/fields/:entity/edit/:entityId" component={FieldsEditFormRoute} />

        <Route exact={true} path="/setup/account-management/security/ip-configurations/list" component={IPConfigurationsListRoute} />
        <Route exact={true} path="/setup/account-management/security/ip-configurations/create" component={IPConfigurationsCreateRoute} />
        <Route exact={true} path="/setup/account-management/security/ip-configurations/edit/:ipConfigurationId" component={IPConfigurationsEditRoute} />

        <Route exact={true} path="/setup/account-management/security/two-factor-authentication" component={TwoFactorAuthenticationRoute} />

        <Route exact={true} path="/setup/profiles/list" component={ProfileListRoute} />

        <Route exact={true} path="/setup/profiles/create" component={CreateProfileRoute} />
        <Route exact={true} path="/setup/profiles/edit/:profileId" component={EditProfileRoute} />

        <Route exact={true} path="/setup/goals/list" component={GoalListRoute} />
        <Route exact={true} path="/setup/goals/create" component={GoalCreateRoute} />
        <Route exact={true} path="/setup/goals/edit/:goalId" component={GoalEditRoute} />


        <Route exact={true} path="/setup/products-services/list" component={ProductsServicesRoute} />
        <Route exact={true} path="/setup/products-services/create" component={CreateProductsServicesRoute} />
        <Route exact={true} path="/setup/products-services/edit/:productId" component={EditProductsServicesRoute} />

        <Route exact={true} path="/setup/integrations/lead-capture-forms/list" component={LeadCaptureFormsListRoute}/>
        <Route exact={true} path="/setup/integrations/lead-capture-forms/create" component={LeadCaptureFormsCreateRoute} />
        <Route exact={true} path="/setup/integrations/lead-capture-forms/edit/:formId" component={LeadCaptureFormsEditRoute} />

        <Route exact={true} path="/setup/integrations/api-keys/list" component={ApiKeysListRoute} />

        <Route exact={true} path="/setup/integrations/api-usage" component={ApiUsageRoute} />

        <Route exact={true} path="/setup/workflow-automation/workflows/list" component={WorkflowsListRoute}/>
        <Route exact={true} path="/setup/workflow-automation/workflows/templates/:entity" component={WorkflowTemplateRoute}/>
        <Route exact={true} path="/setup/workflow-automation/workflows/create" component={WorkflowsCreateRoute}/>
        <Route exact={true} path="/setup/workflow-automation/workflows/details/:workflowId" component={WorkflowViewRoute}/>
        <Route exact={true} path="/setup/workflow-automation/workflows/edit/:workflowId" component={WorkflowEditRoute}/>
        <Route exact={true} path="/setup/workflow-automation/execution-logs/list" component={WorkflowActionLogsListRoute} />
        <Route exact={true} path="/setup/workflow-automation/scheduled-jobs/list" component={ScheduledJobsListRoute}/>

        <Route exact={true} path="/setup/automation/scoring-rules/:entity/list" component={ScoringRulesListRoute}/>
        <Route exact={true} path="/setup/automation/scoring-rules/:entity/create" component={ScoringRulesCreateRoute}/>
        <Route exact={true} path="/setup/automation/scoring-rules/:entity/edit/:id" component={ScoringRulesEditRoute}/>

        <Route exact={true} path="/setup/integrations/webhooks/list" component={WebhooksListRoute}/>
        <Route exact={true} path="/setup/integrations/webhook-history/list" component={WebhookHistoryListRoute}/>

        <Route exact={true} path="/setup/billing/usage-statistics" component={BillingUsageRoute}/>
        <Route exact={true} path="/setup/billing/subscription-details" component={BillingSubscriptionDetailsRoute}/>

        <Route exact={true} path="/setup/communication/email-settings" component={EmailSettingsRoute}/>

        <Route exact={true} path="/setup/communication/gmail-oauth-callback" component={emailLoginPopUpRoute}/>

        <Route exact={true} path="/setup/communication/outlook-oauth-callback" component={emailLoginPopUpRoute}/>

        <Route exact={true} path="/setup/communication/email-templates/list" component={EmailTemplatesRoute}/>

        <Route exact={true} path="/setup/communication/email-templates/create" component={CreateEmailTemplateRoute}/>
        <Route exact={true} path="/setup/communication/email-templates/edit/:templateId" component={EditEmailTemplateRoute}/>

        <Route exact={true} path="/setup/communication/calendar-settings" component={CalendarSettingsRoute}/>
        <Route exact={true} path="/setup/communication/google-calendar-oauth-callback" component={emailLoginPopUpRoute}/>
        <Route exact={true} path="/setup/communication/microsoft-calendar-oauth-callback" component={emailLoginPopUpRoute}/>

        <Route exact={true} path="/setup/whatsapp-business/account-details" component={WhatsAppSettingsRoute}/>
        <Route exact={true} path="/setup/whatsapp-business/account-details/:accountId/add-agents/:entity" component={WhatsAppAddAgentsRoute}/>
        <Route exact={true} path="/setup/whatsapp-business/account-details/:accountId/map-fields/:entity" component={WhatsAppFieldMappingRoute}/>
        <Route exact={true} path="/setup/whatsapp-business/track-usage" component={WhatsAppBillingInformationRoute} />

        <Route exact={true} path="/setup/whatsapp-business/templates/create" component={CreateWhatsAppTemplateRoute}/>
        <Route exact={true} path="/setup/whatsapp-business/templates/edit/:templateId" component={EditWhatsAppTemplateRoute}/>
        <Route exact={true} path="/setup/whatsapp-business/templates/:templateId/variable-mapping" component={VariableMappingRoute}/>
        <Route exact={true} path="/setup/whatsapp-business/templates/list" component={WhatsAppTemplatesListRoute}/>

        <Route exact={true} path="/marketplace" component={MarketplaceAllAppsRoute} />
        <Route exact={true} path="/marketplace/all-apps/app-details/:appId" component={MarketplaceAppDetailsRoute} />

        <Route exact={true} path="/marketplace/installed-apps/list" component={MarketplaceInstalledAppsRoute} />
        <Route path="/marketplace/installed-apps/:appId" component={MarketplaceInstalledAppsRoute} />

        <Route exact={true} path="/marketplace/manage-my-apps/list" component={MarketplaceManageAppsRoute} />
        <Route exact={true} path="/marketplace/manage-my-apps/create" component={MarketplaceCreateAppRoute} />
        <Route exact={true} path="/marketplace/manage-my-apps/edit/:appId" component={MarketplaceEditAppRoute} />

        <Route exact={true} path="/setup/layouts/:entity/list" component={LayoutsListRoute} />
        <Route exact={true} path="/setup/layouts/:entity/create" component={LayoutsCreateRoute} />
        <Route exact={true} path="/setup/layouts/:entity/edit/:layoutId" component={LayoutsEditRoute} />

        <Route exact={true} path="/setup/conversion-mapping" component={ConversionMappingRoute} />

        <Route exact={true} path="/setup/account-management/currencies/list" component={CurrenciesListRoute} />

        <Route exact={true} path="/setup/account-management/exchange-rates-history/list" component={ExchangeRateHistoryListRoute} />
        <Route exact={true} path="/setup/account-management/exchange-rates/list" component={ExchangeRatesRoute} />

        <Route exact={true} path="/setup/field-sales/executives/list" component={FieldExecutivesListRoute} />
        <Route exact={true} path="/setup/field-sales/track/live-location" component={LiveLocationTrackingRoute} />
        <Route exact={true} path="/setup/field-sales/track/timeline" component={FieldExecutiveTimelineRoute} />
        <Route exact={true} path="/setup/field-sales/configurations" component={FieldSalesConfigurationsRoute} />

        <Route exact={true} path="/setup/shifts/list" component={UserShiftListRoute} />
        <Route exact={true} path="/setup/shifts/create" component={UserShiftCreateRoute} />
        <Route exact={true} path="/setup/shifts/edit/:shiftId" component={UserShiftEditRoute} />

        <Route exact={true} path="/setup/ai/email-assistant" component={EmailAssistantSettingsRoute} />
        <Route exact={true} path="/setup/ai/call-intelligence/transcript-summary" component={CallTranscriptSummaryRoute} />
        <Route exact={true} path="/setup/ai/note-analysis" component={SmartTaskCreatorRoute} />

        <Route render={props => <ErrorPage onClickHome = {() => props.history.push('/')} error={{ errorMessage: ErrorType.NOT_FOUND, errorCode: 404 }}/>} />
      </Switch>
    );
  }
}

export default Routes;

import { LayoutType } from '../components/page/Layouts/model';
import { FieldTypes } from '../components/page/FieldSettings/models/Field';

export const INPUT_TYPE_DERIVED_FROM_FIELD = 'INPUT_TYPE_DERIVED_FROM_FIELD';
export const NO_INPUT_FIELD_REQUIRED = 'NO_INPUT_FIELD_REQUIRED';
export const READ_ONLY_RELATIVE_FILTER = 'READ_ONLY_RELATIVE_FILTER';
export const VALUE = 'value';
export const OPERATOR = 'operator';

const FILTER_OPERATORS = {
  equal: {
    displayName: 'Equals',
    internalName: 'equal',
    inputType: INPUT_TYPE_DERIVED_FROM_FIELD
  },
  not_equal: {
    displayName: 'Not Equals',
    internalName: 'not_equal',
    inputType: INPUT_TYPE_DERIVED_FROM_FIELD
  },
  greater: {
    displayName: 'Greater Than',
    internalName: 'greater',
    inputType: INPUT_TYPE_DERIVED_FROM_FIELD
  },
  greater_or_equal:
  {
    displayName: 'Greater Or Equals',
    internalName: 'greater_or_equal',
    inputType: INPUT_TYPE_DERIVED_FROM_FIELD
  },
  less: {
    displayName: 'Less Than',
    internalName: 'less',
    inputType: INPUT_TYPE_DERIVED_FROM_FIELD
  },
  less_or_equal: {
    displayName: 'Less Or Equals',
    internalName: 'less_or_equal',
    inputType: INPUT_TYPE_DERIVED_FROM_FIELD
  },
  between: {
    displayName: 'Between',
    internalName: 'between',
    inputType: INPUT_TYPE_DERIVED_FROM_FIELD
  },
  not_between: {
    displayName: 'Not Between',
    internalName: 'not_between',
    inputType: INPUT_TYPE_DERIVED_FROM_FIELD
  },
  in: {
    displayName: 'In',
    internalName: 'in',
    inputType: INPUT_TYPE_DERIVED_FROM_FIELD
  },
  not_in: {
    displayName: 'Not In',
    internalName: 'not_in',
    inputType: INPUT_TYPE_DERIVED_FROM_FIELD
  },
  is_not_null: {
    displayName: 'Is Set',
    internalName: 'is_not_null',
    inputType: NO_INPUT_FIELD_REQUIRED
  },
  is_null: {
    displayName: 'Is Not Set',
    internalName: 'is_null',
    inputType: NO_INPUT_FIELD_REQUIRED
  },
  contains: {
    displayName: 'Contains',
    internalName: 'contains',
    inputType: INPUT_TYPE_DERIVED_FROM_FIELD
  },
  not_contains: {
    displayName: 'Not Contains',
    internalName: 'not_contains',
    inputType: INPUT_TYPE_DERIVED_FROM_FIELD
  },
  begins_with: {
    displayName: 'Begins With',
    internalName: 'begins_with',
    inputType: INPUT_TYPE_DERIVED_FROM_FIELD
  },
  is_empty: {
    displayName: 'is Empty',
    internalName: 'is_empty',
    inputType: NO_INPUT_FIELD_REQUIRED
  },
  is_not_empty: {
    displayName: 'is Not Empty',
    internalName: 'is_not_empty',
    inputType: NO_INPUT_FIELD_REQUIRED
  },
  today: {
    displayName: 'Today',
    internalName: 'today',
    inputType: READ_ONLY_RELATIVE_FILTER
  },
  yesterday: {
    displayName: 'Yesterday',
    internalName: 'yesterday',
    inputType: READ_ONLY_RELATIVE_FILTER
  },
  tomorrow: {
    displayName: 'Tomorrow',
    internalName: 'tomorrow',
    inputType: READ_ONLY_RELATIVE_FILTER
  },
  last_seven_days: {
    displayName: 'Last 7 Days',
    internalName: 'last_seven_days',
    inputType: READ_ONLY_RELATIVE_FILTER
  },
  next_seven_days: {
    displayName: 'Next 7 Days',
    internalName: 'next_seven_days',
    inputType: READ_ONLY_RELATIVE_FILTER
  },
  last_fifteen_days: {
    displayName: 'Last 15 Days',
    internalName: 'last_fifteen_days',
    inputType: READ_ONLY_RELATIVE_FILTER
  },
  next_fifteen_days: {
    displayName: 'Next 15 Days',
    internalName: 'next_fifteen_days',
    inputType: READ_ONLY_RELATIVE_FILTER
  },
  next_thirty_days: {
    displayName: 'Next 30 Days',
    internalName: 'next_thirty_days',
    inputType: READ_ONLY_RELATIVE_FILTER
  },
  last_thirty_days: {
    displayName: 'Last 30 Days',
    internalName: 'last_thirty_days',
    inputType: READ_ONLY_RELATIVE_FILTER
  },
  week_to_date: {
    displayName: 'Week to Date',
    internalName: 'week_to_date',
    inputType: READ_ONLY_RELATIVE_FILTER
  },
  current_week: {
    displayName: 'Current Week',
    internalName: 'current_week',
    inputType: READ_ONLY_RELATIVE_FILTER
  },
  last_week: {
    displayName: 'Last Week',
    internalName: 'last_week',
    inputType: READ_ONLY_RELATIVE_FILTER
  },
  next_week: {
    displayName: 'Next Week',
    internalName: 'next_week',
    inputType: READ_ONLY_RELATIVE_FILTER
  },
  month_to_date: {
    displayName: 'Month to Date',
    internalName: 'month_to_date',
    inputType: READ_ONLY_RELATIVE_FILTER
  },
  current_month: {
    displayName: 'Current Month',
    internalName: 'current_month',
    inputType: READ_ONLY_RELATIVE_FILTER
  },
  next_month: {
    displayName: 'Next Month',
    internalName: 'next_month',
    inputType: READ_ONLY_RELATIVE_FILTER
  },
  last_month: {
    displayName: 'Last Month',
    internalName: 'last_month',
    inputType: READ_ONLY_RELATIVE_FILTER
  },
  quarter_to_date: {
    displayName: 'Quarter to Date',
    internalName: 'quarter_to_date',
    inputType: READ_ONLY_RELATIVE_FILTER
  },
  current_quarter: {
    displayName: 'Current Quarter',
    internalName: 'current_quarter',
    inputType: READ_ONLY_RELATIVE_FILTER
  },
  last_quarter: {
    displayName: 'Last Quarter',
    internalName: 'last_quarter',
    inputType: READ_ONLY_RELATIVE_FILTER
  },
  next_quarter: {
    displayName: 'Next Quarter',
    internalName: 'next_quarter',
    inputType: READ_ONLY_RELATIVE_FILTER
  },
  year_to_date: {
    displayName: 'Year to Date',
    internalName: 'year_to_date',
    inputType: READ_ONLY_RELATIVE_FILTER
  },
  current_year: {
    displayName: 'Current Year',
    internalName: 'current_year',
    inputType: READ_ONLY_RELATIVE_FILTER
  },
  next_year: {
    displayName: 'Next Year',
    internalName: 'next_year',
    inputType: READ_ONLY_RELATIVE_FILTER
  },
  last_year: {
    displayName: 'Last Year',
    internalName: 'last_year',
    inputType: READ_ONLY_RELATIVE_FILTER
  },
  before_current_date_and_time: {
    displayName: 'Before Current Date & Time',
    internalName: 'before_current_date_and_time',
    inputType: READ_ONLY_RELATIVE_FILTER
  },
  after_current_date_and_time: {
    displayName: 'After Current Date & Time',
    internalName: 'after_current_date_and_time',
    inputType: READ_ONLY_RELATIVE_FILTER
  }
};

export const entitiesWithRelativeDateTimeOperators = [
  'leads', 'deals', 'contacts', 'companies', 'emails', 'tasks',
  'whatsapp-templates'
];

export const relativeDateTimeOperators = [
  'today',
  'yesterday',
  'tomorrow',
  'last_seven_days',
  'next_seven_days',
  'last_fifteen_days',
  'next_fifteen_days',
  'last_thirty_days',
  'next_thirty_days',
  'week_to_date',
  'current_week',
  'last_week',
  'next_week',
  'month_to_date',
  'current_month',
  'last_month',
  'next_month',
  'quarter_to_date',
  'current_quarter',
  'last_quarter',
  'next_quarter',
  'year_to_date',
  'current_year',
  'last_year',
  'next_year',
  'before_current_date_and_time',
  'after_current_date_and_time'
];

const permissionActions = {
  write: 'Create',
  read: 'Read',
  update: 'Update',
  delete: 'Delete',
  call: 'Call',
  email: 'Email',
  task: 'Task',
  note: 'Note',
  sms: 'Conversation',
  quotation: 'Quotation',
  document: 'Document',
  meeting: 'Meeting',
  readAll: 'Read All',
  updateAll: 'Update All',
  deleteAll: 'Delete All'
};

const sortOrders = {
  asc: 'Ascending',
  desc: 'Descending'
};

export const getUnsupportedFilterFieldTypes = (entity) =>{
  const unsupportedFilterFieldTypes = [
    FieldTypes.TIME_PICKER,
    FieldTypes.CHECKBOX,
    FieldTypes.RADIO_BUTTON,
    FieldTypes.PARAGRAPH_TEXT,
    FieldTypes.RICH_TEXT
  ];

  if(![entities.LEADS, entities.CONTACTS, entities.COMPANIES].includes(entity)){
    unsupportedFilterFieldTypes.push(FieldTypes.PHONE);
    unsupportedFilterFieldTypes.push(FieldTypes.EMAIL);
  }

  return unsupportedFilterFieldTypes;
};

const sortableFieldTypes = [
  FieldTypes.PICK_LIST,
  FieldTypes.NUMBER,
  FieldTypes.DATE_PICKER,
  FieldTypes.TIME_PICKER,
  FieldTypes.DATETIME_PICKER,
  FieldTypes.AUTO_INCREMENT,
  FieldTypes.TEXT_FIELD,
  FieldTypes.MONEY,
  FieldTypes.DISCOUNT
];

const unsupportedFilterFields = [
  'userCount'
];

export {
  FILTER_OPERATORS,
  permissionActions,
  sortOrders,
  unsupportedFilterFields,
  sortableFieldTypes
};

export const entitiesWithInNotInOperatorsForLookUpAndPicklist  = [
  'leads',
  'deals',
  'contacts',
  'companies',
  'quotations',
  'meetings',
  'tasks',
  'calls',
  'emails',
  'reports',
  'webhooks',
  'webhook-events',
  'documents',
  'bulk-jobs',
  'products-services',
  'exchangeRate',
  'sharing',
  'whatsapp-templates',
  'notes',
  'scoring-rules'
];

export enum Products { SALES = 'Sales',  SETUP = 'Setup', MARKETPLACE = 'Marketplace' }

export const leadFields = {
  PRODUCTS: 'products',
  LATEST_NOTES: 'latestNotes'
};

export const contactFields = {
  ASSOCIATED_DEALS: 'associatedDeals'
};

export const entities = {
  LEADS: 'leads',
  DEALS: 'deals',
  CONTACTS: 'contacts',
  COMPANIES: 'companies',
  TASKS: 'tasks',
  NOTES: 'notes',
  REPORTS: 'reports',
  USER: 'users',
  LEAD_CAPTURE_FORMS: 'lead-capture-forms',
  MEETINGS: 'meetings',
  IMPORTS: 'imports',
  EMAILS: 'emails',
  EMAIL_TEMPLATES:'email_template',
  BILLING: 'billing',
  SEARCH_LIST: 'searchList',
  API_KEYS: 'api-keys',
  DASHBOARDS: 'dashboards',
  PRODUCT_SERVICES: 'products-services',
  CALLS: 'calls',
  SMS: 'sms',
  BULK_JOBS: 'bulk-jobs',
  EXPORTS: 'exports',
  SHARING: 'sharing',
  WEBHOOKS: 'webhooks',
  WEBHOOK_EVENTS: 'webhook-events',
  WORKFLOWS: 'workflows',
  WORKFLOW_ACTION_LOGS: 'workflow-action-logs',
  SCHEDULED_JOBS: 'scheduled-jobs',
  QUOTATIONS: 'quotations',
  DOCUMENTS: 'documents',
  FEEDS: 'feeds',
  ACCOUNT: 'accounts',
  LAYOUTS: 'layouts',
  PROFILES: 'profiles',
  GOALS: 'goals',
  CURRENCIES: 'currencies',
  IP_CONFIGURATIONS: 'ip-configurations',
  EXECUTIVES: 'executives',
  WHATSAPP_BUSINESS: 'whatsapp-business',
  SHIFTS: 'shifts',
  WHATSAPP_TEMPLATES: 'whatsapp-templates',
  SCORING_RULES: 'scoring-rules',
  AI: 'ai',
  GEOFENCE: 'geofence',
  FIELD_SALES: 'field-sales',
  WHATSAPP: 'whatsapp',
  CAMPAIGNS: 'campaigns',
  CAMPAIGN_ACTIVITIES: 'campaign-activities',
  CAMPAIGN_ACTIVITY_RECIPIENT_STATUS: 'campaign-activity-recipient-status'
};

export const billableEntities = ['leads',  'deals',  'contacts',  'companies',  'tasks',  'notes',  'meetings',  'calls', 'emails'];

export enum entitiesPlural {
  LEAD = 'leads',
  DEAL = 'deals',
  CONTACT = 'contacts',
  COMPANY = 'companies',
  TASK = 'tasks',
  NOTE = 'notes',
  EMAIL_TEMPLATE = 'email_templates'
}

export const formLayoutUrlFor = {
  dealCreate: '/deals/layouts/create/default',
  dealEdit: '/deals/layouts/edit/default',
  companyCreate: '/companies/layouts/create/default',
  companyEdit: '/companies/layouts/edit/default',
  quotationCreate: '/quotations/layout/create',
  quotationEdit: '/quotations/layout/edit',
  taskCreate: '/layouts/task/create',
  taskEdit: '/layouts/task/edit',
  contactCreate: '/layouts/contact/create',
  leadCreate: '/layouts/lead/create'
};

export const entitiesWithIdNameResolved = [entities.DEALS, entities.COMPANIES, entities.QUOTATIONS, entities.PRODUCT_SERVICES];

export const entitiesWithTenantSettingsApplicable = [entities.USER, entities.TASKS];

export const TABLE_RE_FETCH_TIMEOUT = 750;

export enum ReferrerFlow {
  NAV_BAR = 'Nav Bar',
  CONTROL_CENTER_DASHBOARD = 'Control Center Dashboard',
  CONTROL_CENTER_SIDE_PANEL = 'Control Center Side Panel',
  SALES_CRM_DASHBOARD = 'Sales CRM Dashboard',
  QUICK_ADD = 'Quick Add',
  QUICK_EDIT = 'Quick Edit',
  LEAD_DETAILS = 'Lead Details',
  CONTACT_DETAILS = 'Contact Details',
  TASK_DETAILS = 'Task Details',
  COMPANY_DETAILS = 'Company Details',
  DEAL_DETAILS = 'Deal Details',
  CONTACT_LIST = 'Contact List',
  COMPANY_LIST = 'Company List',
  LEAD_LIST = 'Lead List',
  DEAL_LIST = 'Deal List',
  IMPORT_DRAFT = 'Import Draft',
  MEETING_DETAILS = 'Meeting Details',
  GLOBAL_SEARCH = 'Global Search',
  CAMPAIGN_LIST = 'Campaign List',
  CAMPAIGN_FORM = 'Campaign Form',
  CAMPAIGN_DETAILS = 'Campaign Details',
  CAMPAIGN_ACTIVITY_LIST = 'CAMPAIGN_ACTIVITY_LIST'
}

export const forecastingTypeOptions = [
  {
    id: 'OPEN',
    name: 'OPEN',
    displayName: 'Open'
  },
  {
    id: 'CLOSED_WON',
    name: 'CLOSED_WON',
    displayName: 'Won'
  },
  {
    id: 'CLOSED_LOST',
    name: 'CLOSED_LOST',
    displayName: 'Lost'
  },
  {
    id: 'CLOSED_UNQUALIFIED',
    name: 'CLOSED_UNQUALIFIED',
    displayName: 'Unqualified'
  },
];

export const bulkActions = {
  REASSIGN: 'reassign',
  DELETE: 'delete',
  UPDATE: 'update',
  EMAIL: 'email',
  EXPORT: 'export',
  WHATSAPP: 'whatsapp',
  SETUP_GEOFENCE: 'setup_geofence'
};

export enum DropdownLocation {
  LEFT = 'LEFT',
  RIGHT = 'RIGHT'
}

export const DEAL_PRODUCT_SECTION_FIELDS = ['products', 'quantity', 'price', 'discount', 'units'];

export const DEAL_FIELDS_REQUIRED_FOR_PRODUCT = ['quantity', 'price', 'discount', 'units'];

export const DEAL_PRODUCTS_SECTION_NAME = 'Product or Services';

export enum CKEditorFeatures {
  TABLE = 'TABLE',
  IMAGE = 'IMAGE',
  STRIKETHROUGH = 'STRIKETHROUGH',
  SUBSCRIPT = 'SUBSCRIPT',
  SUPERSCRIPT = 'SUPERSCRIPT'
}

export const TRIAL_ELEVATE_FEATURE = ['goals'];

export const USD_BILLING_INTRODUCTION_DATE = '2023-07-06T14:30:00.000+0000';

export enum ListingViews {
  LIST = 'LIST',
  KANBAN = 'KANBAN',
  HIERARCHY= 'HIERARCHY',
  RULEBOARD = 'RULEBOARD'
}

export const MILLION = 1000000;
export const BILLION = 1000000000;
export const TRILLION = 1000000000000;

export const THOUSAND = 1000;
export const LAKH = 100000;
export const CRORE = 10000000;

export const NumberFormat = {
  INDIAN_NUMBER_FORMAT: 'INDIAN_NUMBER_FORMAT',
  INTERNATIONAL_NUMBER_FORMAT: 'INTERNATIONAL_NUMBER_FORMAT'
};

export const MAX_ENTITIES_ALLOWED_FOR_BULK_ACTION = 10000;

export const CANCELED_REQUEST_ERROR = 'ERR_CANCELED';

export const STANDARD = 'standard';
export const PREFERRED = 'preferred';

export const layoutUrls = {
  [entities.LEADS]: {
    [LayoutType.LIST.toLowerCase()]: {
      [STANDARD]: '/leads/layout/list',
      [PREFERRED]: '/layouts/lead/list'
    },
    [LayoutType.CREATE.toLowerCase()]: {
      [STANDARD]: '/ui/layouts/CREATE/LEAD'
    },
    [LayoutType.EDIT.toLowerCase()]: {
      [STANDARD]: '/ui/layouts/EDIT/LEAD',
      [PREFERRED]: '/layouts/lead/edit'
    },
    [LayoutType.DETAIL.toLowerCase()]: {
      [PREFERRED]: '/layouts/lead/detail'
    }
  },
  [entities.CONTACTS]: {
    [LayoutType.LIST.toLowerCase()]: {
      [STANDARD]: '/contacts/layout/list'
    },
    [LayoutType.CREATE.toLowerCase()]: {
      [STANDARD]: '/ui/layouts/CREATE/CONTACT'
    },
    [LayoutType.EDIT.toLowerCase()]: {
      [STANDARD]: '/ui/layouts/EDIT/CONTACT',
      [PREFERRED]: '/layouts/contact/edit'
    },
    [LayoutType.DETAIL.toLowerCase()]: {
      [PREFERRED]: '/layouts/contact/detail'
    }
  },
  [entities.DEALS]: {
    [LayoutType.LIST.toLowerCase()]: {
      [STANDARD]: '/deals/layout/list',
      [PREFERRED]: '/deals/layouts/list/default'
    },
    [LayoutType.EDIT.toLowerCase()]: {
      [STANDARD]: '/deals/layout?view=edit',
      [PREFERRED]: '/deals/layouts/edit/default'
    },
    [LayoutType.DETAIL.toLowerCase()]: {
      [PREFERRED]: '/deals/layouts/detail/default'
    }
  },
  [entities.COMPANIES]: {
    [LayoutType.LIST.toLowerCase()]: {
      [STANDARD]: '/companies/layout/list'
    },
    [LayoutType.EDIT.toLowerCase()]: {
      [STANDARD]: '/companies/layout?view=edit',
      [PREFERRED]: '/companies/layouts/edit/default'
    },
    [LayoutType.DETAIL.toLowerCase()]: {
      [PREFERRED]: '/companies/layouts/detail/default'
    }
  },
  [entities.MEETINGS]: {
    [LayoutType.LIST.toLowerCase()]: {
      [STANDARD]: '/meetings/layout/list'
    },
    [LayoutType.EDIT.toLowerCase()]: {
      [STANDARD]: '/meetings/layout?view=edit'
    }
  },
  [entities.EMAILS]: {
    [LayoutType.LIST.toLowerCase()]: {
      [STANDARD]: '/emails/layout/list'
    }
  },
  [entities.TASKS]: {
    [LayoutType.LIST.toLowerCase()]: {
      [STANDARD]: '/ui/layouts/list/task'
    },
    [LayoutType.CREATE.toLowerCase()]: {
      [STANDARD]: '/ui/layouts/CREATE/TASK',
      [PREFERRED]: '/layouts/task/create'
    },
    [LayoutType.EDIT.toLowerCase()]: {
      [STANDARD]: '/ui/layouts/EDIT/TASK',
      [PREFERRED]: '/layouts/task/edit'
    },
    [LayoutType.DETAIL.toLowerCase()]: {
      [PREFERRED]: '/layouts/task/detail'
    }
  },
  [entities.QUOTATIONS]: {
    [LayoutType.LIST.toLowerCase()]: {
      [STANDARD]: '/quotations/layout/list'
    },
    [LayoutType.EDIT.toLowerCase()]: {
      [STANDARD]: '/quotations/layout/edit'
    }
  },
  [entities.CALLS]: {
    [LayoutType.LIST.toLowerCase()]: {
      [STANDARD]: '/call-logs/layout/list'
    },
    [LayoutType.CREATE.toLowerCase()]: {
      [STANDARD]: '/call-logs/layout?view=create'
    },
    [LayoutType.EDIT.toLowerCase()]: {
      [STANDARD]: '/call-logs/layout?view=edit'
    }
  },
  [entities.USER]: {
    [LayoutType.LIST.toLowerCase()]: {
      [STANDARD]: '/ui/layouts/list/user'
    },
    [LayoutType.EDIT.toLowerCase()]: {
      [STANDARD]: '/ui/layouts/EDIT/USER'
    }
  },
  [entities.PRODUCT_SERVICES]: {
    [LayoutType.LIST.toLowerCase()]: {
      [STANDARD]: '/products/layout/list'
    },
    [LayoutType.EDIT.toLowerCase()]: {
      [STANDARD]: '/products/layout?view=edit'
    }
  },
  [entities.WORKFLOWS]: {
    [LayoutType.LIST.toLowerCase()]: {
      [STANDARD]: '/workflows/layout/list'
    }
  },
  [entities.WORKFLOW_ACTION_LOGS]: {
    [LayoutType.LIST.toLowerCase()]: {
      [STANDARD]: '/execution-logs/layout/list'
    }
  },
  [entities.SCHEDULED_JOBS]: {
    [LayoutType.LIST.toLowerCase()]: {
      [STANDARD]: '/scheduled-jobs/layout/list'
    }
  },
  [entities.SCORING_RULES]: {
    [LayoutType.LIST.toLowerCase()]: {
      [STANDARD]: '/score-rules/layout/list'
    }
  },
  [entities.WHATSAPP]: {
    [LayoutType.LIST.toLocaleLowerCase()]: {
      [STANDARD]: '/messages/whatsapp/layout/list'
    }
  }
};

export const ACTIVE = true;
export const INACTIVE = false;

export const TRIAL_ADDON = {
  goals: 'goalTrialRemindLater',
  fieldSales: 'fieldSalesTrailRemindLater'
};

export const entitiesWithHierarchyView = [entities.USER];
export const entitiesWithKanbanView = [entities.LEADS, entities.DEALS];
export const entitiesWithBoardView = [entities.SCORING_RULES];

export const MONEY_FIELD_DECIMAL_SCALE = 6;

export const propertiesToStoreInIndexedDb = {
  layoutCache: 'entityType',
  dashboard: 'userId',
  user: 'user'
};

export const indexedDbVersion = 4;

export const BETA_FEATURES = ['ai'];

export const productStandardFieldsToShowOnDetailsPopUp = ['category', 'hsnSacCode', 'countryOfOrigin'];

export const commonInternalErrorCode = '000000';

export const TENANT_IDS_FOR_VIASOCKET = [691, 2549, 2620];

export const supportedFilterOperatorsByEntity = {
  [entities.EMAILS]: {
    subject: [
      FILTER_OPERATORS.equal.internalName,
      FILTER_OPERATORS.not_equal.internalName,
      FILTER_OPERATORS.contains.internalName,
      FILTER_OPERATORS.not_contains.internalName,
      FILTER_OPERATORS.in.internalName,
      FILTER_OPERATORS.not_in.internalName,
      FILTER_OPERATORS.begins_with.internalName
    ],
    sentBy: [
      FILTER_OPERATORS.equal.internalName,
      FILTER_OPERATORS.in.internalName
    ],
    receivedBy: [
      FILTER_OPERATORS.equal.internalName,
      FILTER_OPERATORS.in.internalName
    ],
    user: [
      FILTER_OPERATORS.equal.internalName,
      FILTER_OPERATORS.in.internalName
    ],
    associatedLeads: [
      FILTER_OPERATORS.equal.internalName,
      FILTER_OPERATORS.is_null.internalName,
      FILTER_OPERATORS.is_not_null.internalName,
      FILTER_OPERATORS.in.internalName
    ],
    associatedDeals: [
      FILTER_OPERATORS.equal.internalName,
      FILTER_OPERATORS.is_null.internalName,
      FILTER_OPERATORS.is_not_null.internalName,
      FILTER_OPERATORS.in.internalName
    ],
    associatedContacts: [
      FILTER_OPERATORS.equal.internalName,
      FILTER_OPERATORS.is_null.internalName,
      FILTER_OPERATORS.is_not_null.internalName,
      FILTER_OPERATORS.in.internalName
    ]
  },
  [entities.WHATSAPP]: {
    direction: [
      FILTER_OPERATORS.equal.internalName,
      FILTER_OPERATORS.not_equal.internalName
    ],
    messageBody: [
      FILTER_OPERATORS.equal.internalName,
      FILTER_OPERATORS.not_equal.internalName,
      FILTER_OPERATORS.contains.internalName,
      FILTER_OPERATORS.not_contains.internalName
    ]
  },
  [entities.WORKFLOW_ACTION_LOGS]: {
    workflowId: [
      FILTER_OPERATORS.equal.internalName,
      FILTER_OPERATORS.not_equal.internalName,
      FILTER_OPERATORS.in.internalName,
      FILTER_OPERATORS.not_in.internalName
    ],
    entityId: [
      FILTER_OPERATORS.equal.internalName,
      FILTER_OPERATORS.not_equal.internalName,
      FILTER_OPERATORS.in.internalName,
      FILTER_OPERATORS.not_in.internalName
    ],
    status: [
      FILTER_OPERATORS.equal.internalName,
      FILTER_OPERATORS.not_equal.internalName
    ],
    workflowAction: [
      FILTER_OPERATORS.equal.internalName,
      FILTER_OPERATORS.not_equal.internalName
    ]
  }
};

import { DELETE, MEETING, READ, READ_ALL, UPDATE, UPDATE_ALL, WRITE } from './permissionUtil';

/**
 *
 * route permissions are:
 * [[{entityPermission1, {entityPermission2}, ...][{entityPermissionA, {entityPermissionB},...]....]
 * AND operator will be applied on inner array and OR operator on outer array
 * so it is equivalent to:
 * [[{entityPermission1 AND {entityPermission2} AND ...] OR [{entityPermissionA AND {entityPermissionB} AND ...]....]
 *
 */

const routePermissions = {
  TenantUpdatePermission: [[{ entity: 'tenants', requiredPermissions: [UPDATE] }]],
  TenantReadPermission: [[{ entity: 'tenants', requiredPermissions: [READ] }]],
  ProfileListRoute: [[{ entity: 'profiles' , requiredPermissions:[READ] }]],
  CreateProfileRoute: [[{ entity: 'profiles' , requiredPermissions:[WRITE] }]],
  EditProfileRoute: [[{ entity: 'profiles' , requiredPermissions:[READ, UPDATE] }]],
  UserListRoute: [[{ entity: 'users', requiredPermissions:[READ] }]],
  UserCreateRoute: [[{ entity: 'users', requiredPermissions:[WRITE] }]],
  UserEditRoute: [[{ entity: 'users', requiredPermissions:[UPDATE] }]],
  TeamListRoute: [[{ entity: 'teams', requiredPermissions:[READ] }]],
  TeamCreateRoute: [[{ entity: 'teams', requiredPermissions:[WRITE] }]],
  TeamEditRoute: [[{ entity: 'teams', requiredPermissions:[UPDATE] }]],
  TaskListRoute: [[{ entity: 'tasks', requiredPermissions:[READ] }]],
  CallsListRoute: [[{ entity: 'call', requiredPermissions:[READ] }]],
  WhatsAppListRoute: [[{ entity: 'sms', requiredPermissions:[READ] }]],
  AssignUserRoute: [[{ entity: 'users', requiredPermissions:[READ] }]],
  TaskSettingRoute:[[{ entity: 'config', requiredPermissions:[READ] }]],
  ModuleCustomizationRoute:[[{ entity: 'config', requiredPermissions:[READ] }]],
  FieldsListingRoute: [[{ entity: 'customField', requiredPermissions:[READ] }]],
  FieldWizardFormRoute: [[{ entity: 'customField', requiredPermissions:[WRITE] }]],
  FieldsEditFormRoute: [[{ entity: 'customField', requiredPermissions:[READ, UPDATE] }]],
  SharingListLayoutRoute: [[{ entity: 'sharing', requiredPermissions:[READ] }]],
  SharingCreateLayoutRoute: [[{ entity: 'sharing', requiredPermissions:[READ, WRITE] }]],
  SharingEditLayoutRoute: [[{ entity: 'sharing', requiredPermissions:[READ, UPDATE] }]],
  ProductsServicesRoute: [[{ entity: 'products-services', requiredPermissions:[READ] }]],
  CreateProductsServicesRoute:[[{ entity: 'products-services', requiredPermissions:[WRITE] }]],
  EditProductsServicesRoute: [[{ entity: 'products-services', requiredPermissions:[UPDATE] }]],
  SetupRoute: [
    [{ entity: 'user', requiredPermissions:[READ] }],
    [{ entity: 'team', requiredPermissions:[READ] }],
    [{ entity: 'profiles', requiredPermissions:[READ] }],
    [{ entity: 'config', requiredPermissions:[READ] }],
    [{ entity: 'customField', requiredPermissions:[READ] }],
    [{ entity: 'pipeline', requiredPermissions:[READ] }],
    [{ entity: 'tenant', requiredPermissions:[READ] }],
    [{ entity: 'sharing', requiredPermissions:[READ] }],
    [{ entity: 'products-services', requiredPermissions:[READ] }],
    [{ entity: 'lead-capture-forms', requiredPermissions:[READ] }]
  ],
  DataImportRoute: [
    [{ entity: 'lead', requiredPermissions:[WRITE] }],
    [{ entity: 'contact', requiredPermissions:[WRITE] }],
    [{ entity: 'company', requiredPermissions:[WRITE] }]
  ],
  PipelineListRoute: [[{ entity: 'pipeline', requiredPermissions:[READ] }]],
  PipelineLayoutRoute: [[{ entity: 'pipeline', requiredPermissions:[UPDATE] }]],
  DealListRoute: [[{ entity: 'deal', requiredPermissions:[READ] }]],
  DealDetailsRoute: [[{ entity: 'deal', requiredPermissions:[READ] }]],
  CompanyListRoute: [[{ entity: 'company', requiredPermissions:[READ] }]],
  CompanyDetailsRoute: [[{ entity: 'company', requiredPermissions:[READ] }]],
  LeadListRoute: [[{ entity: 'lead', requiredPermissions:[READ] }]],
  LeadDetailsRoute: [[{ entity: 'lead', requiredPermissions:[READ] }]],
  ContactListRoute: [[{ entity: 'contact', requiredPermissions:[READ] }]],
  ContactDetailsRoute: [[{ entity: 'contact', requiredPermissions:[READ] }]],
  ReportListRoute: [[{ entity: 'report', requiredPermissions:[READ] }]],
  ReportCreateRoute: [[{ entity: 'report', requiredPermissions:[WRITE] }]],
  ReportEditRoute: [[{ entity: 'report', requiredPermissions:[UPDATE] }]],
  ReportDetailsRoute: [[{ entity: 'report', requiredPermissions:[READ] }]],
  GoalListRoute: [[{ entity: 'goal' , requiredPermissions:[READ] }]],
  EmailListRoute: [[{ entity: 'email', requiredPermissions:[READ] }]],
  GoalCreateRoute: [[{ entity: 'goal' , requiredPermissions:[WRITE] }]],
  GoalEditRoute: [[{ entity: 'goal' , requiredPermissions:[READ] }]],
  LeadCaptureFormsListRoute: [[{ entity: 'lead-capture-forms', requiredPermissions: [READ] }]],
  LeadCaptureFormsCreateRoute: [[{ entity: 'lead-capture-forms', requiredPermissions: [WRITE] }]],
  LeadCaptureFormsEditRoute: [[{ entity: 'lead-capture-forms', requiredPermissions: [UPDATE] }]],
  MeetingListRoute: [[{ entity: 'meeting', requiredPermissions: [READ] }]],
  WorkflowListRoute: [[{ entity: 'workflow', requiredPermissions: [READ] }]],
  WorkflowTemplateRoute: [[{ entity: 'workflow', requiredPermissions: [WRITE] }]],
  WorkflowCreateRoute: [[{ entity: 'workflow', requiredPermissions: [WRITE] }]],
  WorkflowViewRoute: [[{ entity: 'workflow', requiredPermissions: [READ] }]],
  WorkflowEditRoute: [[{ entity: 'workflow', requiredPermissions: [UPDATE] }]],
  WorkflowActionLogsListRoute: [[{ entity: 'workflow', requiredPermissions: [READ_ALL] }]],
  ScheduledJobsListRoute: [[{ entity: 'workflow', requiredPermissions: [READ_ALL] }]],
  ScoringRulesListRoute: [[{ entity: 'scoringRules', requiredPermissions: [READ_ALL] }]],
  ScoringRulesCreateRoute: [[{ entity: 'scoringRules', requiredPermissions: [WRITE] }]],
  ScoringRulesEditRoute: [[{ entity: 'scoringRules', requiredPermissions: [READ] }]],
  ExportListRoute: [[{ entity: 'exports', requiredPermissions: [READ] }]],
  ExportCreateRoute: [[{ entity: 'exports', requiredPermissions: [WRITE] }]],
  WebhookListRoute: [[{ entity: 'webhooks', requiredPermissions: [READ] }]],
  QuotationsListRoute: [[{ entity: 'quotations', requiredPermissions: [READ] }]],
  QuotationsDetailsRoute: [[{ entity: 'quotations', requiredPermissions: [READ] }]],
  CalendarSettingsRoute: [
    [{ entity: 'lead', requiredPermissions:[MEETING] }],
    [{ entity: 'deal', requiredPermissions:[MEETING] }],
    [{ entity: 'contact', requiredPermissions:[MEETING] }]
  ],
  LayoutsListRoute: [[{ entity: 'layout', requiredPermissions: [READ_ALL] }]],
  LayoutsCreateRoute: [[{ entity: 'layout', requiredPermissions: [WRITE] }]],
  LayoutsEditRoute: [[{ entity: 'layout', requiredPermissions: [UPDATE_ALL] }]],

  ConversionMappingRoute: [[{ entity: 'conversionMapping', requiredPermissions: [READ_ALL] }]],

  CurrenciesListRoute: [[{ entity: 'currencies', requiredPermissions: [READ_ALL] }]],
  ExchangeRateHistoryListRoute: [[{ entity: 'currencies', requiredPermissions: [READ_ALL] }]],
  ExchangeRatesRoute: [[{ entity: 'currencies', requiredPermissions: [READ_ALL] }]],
  IPConfigurationsListRoute: [[{ entity: 'security', requiredPermissions: [READ_ALL] }]],
  IPConfigurationsCreateRoute: [[{ entity: 'security', requiredPermissions: [WRITE] }]],
  IPConfigurationsEditRoute: [[{ entity: 'security', requiredPermissions: [READ_ALL] }]],
  TwoFactorAuthenticationRoute: [[{ entity: 'security', requiredPermissions: [READ_ALL] }]],
  FieldExecutivesListRoute: [[{ entity: 'fieldSales', requiredPermissions: [READ] }]],
  LiveLocationTrackingRoute: [[{ entity: 'fieldSales', requiredPermissions: [READ] }]],
  FieldExecutiveTimelineRoute: [[{ entity: 'fieldSales', requiredPermissions: [READ] }]],
  FieldSalesConfigurationsRoute: [[{ entity: 'fieldSales', requiredPermissions: [UPDATE_ALL] }]],
  WhatsAppSettingsRoute: [[{ entity: 'whatsappBusiness', requiredPermissions: [READ_ALL] }]],
  WhatsAppTemplatesListRoute: [[{ entity: 'whatsappTemplates', requiredPermissions: [READ] }]],
  CreateWhatsAppTemplateRoute: [[{ entity: 'whatsappTemplates', requiredPermissions: [WRITE] }]],
  EditWhatsAppTemplateRoute: [[{ entity: 'whatsappTemplates', requiredPermissions: [UPDATE] }]],
  UserShiftListRoute: [[{ entity: 'shift', requiredPermissions: [READ_ALL] }]],
  UserShiftCreateRoute: [[{ entity: 'shift', requiredPermissions: [WRITE] }]],
  UserShiftEditRoute: [[{ entity: 'shift', requiredPermissions: [READ_ALL] }]],
  CreateBulkJobRoute: [[{ entity: 'note', requiredPermissions: [DELETE] }]],
  EmailTemplatesListRoute: [[{ entity: 'email_templates', requiredPermissions: [READ] }]],
  EmailTemplatesCreateRoute: [[{ entity: 'email_templates', requiredPermissions: [WRITE] }]],
  EmailTemplatesEditRoute: [[{ entity: 'email_templates', requiredPermissions: [READ] }]],
  AIPermission: [[{ entity: 'ai', requiredPermissions: [READ_ALL] }]],
  CampaignListRoute: [[{ entity: 'campaigns', requiredPermissions: [READ] }]],
  CampaignCreateRoute: [[{ entity: 'campaigns', requiredPermissions: [WRITE] }]],
  CampaignEditRoute: [[{ entity: 'campaigns', requiredPermissions: [UPDATE] }]],
  CampaignDetailsRoute: [[{ entity: 'campaigns', requiredPermissions: [READ] }]],
  CampaignActivityListRoute: [[{ entity: 'campaigns', requiredPermissions: [READ] }]],
  CampaignActivityCreateRoute: [[{ entity: 'campaigns', requiredPermissions: [WRITE] }]],
  CampaignActivityEditRoute: [[{ entity: 'campaigns', requiredPermissions: [UPDATE] }]],
  CampaignActivityRecipientStatusList: [[{ entity: 'campaigns', requiredPermissions: [READ] }]]
};

export { routePermissions };
